import json
import pandas as pd
import requests
import re
import threading
import queue
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app
from typing import Union, List, Dict

# 创建线程安全的队列
batch_queue = queue.Queue()
# 创建线程锁
db_lock = threading.Lock()


def check_lymph_nodes():
    """
    主任务函数：提取所有类型癌症的淋巴结清扫数量并更新数据库
    处理流程：
        1. 查询所有LYMPH_NODE_COUNT为空的记录
        2. 使用多线程提取淋巴结清扫数量
        3. 每批完成后立即更新数据库
    """
    try:
        # 步骤1: 查询所有LYMPH_NODE_COUNT为空的记录
        sql = """
        SELECT ID, TREATMENT_CODE, CHECK_RESULT
        FROM ai.test_lymph_specification
        WHERE LYMPH_NODE_COUNT IS NULL
        """

        # 执行查询
        records_df = db(None, None, sql, 'mysql')  # 假设使用MySQL连接

        if records_df.empty:
            print("未找到LYMPH_NODE_COUNT为空的记录")
            return

        print(f"找到{len(records_df)}条需要处理的记录")

        batch_size = 20  # 每批20条记录
        total_rows = len(records_df)

        # 准备批处理数据并放入队列
        for start in range(0, total_rows, batch_size):
            end = min(start + batch_size, total_rows)
            batch_df = records_df.iloc[start:end]
            batch_queue.put(batch_df)

        # 创建并启动工作线程
        num_workers = 20  # 20个工作线程
        threads = []
        for i in range(num_workers):
            t = threading.Thread(target=worker_thread, args=(i + 1,))
            t.daemon = True  # 设置为守护线程
            t.start()
            threads.append(t)

        # 等待所有批处理完成
        batch_queue.join()
        print("所有批次处理完成")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def worker_thread(worker_id):
    """
    工作线程函数：处理队列中的批次数据
    :param worker_id: 工作线程ID
    """
    print(f"工作线程 {worker_id} 启动")
    while True:
        try:
            batch_df = batch_queue.get()
            if batch_df is None:
                break

            print(f"工作线程 {worker_id} 处理 {len(batch_df)} 条记录")

            # 调用AI函数处理当前批次
            batch_result = extract_lymph_node_count(batch_df)

            if batch_result:
                # 使用线程锁确保数据库更新安全
                with db_lock:
                    update_lymph_node_counts(batch_result)
                    print(f"工作线程 {worker_id} 更新了 {len(batch_result)} 条记录")

            # 标记任务完成
            batch_queue.task_done()

        except Exception as e:
            print(f"工作线程 {worker_id} 处理批次时出错: {str(e)}")
            batch_queue.task_done()


def extract_lymph_node_count(records_df: DataFrame) -> List[Dict]:
    """
    使用AI提取淋巴结清扫数量（适用于所有癌症类型）
    :param records_df: 包含患者检查结果的DataFrame
    :return: 淋巴结数量列表 [{"id": 123, "lymph_node_count": 10}, ...]
    """
    # 构建AI提示词
    prompt = """
        以下病检报告涉及到了多少个数量的淋巴结：
        其中：
        检查报告CHECK_RESULT中如果有1/5或者0/6等等这样的描述代表5或者6个淋巴结，其中分子代表的是异常淋巴结个数，结果要加5或者6。
        要求：
        1. 返回传入的每行CHECK_RESULT涉及的淋巴结数量（整数）
        2. 传进去的每一行数据都要分析，不要从最后几行开始，我要所有的分析结果
        2. 每一行都要有结果，没有涉及淋巴结也要是返回0（整数）
          如： {ID:1,lymph_node_count:0}
        3. 返回的行数要和传进去的行数要相等，如果分批传入100条数据，要结果传出100条数据
        患者检查结果列表：
        """

    for _, row in records_df.iterrows():
        # 使用ID作为唯一标识
        prompt += fr"\n记录ID:{row['ID']}): 记录ID:{row['ID']}的检查结果{row['CHECK_RESULT']}"

    prompt += """
    输出要求：
    1. 每行一个记录ID和淋巴结数量
    2. 采用JSON格式返回
    3. 字段说明:
        id: 记录ID（整数）
        lymph_node_count: 淋巴结数量（整数）

    示例输出格式：
    [{"id": 1, "lymph_node_count": 15}, ...]

    请直接给出提取结果：
    """

    # 调用AI服务
    try:
        response = requests.post(
            "http://***************:12434/api/generate",
            json={
                "model": 'qwen3:8b-q4_K_M',
                "prompt": prompt,
                "stream": False,
            }
        )
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        print(f"AI服务调用失败: {str(e)}")
        # 返回空结果避免阻塞后续处理
        return []

    # 处理AI响应
    ai_output = response.json().get("response", "").strip()
    clean_response = re.sub(r'<think>.*?</think>', '', ai_output, flags=re.DOTALL).strip()
    # print(f"AI返回结果: {clean_response[:200]}...")  # 截断长输出
    return parse_ai_response(clean_response)


def parse_ai_response(response_text: str) -> List[Dict]:
    """
    解析AI返回的JSON格式响应
    :param response_text: AI返回的文本
    :return: 结构化患者数据列表
    """
    try:
        # 尝试直接解析为JSON
        data = json.loads(response_text)
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return [data]
    except json.JSONDecodeError:
        pass

    # 如果直接解析失败，尝试提取JSON部分
    try:
        # 查找可能的JSON数组或对象
        json_matches = re.findall(r'\[.*\]|\{.*\}', response_text, re.DOTALL)
        if json_matches:
            return json.loads(json_matches[0])
    except Exception as e:
        print(f"JSON解析失败: {str(e)}")

    # 最后尝试手动提取数据
    patients = []
    pattern = r'\{\s*"id"\s*:\s*(\d+)\s*,\s*"lymph_node_count"\s*:\s*(\d+)\s*\}'
    matches = re.findall(pattern, response_text)

    for id, lymph_node_count in matches:
        try:
            patients.append({
                "id": int(id.strip()),
                "lymph_node_count": int(lymph_node_count)
            })
        except ValueError:
            continue

    return patients


def update_lymph_node_counts(data: List[Dict]):
    """
    更新淋巴结数量到数据库
    :param data: 更新数据列表 [{"id": 123, "lymph_node_count": 10}, ...]
    """
    for item in data:
        # 确保ID是整数
        item_id = int(item['id'])
        lymph_count = int(item['lymph_node_count'])

        update_sql = f"""
        UPDATE ai.test_lymph_specification
        SET LYMPH_NODE_COUNT = {lymph_count}
        WHERE ID = {item_id}
        """
        try:
            result = db(None, None, update_sql, 'mysql')
            if result:
                print(f"成功更新记录 ID={item_id} 的淋巴结数量为 {lymph_count}")
            else:
                print(f"更新记录 ID={item_id} 失败: 数据库操作未返回结果")
        except Exception as e:
            print(f"更新记录 ID={item_id} 失败: {str(e)}")


# 定时任务入口
@app.task
def start_lymph_node_extraction():
    check_lymph_nodes()


if __name__ == "__main__":
    # 测试执行
    start_lymph_node_extraction()