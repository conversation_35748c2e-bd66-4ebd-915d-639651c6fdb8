import json
import re
from copy import deepcopy
from typing import <PERSON>hab<PERSON>, Tuple

import pandas
import pandas as pd
from pandas import DataFrame

import custom.settins
from datetime import datetime, timedelta
from custom.db.execute import db
from custom.function.methods_general import remove_first_last_mark, get_hours
from custom.function.method_sql import query_phone


def get_follow_up_plan(data_information: pandas.DataFrame) -> pandas.DataFrame:
    """
    data_information批量患者信息 包含 'INPATIENT_NO', 'SEX_CODE', 'IDENNO', 'HOME_TEL', 'LINKMAN_TEL', 'OUT_DATE'
    注：因查住院号的时候主表里面有出院时间，所以就要求外面查了。
    """
    # 提取个性化随访方案元素（出院诊断书-随访）
    inpatient_nos = data_information['INPATIENT_NO'].tolist()
    sub_lists = [inpatient_nos[i:i + 900] for i in range(0, len(inpatient_nos), 900)]  # 分批查询，900一批
    where_clause = " OR ".join([f"""INPATIENT_NO in ('{"', '".join(sub)}')""" for sub in sub_lists])
    sql = f"""select INPATIENT_NO, VALUE, ELEMENT_NAME
                from HIT_MDC.MDC_RCD_IN_RECORD_ITEM
                where ELEMENT_ID in ('2054157', '2054158', '2054159', '2054160',
                                     '2054161', '2054162', '2054163', '2054164',
                                     '2054172', '2054179', '2054186', '2054193',
                                     '2054173', '2054180', '2054187', '2054194',
                                     '2054174', '2054181', '2054188', '2054195',
                                     '2054175', '2054182', '2054189', '2054196',
                                     '2054176', '2054183', '2054190', '2054197',
                                     '2054177', '2054184', '2054191', '2054198',
                                     '2054178', '2054185', '2054192', '2054199',
                                     '375')
                and INPATIENT_RECORD_NAME = '出院诊断书-随访'
                and ({where_clause}) """
    result = db(None, None, sql, 'oracle')
    # 对每个患者的每个随访方案进行拆分合并
    follow_up = []
    for inpatient_no in set(data_information['INPATIENT_NO']):  # 不管有没有个性化随访方案，都遍历
        # 单个患者信息
        info_df = data_information[data_information['INPATIENT_NO'] == inpatient_no].reset_index(drop=True)
        # 单个患者个性化随访方案
        item_df = result[result['INPATIENT_NO'] == inpatient_no].reset_index(drop=True)
        follow_up_plan = []
        if not item_df.empty:
            # 有个性化方案的患者，首次为自动提醒
            follow_up_plan.append(
                {'index': 0,  # 没方案的就是index：0
                 'next_time': get_next_time(False, info_df.loc[0, 'OUT_DATE'],
                                            custom.settins.start_time_of_manual_follow_up * 24),
                 'cycle': False,
                 'precision': False,
                 'hours': custom.settins.start_time_of_manual_follow_up * 24,
                 'content': '自动回访任务',
                 'complete': '自动回访任务',
                 'state': 0  # sate为0，index也为0的就是重点病人的自动回访任务
                 })
            for i in range(9):
                # “随访计划周期”元素并非必须，所以直接输出bool就行
                plan_cycle = False
                df_cycle = item_df[item_df['ELEMENT_NAME'] == f'随访计划周期{i + 1}']
                str_cycle = ''
                if not df_cycle.empty:
                    str_cycle = df_cycle.iloc[0, 1].strip()
                    plan_cycle = str_cycle == '每'  # 删除首位空白后输出是否匹配每

                # “随访计划时间”元素如果没有，那就后面肯定没有了，直接跳出循环了
                df_time = item_df[item_df['ELEMENT_NAME'] == f'随访计划时间{i + 1}']
                if df_time.empty:
                    break
                plan_time = df_time.iloc[0, 1]
                plan_unit = item_df[item_df['ELEMENT_NAME'] == f'随访计划时间单位{i + 1}'].iloc[0, 1]
                plan_hours = get_hours(plan_time, plan_unit)
                if plan_hours == 0:
                    break

                next_time = get_next_time(plan_unit == '小时', info_df.loc[0, 'OUT_DATE'], int(plan_hours))
                plan_content = remove_first_last_mark(
                    item_df[item_df['ELEMENT_NAME'] == f'随访处理内容{i + 1}'].iloc[0, 1])
                complete = str_cycle + plan_time + plan_unit + plan_content

                # 拼装单个随访方案，cycle表示是否循环周期，precision表示描述是否为小时、以便发送精准短信，hours转换后小时数，content计划内容
                follow_up_plan.append({
                    'index': str(i + 1),
                    'next_time': next_time,
                    'cycle': plan_cycle,
                    'precision': plan_unit == '小时',
                    'hours': plan_hours,
                    'content': plan_content,
                    'complete': complete,
                    'state': 0
                })
        else:
            # 没写随访方案的患者
            follow_up_plan.append(
                {'index': 0,  # 没方案的就是index：0
                 'next_time': get_next_time(False, info_df.loc[0, 'OUT_DATE'],
                                            custom.settins.start_time_of_manual_follow_up * 24),
                 'cycle': False,
                 'precision': False,
                 'hours': custom.settins.start_time_of_manual_follow_up * 24,
                 'content': '常规随访任务',
                 'complete': '常规随访任务',
                 'state': 1  # 普通随访任务跳过通知患者的阶段
                 })

        # 拼接每个病人的随访方案  # todo 判断诊断等信息是否匹配需数据采集的患者。
        plan_df = pandas.DataFrame(follow_up_plan)  # 同一个病人会有多个随访方案
        # 联系电话
        tel_set = set()
        pattern = r'^1\d{10}$'
        if info_df.loc[0, 'HOME_TEL'] and re.match(pattern, info_df.loc[0, 'HOME_TEL']):
            tel_set.add(info_df.loc[0, 'HOME_TEL'])
        if info_df.loc[0, 'LINKMAN_TEL'] and re.match(pattern, info_df.loc[0, 'LINKMAN_TEL']):
            tel_set.add(info_df.loc[0, 'LINKMAN_TEL'])
        tel = json.dumps(list(tel_set))
        # 称呼
        appellation = '先生' if info_df.loc[0, 'SEX_CODE'] == 'M' else '女士'

        follow_up.append({'name': info_df.loc[0, 'NAME'], 'appellation': appellation, 'inpatient_no': inpatient_no,
                          'id_card': info_df.loc[0, 'IDENNO'], 'tel': tel,
                          'out_date': info_df.loc[0, 'OUT_DATE'], 'plan': plan_df,
                          'dept_code': info_df.loc[0, 'DEPT_CODE']})

    return pd.DataFrame(follow_up)


def insert_follow_up_db(data: pandas.DataFrame, test_model=False):
    """
    向数据库中添加随访信息
    """
    if data.empty:
        return
    # 将科室data['dept_code'] and 身份证号data['ID_CARD']相同的计划作废了。
    or_conditions = []
    index: int
    for index, row in data.iterrows():
        or_conditions.append(f"(ID_CARD = '{row['id_card']}' AND DEPT_CODE = '{row['dept_code']}')")
        if index % 100 == 0 and not test_model:  # 每100条执行一次
            sql = f"""UPDATE yzzxyy.follow_plan SET DONE = '1' WHERE {' OR '.join(or_conditions)}"""
            db(None, None, sql, 'mysql')
            or_conditions = []
    if or_conditions and not test_model:  # 处理剩余的记录
        sql = f"""UPDATE yzzxyy.follow_plan SET DONE = '1' WHERE {' OR '.join(or_conditions)}"""
        db(None, None, sql, 'mysql')

    # 记录插入
    focus = data[data['plan'].apply(lambda x: x is not None and not x.empty)]
    focus_item_list = []
    for _, patient_info in focus.iterrows():  # 遍历病人
        for _, rows in patient_info['plan'].iterrows():  # 遍历单个方案
            focus_item_list.append({'NEXT_TIME': rows['next_time'],
                                    'OUT_DATE': patient_info['out_date'],
                                    'INPATIENT_NO': patient_info['inpatient_no'],
                                    'NAME': patient_info['name'],
                                    'APPELLATION': patient_info['appellation'],
                                    'TEL': patient_info['tel'],
                                    'ID_CARD': patient_info['id_card'],
                                    'INDEX_ID': rows['index'],
                                    'CYCLE': '1' if rows['cycle'] else '0',
                                    'TO_HOUR': '1' if rows['precision'] else '0',
                                    'HOURS': rows['hours'],
                                    'CONTENT': rows['content'],
                                    'COMPLETE': rows['complete'],
                                    'STATE': rows['state']})
    if focus_item_list:
        focus_df = pandas.DataFrame(focus_item_list)
        batch_size = 100  # 保持和非重点病人相同的批次大小
        for i in range(0, len(focus_df), batch_size):
            batch = focus_df.iloc[i:i + batch_size]
            values = ", ".join(batch.apply(
                lambda x: f"""('{x['NEXT_TIME']}', '{x['OUT_DATE']}', '{x['INPATIENT_NO']}', 
                              '{x['NAME']}', '{x['APPELLATION']}', '{x['TEL']}', 
                              '{x['ID_CARD']}', '{x['INDEX_ID']}', '{x['CYCLE']}', 
                              '{x['TO_HOUR']}', '{x['HOURS']}', '{x['CONTENT']}', 
                              '{x['COMPLETE']}', '{x['STATE']}')""",
                axis=1))
            if values:
                sql = f'''INSERT INTO yzzxyy.follow_plan 
                         (NEXT_TIME, OUT_DATE, INPATIENT_NO, NAME, APPELLATION, 
                          TEL, ID_CARD, INDEX_ID, CYCLE, TO_HOUR, HOURS, CONTENT, COMPLETE, STATE)
                         VALUES {values}'''
                if not test_model:  # 保持和非重点病人相同的测试模式判断
                    db(None, None, sql, 'mysql')


def get_next_time(to_hour: bool, start_date: datetime, add_hours: int):
    if to_hour:
        next_time = (start_date + timedelta(hours=add_hours)).strftime("%Y-%m-%d %H:%M")
    else:
        next_time = ((start_date + timedelta(hours=add_hours)).strftime("%Y-%m-%d") +
                     ' ' + custom.settins.notification_time)
    return next_time


def get_follow_name_tel(dept_code, house_doc_name, house_doc_code):
    dedicated = db('DEDICATED_CODE', 'yzzxyy.follow_dedicated',
                   {'DEPT_CODE': dept_code}, 'mysql')
    tel = query_phone(dedicated if dedicated else house_doc_code)
    doctor_name = '回访专员' if dedicated else house_doc_name + '医生'
    return doctor_name, tel


def get_follow_code(dept_code, house_doc_code, house_doc_name):  # 获取随访人工号和姓名
    # todo：设置多个随访专员
    dedicated = db('DEDICATED_CODE', 'yzzxyy.follow_dedicated',
                   {'DEPT_CODE': dept_code}, 'mysql')
    if dedicated:
        name = db('name', 'yzzxyy.info_user', {'id': dedicated}, 'mysql')
        return dedicated, name
    else:
        return house_doc_code, house_doc_name


drgs_items_save = DataFrame()


def update_history(inpatient_no: str, drgs_d: dict, cmi_d, drgs_m: dict, cmi_m, level4, level4c, mic):
    """ 更新历史记录 """
    cmi_d = "NULL" if not cmi_d else cmi_d
    cmi_m = "NULL" if not cmi_m else cmi_m
    db(None, None,
       f"""INSERT INTO yzzxyy.history_cmi 
       (INPATIENT_NO, P_NAME, AGE, SEX, BABY_IN_WEIGHT, IN_DAY, OUT_DEPT, OUT_TIME, 
       HOUSE_DOC_NAME, CHARGE_DOC_NAME, PROFE_DOC_NAME, CHIEF_DOC_NAME, DRGS_D, CMI_D, DRGS_M, CMI_M, 
       DIAG_CODE_D, DIAG_NAME_D, OPER_CODE_D, OPER_NAME_D, DIAG_CODE_M, DIAG_NAME_M, OPER_CODE_M, OPER_NAME_M, 
       LEVEL4, LEVEL4C, MIC, FEE, CONDITION1_D, CONDITION2_D, CONDITION3_D, CONDITION4_D, CONDITION5_D, CC_D, MCC_D,
       CONDITION1_M, CONDITION2_M, CONDITION3_M, CONDITION4_M, CONDITION5_M, CC_M, MCC_M, UPDATE_TIME)
        VALUES ('{inpatient_no}', '{drgs_m['name']}', '{drgs_m['age']}', '{drgs_m['sex']}', 
                {'NULL' if not drgs_m['baby_in_weight'] else drgs_m['baby_in_weight']},'{drgs_m['day']}', 
                '{drgs_m['dept']}', '{drgs_m['out_time']}', 
                '{drgs_m['house_doc_name']}', '{drgs_m['charge_doc_name']}', '{drgs_m['profe_doc_name']}',
                '{drgs_m['chief_doc_name']}', '{drgs_d.get('drgs', '')}', {cmi_d}, '{drgs_m.get('drgs', '')}', {cmi_m}, 
                '{drgs_d.get('main_diag_code', '')}', '{drgs_d.get('main_diag_name', '')}', 
                '{drgs_d.get('main_oper_code', '')}', '{drgs_d.get('main_oper_name', '')}',
                '{drgs_m.get('main_diag_code', '')}', '{drgs_m.get('main_diag_name', '')}', 
                '{drgs_m.get('main_oper_code', '')}', '{drgs_m.get('main_oper_name', '')}',
                {level4}, {level4c}, {mic}, {drgs_m['fee']}, 
                '{drgs_d.get('condition1', '')}', '{drgs_d.get('condition2', '')}', '{drgs_d.get('condition3', '')}', 
                '{drgs_d.get('condition4', '')}', '{drgs_d.get('condition5', '')}', 
                '{"；".join(drgs_d.get('cc', ''))}', '{"；".join(drgs_d.get('mcc', ''))}',
                '{drgs_m.get('condition1', '')}', '{drgs_m.get('condition2', '')}', '{drgs_m.get('condition3', '')}',
                '{drgs_m.get('condition4', '')}', '{drgs_m.get('condition5', '')}', 
                '{"；".join(drgs_m.get('cc', ''))}', '{"；".join(drgs_m.get('mcc', ''))}', NOW())
        ON DUPLICATE KEY UPDATE P_NAME = VALUES(P_NAME), AGE = values(AGE), SEX = values(SEX), BABY_IN_WEIGHT = VALUES(BABY_IN_WEIGHT),
            IN_DAY = VALUES(IN_DAY), OUT_DEPT = VALUES(OUT_DEPT), OUT_TIME = VALUES(OUT_TIME),
            HOUSE_DOC_NAME = VALUES(HOUSE_DOC_NAME), CHARGE_DOC_NAME = VALUES(CHARGE_DOC_NAME),
            PROFE_DOC_NAME = VALUES(PROFE_DOC_NAME), CHIEF_DOC_NAME = VALUES(CHIEF_DOC_NAME),
            DRGS_D = VALUES(DRGS_D), CMI_D = VALUES(CMI_D), DRGS_M = VALUES(DRGS_M), CMI_M = VALUES(CMI_M),
            DIAG_CODE_D = VALUES(DIAG_CODE_D), DIAG_NAME_D = VALUES(DIAG_NAME_D), OPER_CODE_D = VALUES(OPER_CODE_D),
            OPER_NAME_D = VALUES(OPER_NAME_D),
            DIAG_CODE_M = VALUES(DIAG_CODE_M), DIAG_NAME_M = VALUES(DIAG_NAME_M), OPER_CODE_M = VALUES(OPER_CODE_M),
            OPER_NAME_M = VALUES(OPER_NAME_M),
            LEVEL4 = VALUES(LEVEL4), LEVEL4C = VALUES(LEVEL4C), MIC = VALUES(MIC), FEE = VALUES(FEE), 
            CONDITION1_D = VALUES(CONDITION1_D), CONDITION2_D = VALUES(CONDITION2_D), CONDITION3_D = VALUES(CONDITION3_D),
            CONDITION4_D = VALUES(CONDITION4_D), CONDITION5_D = VALUES(CONDITION5_D), CC_D = VALUES(CC_D), MCC_D = VALUES(MCC_D),
            CONDITION1_M = VALUES(CONDITION1_M), CONDITION2_M = VALUES(CONDITION2_M), CONDITION3_M = VALUES(CONDITION3_M),
            CONDITION4_M = VALUES(CONDITION4_M), CONDITION5_M = VALUES(CONDITION5_M), CC_M = VALUES(CC_M), MCC_M = VALUES(MCC_M),
            UPDATE_TIME = VALUES(UPDATE_TIME)""",
       'mysql')


def is_same_homepage(inpatient_no: str):
    sql = f"""WITH Homepage AS (SELECT HOMEPAGE_ID
                          FROM NMRWS.NMRWS_MR_HOMEPAGE
                          WHERE INPATIENT_NO = '{inpatient_no}'),
             Type1 AS (SELECT ICD_CODE AS CODE, IS_MAIN
                       FROM NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE
                       WHERE HOMEPAGE_ID IN (SELECT HOMEPAGE_ID FROM Homepage)
                         AND DIAGNOSE_TYPE IN (3, 8)
                         AND DIAGNOSE_CODE_TYPE = 1),
             Type2 AS (SELECT ICD_CODE AS CODE, IS_MAIN
                       FROM NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE
                       WHERE HOMEPAGE_ID IN (SELECT HOMEPAGE_ID FROM Homepage)
                         AND DIAGNOSE_TYPE IN (3, 8)
                         AND DIAGNOSE_CODE_TYPE = 2),
             Type3 AS (SELECT OPERATION_CODE AS CODE, IS_MAIN
                       FROM NMRWS.NMRWS_MR_HOMEPAGE_OPERATION
                       WHERE HOMEPAGE_ID IN (SELECT HOMEPAGE_ID FROM Homepage)
                         AND OPERATION_CODE_TYPE = 1),
             Type4 AS (SELECT OPERATION_CODE AS CODE, IS_MAIN
                       FROM NMRWS.NMRWS_MR_HOMEPAGE_OPERATION
                       WHERE HOMEPAGE_ID IN (SELECT HOMEPAGE_ID FROM Homepage)
                         AND OPERATION_CODE_TYPE = 2),
             A AS (SELECT COUNT(1) AS A
                   FROM Type1 T1 FULL OUTER JOIN Type2 T2 ON T1.CODE = T2.CODE AND T1.IS_MAIN = T2.IS_MAIN
                   WHERE T1.CODE IS NULL OR T2.CODE IS NULL),
             B AS (SELECT COUNT(1) AS B
                   FROM Type3 T3 FULL OUTER JOIN Type4 T4 ON T3.CODE = T4.CODE AND T3.IS_MAIN = T4.IS_MAIN
                   WHERE T3.CODE IS NULL OR T4.CODE IS NULL)
        SELECT A + B AS Total FROM A, B"""
    return db(None, None, sql, 'oracle', force_query=True).iloc[0]['TOTAL'] == 0


def drgs_from_no(inpatient_no, is_doctor: bool = True, exhaustion=False, full: bool = False):
    """ 依据住院号来找出最终分组结果（Warning System和remove celery worker 同用方法）
    exhaustion 代表首诊断探索，为 True 时返回值是Dataframe  """

    def get_diag(inpatient_no, is_doctor):
        """ 获取住院号首页基本信息和诊断 """
        code_type = 1 if is_doctor else 2
        info_df = db(None, None,
                     f"""SELECT NAME,
                                   HNYZ_ZXYY.FUN_GET_DEPT_NAME(OUT_DEPT_CODE)         DEPT,
                                   AGE_D,
                                   AGE_M,
                                   AGE_Y,
                                   BABY_IN_WEIGHT,
                                   DECODE(SEX_CODE, 1, '男', '女') AS                 SEX,
                                   AGE,
                                   IN_DAYS,
                                   HNYZ_ZXYY.FUN_GET_EMPL_NAME(INHOS_DOC_CODE_CODE)   HOUSE_DOC_NAME,
                                   HNYZ_ZXYY.FUN_GET_EMPL_NAME(ATTENDING_DOC_CODE)    CHARGE_DOC_NAME,
                                   HNYZ_ZXYY.FUN_GET_EMPL_NAME(PROFESSIONAL_DOC_CODE) PROFE_DOC_NAME,
                                   HNYZ_ZXYY.FUN_GET_EMPL_NAME(DEPT_DIRECTOR_CODE)    CHIEF_DOC_NAME,
                                   OUT_DATE,
                                   FEE_TOTAL
                                 FROM NMRWS.NMRWS_MR_HOMEPAGE WHERE INPATIENT_NO = '{inpatient_no}' """,
                     'oracle')
        if info_df.empty:
            return None
        info = info_df.iloc[0]

        sql = f"""WITH homepage_ids AS (
                        SELECT HOMEPAGE_ID
                        FROM NMRWS.NMRWS_MR_HOMEPAGE
                        WHERE INPATIENT_NO = '{inpatient_no}'
                    )
                    SELECT ICD_CODE AS CODE, ICD_NAME AS NAME, IS_MAIN, 0 AS OPER
                    FROM NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE
                    WHERE HOMEPAGE_ID IN (SELECT HOMEPAGE_ID FROM homepage_ids)
                      AND DIAGNOSE_CODE_TYPE = {code_type}
                      AND DIAGNOSE_TYPE IN (3, 8)
                    UNION ALL
                    SELECT OPERATION_CODE AS CODE, OPERATION_NAME AS NAME, IS_MAIN, 1 AS OPER
                    FROM NMRWS.NMRWS_MR_HOMEPAGE_OPERATION
                    WHERE HOMEPAGE_ID IN (SELECT HOMEPAGE_ID FROM homepage_ids)
                      AND OPERATION_CODE_TYPE = {code_type}"""
        return {"info": info, "code_df": db(None, None, sql, 'oracle', force_query=True)}

    ## STEP1：获取首页信息——————————————————————————————————————————————————————————————
    diag = get_diag(inpatient_no, is_doctor)
    if not diag and not exhaustion:
        return {'show': '未检索到患者首页'}
    elif not diag:
        return DataFrame([{'weight': {'show': f"未检索到患者首页"}}])

    info = diag['info']
    code_df = diag['code_df']
    # 诊断及手术
    result = {
        'name': info['NAME'], 'sex': info['SEX'], 'age': info['AGE'], 'day': info['IN_DAYS'],
        'dept': info['DEPT'], 'house_doc_name': info['HOUSE_DOC_NAME'], 'charge_doc_name': info['CHARGE_DOC_NAME'],
        'profe_doc_name': info['PROFE_DOC_NAME'], 'chief_doc_name': info['CHIEF_DOC_NAME'],
        'baby_in_weight': info['BABY_IN_WEIGHT'], 'out_time': info['OUT_DATE'], 'fee': info['FEE_TOTAL']}
    if code_df.empty and not exhaustion:
        result.update({'show': f"{info['DEPT']} {info['NAME']} | 患者首页诊断信息为空"})
        return result
    elif code_df.empty:
        result.update({'level': -1, 'weight': {'show': f"{info['DEPT']} {info['NAME']} | 患者首页诊断信息为空"}})
        return DataFrame([result])
    return drgs_common_method(code_df, info, exhaustion, full=full)


def drgs_common_method(code_df, info, exhaustion: bool = False, full: bool = False):
    """ Warning System和remove celery worker 同用方法
     exhaustion用于Warning System的探索优化，
     full用于详细报表统计"""

    def __condition(finish_adrg, main_diag_yb, main_oper_yb, other_diag_yb, other_oper_yb):
        # 寻找条件（AP无需前置，Y其他诊断，Z多诊断，其他均为主诊断）
        # 先找中标的子条件，如果子条件可以在主条件中找到就不用加主条件了，否则随便找个匹配到的主条件
        condition_code = db(
            None, None,
            f"""WITH CET AS ( SELECT CODE, GROUP_NUM, ITEM_NUM, TYPE  FROM ADRG WHERE
                         ADRG = '{finish_adrg[:3]}' AND (
                        (CODE = '{main_diag_yb}' AND TYPE IN (1, 2)) OR
                        (CODE IN ('{"', '".join(other_diag_yb)}') AND TYPE IN (2, 3)) OR
                        (CODE = '{main_oper_yb}' AND TYPE = 4) OR
                        (CODE IN ('{"', '".join(other_oper_yb)}') AND TYPE = 5)) )
                        SELECT CODE, GROUP_NUM, ITEM_NUM FROM CET WHERE GROUP_NUM = (
                            SELECT GROUP_NUM FROM CET GROUP BY GROUP_NUM ORDER BY MAX(ITEM_NUM) DESC LIMIT 1)""",
            'DRG', force_query=True)
        if finish_adrg.startswith('Y'):
            mdc_code = db(None, None,
                          f"""SELECT CODE FROM MDC WHERE MDC = 'Y' AND CODE IN 
                                        ('{"', '".join(condition_code['CODE'].tolist())}')""",
                          'DRG')
            if mdc_code.empty:
                # mdc为独立条件
                mdc_code = db(None, None,
                              f"""SELECT CODE FROM MDC WHERE MDC = 'Y' AND CODE IN 
                                            ('{"', '".join(other_diag_yb + [main_diag_yb])}')""",
                              'DRG').iloc[0]['CODE']
                condition_list = condition_code.groupby('ITEM_NUM')['CODE'].first().tolist() + [mdc_code]
            else:
                merged_df = pandas.merge(mdc_code, condition_code, on='CODE', how='left')
                mdc_id = merged_df['TYPE'].idxmin()
                condition_list = \
                    condition_code[condition_code['ITEM_NUM'] != merged_df.loc[mdc_id, 'ITEM_NUM']].groupby('ITEM_NUM')[
                        'CODE'].first().tolist() + [merged_df.loc[mdc_id, 'CODE']]
        elif finish_adrg.startswith('Z'):
            mdc_code = db(
                None, None,
                f"""select CODE FROM MDC
                                WHERE MDC = 'Z'
                                  AND TYPE != (SELECT TYPE FROM MDC WHERE MDC = 'Z' AND CODE = '{main_diag_yb}' LIMIT 1)
                                  AND CODE IN ('{"', '".join(other_diag_yb)}') LIMIT 1""",
                'DRG').iloc[0]['CODE']

            code_list = condition_code['CODE'].tolist()
            grouped_codes = condition_code.groupby('ITEM_NUM')['CODE'].first()

            exclude_item_nums = set()
            if mdc_code in code_list:
                exclude_item_nums.add(condition_code.loc[condition_code['CODE'] == mdc_code, 'ITEM_NUM'].iloc[0])
            if main_diag_yb in code_list:
                exclude_item_nums.add(condition_code.loc[condition_code['CODE'] == main_diag_yb, 'ITEM_NUM'].iloc[0])

            if mdc_code not in code_list and main_diag_yb not in code_list:
                condition_list = [mdc_code, main_diag_yb] + grouped_codes.tolist()
            else:
                condition_list = [main_diag_yb, mdc_code] + grouped_codes[
                    ~grouped_codes.index.isin(exclude_item_nums)].tolist()

        elif not finish_adrg.startswith(('A', 'P')):
            if main_diag_yb in condition_code['CODE'].tolist():
                mdc_item_num = condition_code[condition_code['CODE'] == main_diag_yb].iloc[0]['ITEM_NUM']
                condition_list = ([main_diag_yb] +
                                  condition_code[condition_code['ITEM_NUM'] != mdc_item_num].groupby('ITEM_NUM')[
                                      'CODE'].first().tolist())
            else:
                condition_list = [main_diag_yb] + condition_code.groupby('ITEM_NUM')['CODE'].first().tolist()
        else:
            condition_list = []

        # 把医保编码转换为名称
        name_list = []
        for i in condition_list:
            name_list.append(db('NAME', 'ICD', {'CODE_YB': f'{i}'}, 'DRG'))
        return name_list

    def get_drgs(finish_adrg: str, main_diag_yb: str, other_diag_yb: str) -> Tuple[str, list, list]:
        sql = f"SELECT SUBSTR(CODE, -1) TYPE, CMI FROM CMI_WEIGHT WHERE CODE LIKE '{finish_adrg}%'"
        drgs = db(None, None, sql, 'DRG')

        if drgs.shape[0] == 1:
            return finish_adrg + drgs.iloc[0]['TYPE'], [], []
        else:
            exclude = db('EXCLUDE', 'DRGS_EXCLUDE', {'ICD': main_diag_yb}, 'DRG')
            sql_template = {
                '1': f"SELECT ICD CODE, 1 TYPE FROM MCC WHERE EXCLUDE != '{exclude}'",
                '3': f"SELECT ICD CODE, 3 TYPE FROM CC WHERE EXCLUDE != '{exclude}'"
            }
            seen = set()
            conditions = [
                sql_template[typ]
                for typ in drgs['TYPE']
                if typ in sql_template and not (typ in seen or seen.add(typ))
            ]
            condition = ' UNION ALL '.join(conditions) if conditions else ''
            mcc = db(None, None,
                     f"""SELECT a.TYPE, (SELECT NAME FROM ICD i WHERE i.CODE_YB = a.CODE) AS NAME 
                     FROM ({condition}) a WHERE a.CODE IN ('{"', '".join(other_diag_yb)}') ORDER BY a.TYPE""",
                     'DRG', force_query=True)
            return (finish_adrg + '5' if mcc.empty else finish_adrg + str(mcc.iloc[0]['TYPE']),
                    [] if mcc.empty else mcc[mcc['TYPE'] == 3]['NAME'].tolist(),
                    [] if mcc.empty else mcc[mcc['TYPE'] == 1]['NAME'].tolist())

    result = {
        'name': info['NAME'], 'sex': info['SEX'], 'age': info['AGE'], 'day': info['IN_DAYS'],
        'dept': info['DEPT'], 'house_doc_name': info['HOUSE_DOC_NAME'], 'charge_doc_name': info['CHARGE_DOC_NAME'],
        'profe_doc_name': info['PROFE_DOC_NAME'], 'chief_doc_name': info['CHIEF_DOC_NAME'],
        'baby_in_weight': info['BABY_IN_WEIGHT'], 'out_time': info['OUT_DATE'], 'fee': info['FEE_TOTAL']}
    ## STEP2：获取诊断信息——————————————————————————————————————————————————————————————
    main_diag_df = code_df[(code_df['IS_MAIN'] == 1) & (code_df['OPER'] == 0)]
    if main_diag_df.empty:
        return result
    main_diag = main_diag_df.iloc[0]['CODE']
    main_diag_yb = get_yb_code(main_diag)
    other_diag = code_df[(code_df['IS_MAIN'] == 2) & (code_df['OPER'] == 0)]['CODE'].tolist()
    other_diag = [x for x in other_diag if x is not None]  # 把内容是None的诊断去掉
    other_diag_yb = [] if not other_diag else \
        db(None, None,
           f"""SELECT CODE_YB FROM ICD WHERE CODE IN ('{"', '".join(other_diag)}')""",
           'DRG')['CODE_YB'].tolist()
    complete_diag_yb = [main_diag_yb] + other_diag_yb
    main_oper = code_df[(code_df['IS_MAIN'] == 1) & (code_df['OPER'] == 1)].iloc[0]['CODE'] if not code_df[
        (code_df['IS_MAIN'] == 1) & (code_df['OPER'] == 1)].empty else ''
    main_oper = '' if not main_oper else main_oper  # 去除None
    main_oper_yb = '' if not main_oper else get_yb_code(main_oper)
    other_oper = code_df[(code_df['IS_MAIN'] == 2) & (code_df['OPER'] == 1)]['CODE'].tolist()
    other_oper = [x for x in other_oper if x is not None]
    other_oper_yb = [] if not other_oper else \
        db(None, None,
           f"""SELECT CODE_YB FROM ICD WHERE CODE IN ('{"', '".join(other_oper)}')""",
           'DRG')['CODE_YB'].tolist()
    complete_oper_yb = [main_oper_yb] + other_oper_yb if main_oper_yb else []
    result.update({'main_diag_code': main_diag,
                   'main_diag_name': code_df[code_df['CODE'] == main_diag].iloc[0].get('NAME', '') if main_diag else '',
                   'main_oper_code': main_oper,
                   'main_oper_name': code_df[code_df['CODE'] == main_oper].iloc[0].get('NAME', '') if main_oper else '',
                   'other_diag_code': other_diag,
                   'other_oper_code': other_oper})

    ## STEP3：MDC匹配【MDCA→MDCP→MACY→MDCZ→其余22个MDC并发】—————————————————————————————————
    global drgs_items_save  # 待匹配项目
    if drgs_items_save.empty:
        sql = f"""SELECT a.ADRG, a.GROUP_NUM, COUNT(DISTINCT a.ITEM_NUM) ITEM_COUNT, a.LEVEL 
                        FROM ADRG a GROUP BY a.ADRG, a.GROUP_NUM, a.LEVEL """
        drgs_items = db(None, None, sql, 'DRG')
        drgs_items['ITEM'] = drgs_items['ITEM_COUNT'].apply(lambda x: ''.join(map(str, range(1, x + 1))))
        drgs_items['PRE_COUNT'] = 0  # 剩余前置条件
        mask_z = drgs_items['ADRG'].str.startswith('Z', na=False)
        mask_ap = drgs_items['ADRG'].str.startswith('A', na=False)
        drgs_items.loc[mask_z, 'PRE_COUNT'] = 2
        drgs_items.loc[(~mask_z) & (~mask_ap), 'PRE_COUNT'] = 1
        drgs_items_save = deepcopy(drgs_items)
    else:
        drgs_items = deepcopy(drgs_items_save)
    ## 找MDC前置条件是否满足
    # 除了Y以外，必须要有主诊断匹配才行
    if exhaustion:
        # 探索模式用所有诊断去匹配MDC
        mdc_type = db(None, None,
                      f"""SELECT DISTINCT a.MDC, a.TYPE, a.CODE FROM MDC a WHERE a.CODE IN ('{"', '".join(complete_diag_yb)}') """,
                      'DRG')
    else:
        mdc_type = db(['MDC', 'TYPE', 'CODE'], 'MDC', {'CODE': main_diag_yb}, 'DRG')
    mdc_list = mdc_type['MDC'].unique()
    for mdc in mdc_list:  # 按MDC对待匹配目录进行处理
        mask = drgs_items["ADRG"].str.startswith(mdc, na=False)
        drgs_items.loc[mask, "PRE_COUNT"] -= 1
    for row in mdc_type.itertuples(index=False):  # 对MDC中Z目录进行二次匹配
        sql = f"""SELECT COUNT(1) NUM FROM MDC a WHERE a.MDC = 'Z' AND a.TYPE != {row.TYPE} AND a.CODE in
                         ('{"', '".join(other_diag_yb)}')"""
        if row.MDC == 'Z' and db(None, None, sql, 'DRG').iloc[0]['NUM'] != 0:
            drgs_items.loc[drgs_items["ADRG"].str.startswith('Z', na=False), "PRE_COUNT"] = 0
            if exhaustion:
                other_diag_yb = [x for x in complete_diag_yb if x != row.CODE]
            break

    # Y的话只需要其他诊断匹配就行
    sql = f"""SELECT COUNT(1) NUM FROM MDC a WHERE a.MDC = 'Y' AND a.CODE in ('{"', '".join(complete_diag_yb)}')"""
    drgs_items.loc[drgs_items["ADRG"].str.startswith('Y', na=False), "PRE_COUNT"] -= 1 if \
        db(None, None, sql, 'DRG').iloc[0]['NUM'] != 0 else 0
    # P的话需要匹配年龄和出生体重等
    if info['AGE_Y'] == 0:
        if info['AGE_M'] == 0 and info['AGE_D'] < 29:
            drgs_items.loc[drgs_items['ADRG'].isin(['PB1', 'PC1', 'PD1', 'PJ1', 'PK1', 'PR1']), "PRE_COUNT"] -= 1
        else:
            drgs_items.loc[drgs_items['ADRG'].isin(['PV1']), "PRE_COUNT"] -= 1
        if info['BABY_IN_WEIGHT'] and info['BABY_IN_WEIGHT'] < 1500:
            drgs_items.loc[drgs_items['ADRG'].isin(['PS1']), "PRE_COUNT"] -= 1
        elif info['BABY_IN_WEIGHT'] and info['BABY_IN_WEIGHT'] <= 1999:
            drgs_items.loc[drgs_items['ADRG'].isin(['PS2']), "PRE_COUNT"] -= 1
        elif info['BABY_IN_WEIGHT'] and info['BABY_IN_WEIGHT'] <= 2499:
            drgs_items.loc[drgs_items['ADRG'].isin(['PS3']), "PRE_COUNT"] -= 1
        else:
            drgs_items.loc[drgs_items['ADRG'].isin(['PS4', 'PU1']), "PRE_COUNT"] -= 1

    mdc_list = mdc_type['MDC'].unique().tolist() + ['A']
    if info['AGE_Y'] == 0:
        mdc_list += ['P']

    ## STEP4：找子条件是否满足——————————————————————————————————————————————————————————————
    reg = "(" + " OR ".join([f"a.ADRG LIKE '{prefix}%'" for prefix in list(set(mdc_list))]) + ")"  # MDC匹配
    # 子诊断匹配
    if exhaustion:  # 探索模式
        condition = [f"""A.CODE IN ('{"', '".join(complete_diag_yb + complete_oper_yb)}') """]
    else:
        condition = [f"A.CODE = '{main_diag_yb}' AND TYPE IN (1, 3)"]
        if other_diag_yb:
            condition.append(f"""A.CODE IN ('{"', '".join(other_diag_yb)}') AND TYPE IN (2, 3)""")
        if main_oper_yb:
            condition.append(f"A.CODE = '{main_oper_yb}' AND TYPE IN (4, 5)")
        if other_oper_yb:
            condition.append(f"""A.CODE IN ('{"', '".join(other_oper_yb)}') AND TYPE = 5""")
    sql = f"""SELECT DISTINCT a.ADRG AS ADRG, a.CODE, a.TYPE, a.GROUP_NUM AS GROUP_NUM, a.ITEM_NUM AS ITEM_NUM
                    FROM ADRG a WHERE {reg} AND (({') OR ('.join(condition)})) """
    adrg_match = db(None, None, sql, 'DRG')

    for row in adrg_match.itertuples(index=False):
        mask = ((drgs_items["ADRG"] == row.ADRG) & (drgs_items["GROUP_NUM"] == row.GROUP_NUM) &
                drgs_items["ITEM"].str.contains(str(row.ITEM_NUM), na=False))
        if any(mask):
            drgs_items.loc[mask, "ITEM"] = drgs_items.loc[mask, "ITEM"].iloc[0].replace(str(row.ITEM_NUM), '')
            drgs_items.loc[mask, "ITEM_COUNT"] -= 1

    ## 追加其他特殊分类（包含全部手术、无手术等）
    # 过滤排除手术条件
    values_union = " UNION ALL ".join([f"SELECT '{v}' AS value" for v in complete_oper_yb])
    if not values_union or (db(None, None,
                               f"""SELECT checklist.value FROM ({values_union}) AS checklist
                                            WHERE NOT EXISTS (SELECT 1
                                                              FROM main.CODE_EXCLUDE
                                                              WHERE ICD = checklist.value)""", 'DRG')).empty:
        if 'Z' in mdc_list:
            drgs_items.loc[len(drgs_items)] = ['ZZ1', 1, 1, 4, 0, 0]
    else:
        mdc_mapping = {
            'S': ['SB1', 1, 1, 5, 0, 0],
            'T': ['TB1', 1, 1, 5, 0, 0],
            'X': ['XJ1', 1, 1, 5, 0, 0],
            'Y': ['YC1', 1, 1, 3, 0, 0]
        }
        for char, values in mdc_mapping.items():
            if char in mdc_list:
                drgs_items.loc[len(drgs_items)] = values

    ## STEP5：最终匹配————————————————————————————————————————————————————————————————————————
    finish = drgs_items.loc[(drgs_items['ITEM_COUNT'] == 0) & (drgs_items['PRE_COUNT'] == 0)]
    if finish.empty and not exhaustion:
        result.update({'show': f"{info['DEPT']} {info['NAME']} | 未入组"})
        return result
    elif finish.empty:
        return DataFrame([{'name': info['NAME'], 'dept': info['DEPT'],
                           'weight': {'show': f"{info['DEPT']} {info['NAME']} | 未入组"}}])
    # 权重
    unique_adrgs = finish['ADRG'].unique()
    cmi_condition = " OR ".join([f"CODE LIKE '{adrg}%'" for adrg in unique_adrgs])
    weight_df = db(['CODE', 'CMI', 'VIR'], 'CMI_WEIGHT', cmi_condition, 'DRG')
    weight_df['ADRG'] = weight_df['CODE'].str[:3]
    adrg_weights = (
        weight_df.groupby('ADRG')
        .agg({'CMI': ['max', 'min'], 'VIR': 'first'})  # 使用 'first' 来保留 'VIR' 的第一个值
        .reset_index()
    )
    adrg_weights.columns = ['ADRG', 'max', 'min', 'VIR']
    # 拼接
    finish = finish.merge(adrg_weights, on="ADRG")  # 最终匹配

    if exhaustion:
        # 探索模式
        mdc_type = mdc_type.set_index('MDC')  # 前期MDC
        adrg_match = adrg_match.set_index('ADRG')  # 前期ADRG列表

        finish['MDC_CODE'] = finish['ADRG'].str[0]  # 最终匹配的MDC
        merged = (finish.merge(mdc_type[mdc_type.index.isin(finish['MDC_CODE'])],  # 将前期MDC过滤为最终MDC
                               left_on='MDC_CODE', right_index=True, how='inner')
                  .merge(adrg_match,
                         left_on='ADRG', right_index=True, suffixes=('_mdc', '_adrg')))
        condition_type123 = merged['TYPE_adrg'].isin([1, 2, 3]) & (
                (merged['TYPE_adrg'] != 1) | (merged['CODE_mdc'] == merged['CODE_adrg']))
        condition_type4 = merged['TYPE_adrg'] == 4
        ex_adrg_df = pandas.concat([merged[condition_type123].assign(CODE=lambda x: x['CODE_mdc'], TYPE=1),
                                    merged[condition_type4].assign(CODE=lambda x: x['CODE_adrg'], TYPE=4)])[
            ['ADRG', 'CODE', 'TYPE', 'GROUP_NUM_adrg', 'ITEM_NUM', 'LEVEL', 'max', 'min',
             'VIR']].drop_duplicates().reset_index(drop=True)

        # ex_adrg = []
        # for adrg_finish in finish.itertuples(index=False):
        #     for mdc in mdc_type[mdc_type['MDC'] == adrg_finish.ADRG[0]].itertuples(index=False):
        #         for adrg in adrg_match[adrg_match['ADRG'] == adrg_finish.ADRG].itertuples(index=False):
        #             if (adrg.TYPE == 1 and adrg.CODE == mdc.CODE) or (adrg.TYPE in (2, 3)):  # 主诊断
        #                 ex_adrg.append({'ADRG': adrg.ADRG, 'CODE': mdc.CODE, 'TYPE': 1,
        #                                 'GROUP_NUM': adrg.GROUP_NUM, 'ITEM_NUM': adrg.ITEM_NUM,
        #                                 'MAX': adrg_finish.max, 'MIN': adrg_finish.min, 'VIR': adrg_finish.VIR})
        #             if adrg.TYPE == 4:
        #                 ex_adrg.append({'ADRG': adrg.ADRG, 'CODE': adrg.CODE, 'TYPE': 4,
        #                                 'GROUP_NUM': adrg.GROUP_NUM, 'ITEM_NUM': adrg.ITEM_NUM,
        #                                 'MAX': adrg_finish.max, 'MIN': adrg_finish.min, 'VIR': adrg_finish.VIR})
        # ex_adrg_df2 = DataFrame(ex_adrg).drop_duplicates()

        exhaustion_result = []
        grouped = ex_adrg_df.groupby('ADRG')
        for adrg_code, adrg_group in grouped:
            drgs, _, _ = get_drgs(adrg_code, main_diag_yb, other_diag_yb)
            item = {
                'main_diags': adrg_group.query('TYPE == 1')['CODE'].tolist() or \
                              mdc_type.filter(like=str(adrg_code[0]), axis=0)['CODE'].tolist(),
                'main_diag': main_diag,
                'main_opers': adrg_group[adrg_group['TYPE'] == 4]['CODE'].tolist(),
                'main_oper': main_oper,
                'drgs': drgs,
                'level': adrg_group['LEVEL'].iloc[0],
                'name': info['NAME'],
                'dept': info['DEPT']
            }

            item['is_main_diag'] = (main_diag_yb in item['main_diags']) or not item['main_diags']
            item['is_main_oper'] = (main_oper_yb in item['main_opers']) or main_oper_yb == ''
            item['weight'] = drgs_weight(item['drgs'])
            item['max_weight'] = item['weight'].get('max', 0)
            exhaustion_result.append(item)
        final_drgs = DataFrame(exhaustion_result).sort_values(['level', 'drgs'], ascending=[True, True])
        # 如果所有的is_main_oper 都是 False 或者 没有is_main_diag和is_main_oper的双True，那么就把main_opers为空的is_main_oper变为True
        if not final_drgs['is_main_oper'].any() or not (final_drgs['is_main_diag'] & final_drgs['is_main_oper']).any():
            mask = final_drgs['main_opers'].apply(lambda x: x == [])  # 创建空列表判断掩码
            final_drgs.loc[mask, 'is_main_oper'] = True
        mask = (final_drgs['is_main_diag']) & (final_drgs['is_main_oper'])
        main_rows = final_drgs[mask]
        if not main_rows.empty:
            main_row = main_rows.iloc[[0]]
            main_row_weight = main_row['max_weight'].values[0]
            other_rows = final_drgs[~mask].copy()
            sorted_other = other_rows.sort_values('max_weight', ascending=False)
            filtered_other = sorted_other[sorted_other['max_weight'] > main_row_weight]
            final_sorted = pandas.concat([main_row, filtered_other], ignore_index=True)
        else:
            final_sorted = final_drgs.sort_values('max_weight', ascending=False)
        return final_sorted
    else:
        finish_adrg = finish.sort_values(['LEVEL', 'ADRG'], ascending=[True, True]).iloc[0]['ADRG']
        finish_adrg, cc, mcc = get_drgs(finish_adrg, main_diag_yb, other_diag_yb)
        result['drgs'] = finish_adrg
        weight = drgs_weight(finish_adrg)
        result['show'] = (f"{info['DEPT']} {info['NAME']} |"
                          f" ADRG组：{finish_adrg[:3]} {weight['show']} |"
                          f" 当前入组：{finish_adrg} 【{weight['vir']}{weight['cmi']}】")
        result['weight'] = weight
        if full:
            condition_list = __condition(finish_adrg, main_diag_yb, main_oper_yb, other_diag_yb, other_oper_yb)
            condition_list += [''] * (5 - len(condition_list))
            result.update({'condition1': condition_list[0], 'condition2': condition_list[1],
                           'condition3': condition_list[2], 'condition4': condition_list[3],
                           'condition5': condition_list[4], 'cc': cc, 'mcc': mcc})
        return result


def drgs_from_df(inpatient_no) -> dict:
    """ 依据住院号来找出最终分组结果 """
    info_df = db(None, None,
                 f"""SELECT A48,
                                     C03C,
                                   C06x01C,
                                   C06x02C,
                                   C06x03C,
                                   C06x04C,
                                   C06x05C,
                                   C06x06C,
                                   C06x07C,
                                   C06x08C,
                                   C06x09C,
                                   C06x10C,
                                   C06x11C,
                                   C06x12C,
                                   C06x13C,
                                   C06x14C,
                                   C06x15C,
                                   C06x16C,
                                   C06x17C,
                                   C06x18C,
                                   C06x19C,
                                   C06x20C,
                                   C06x21C,
                                   C06x22C,
                                   C06x23C,
                                   C06x24C,
                                   C06x25C,
                                   C06x26C,
                                   C06x27C,
                                   C06x28C,
                                   C06x29C,
                                   C06x30C,
                                   C06x31C,
                                   C06x32C,
                                   C06x33C,
                                   C06x34C,
                                   C06x35C,
                                   C06x36C,
                                   C06x37C,
                                   C06x38C,
                                   C06x39C,
                                   C06x40C,
                                   C14x01C,
                                   C35x01C,
                                   C35x02C,
                                   C35x03C,
                                   C35x04C,
                                   C35x05C,
                                   C35x06C,
                                   C35x07C,
                                   C35x08C,
                                   C35x09C,
                                   C35x10C,
                                   C35x11C,
                                   C35x12C,
                                   C35x13C,
                                   C35x14C,
                                   C35x15C,
                                   C35x16C,
                                   C35x17C,
                                   C35x18C,
                                   C35x19C,
                                   C35x20C,
                                   C35x21C,
                                   C35x22C,
                                   C35x23C,
                                   C35x24C,
                                   C35x25C,
                                   C35x26C,
                                   C35x27C,
                                   C35x28C,
                                   C35x29C,
                                   C35x30C,
                                   C35x31C,
                                   C35x32C,
                                   C35x33C,
                                   C35x34C,
                                   C35x35C,
                                   C35x36C,
                                   C35x37C,
                                   C35x38C,
                                   C35x39C,
                                   C35x40C,
                                   '' NAME,
                                   '' DEPT,
                                   A14 AGE_Y,
                                   0 AGE_M,
                                   A16 AGE_D,
                                   A18x01 BABY_IN_WEIGHT
                            FROM my_table
                            WHERE A48 = '{inpatient_no[0]}' AND A49 = '{inpatient_no[1]}' """,
                 'HomePage')

    info = info_df.iloc[0]
    df = info.to_frame().T
    code_columns = [col for col in df.columns if col.startswith(('C03', 'C06', 'C14', 'C35'))]
    melted = df.melt(id_vars=['A48'],
                     value_vars=code_columns,
                     var_name='CODE_COLUMN',
                     value_name='CODE_VALUE')
    melted['PREFIX'] = melted['CODE_COLUMN'].str[:3]
    melted['IS_MAIN'] = melted['PREFIX'].apply(lambda x: 1 if x in ['C03', 'C14'] else 2)
    melted['OPER'] = (~melted['PREFIX'].isin(['C03', 'C06'])).astype(int)
    result = melted[['CODE_VALUE', 'IS_MAIN', 'OPER']].rename(columns={'CODE_VALUE': 'CODE'})
    code_df = result.dropna(subset=['CODE'])

    # 医生
    finish_adrg_d = drgs_common_method(code_df.reset_index(drop=True), info, full=False)  # 分组
    if not finish_adrg_d.get('adrg', ''):
        return {}
    else:
        weight_d = drgs_weight(finish_adrg_d['adrg'])
    return {'CMI': weight_d}


def drgs_weight(drgs: str) -> dict:
    """ 依据已经确定的drgs组，输出对应权重及相关adrg权重区间 """
    cmi_range = db(None, None,
                   f"""SELECT max(CMI) max, min(CMI) min, first_value(VIR) OVER (ORDER BY CMI) AS VIR
                                   FROM CMI_WEIGHT WHERE CODE LIKE '{drgs[:3]}%'""",
                   'DRG').iloc[0]
    cmi = db('CMI', 'CMI_WEIGHT', {'CODE': drgs}, 'DRG')
    vir = '*' if cmi_range['VIR'] == 1 else ''
    return {'max': cmi_range['max'], 'min': cmi_range['min'], 'cmi': cmi, 'is_max': cmi_range['max'] == cmi,
            'show': f"【{vir}{cmi_range['min']} - {cmi_range['max']}】" if cmi_range['min'] != cmi_range[
                'max'] else f"【{vir}{cmi_range['max']}】", 'vir': vir}


def get_yb_code(code):
    """ 依据临床ICD获取医保ICD编码 """
    return db('CODE_YB', 'ICD', {'CODE': code}, 'DRG')
