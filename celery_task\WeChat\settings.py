CUSTOMER_TOKEN = "medtechhub"  # 官网配置的Token
EncodingAESKey = "05uzwNNP8OgLHiLj4fuWlpA8UMmAOMx7ZjkX5jPEeGc"  # 消息加解密Key
APP_ID = "wx4618f4e453c4ef06"  # 开发者ID
TEMP_ID = {'generate': 't8nNr5E5ZjgxInmm8qxFEZKlDMm1gu3BOajlitnoHDo',
           # {"thing33": {"value": "随访任务"},
           #  "thing4": {"value": "计划任务"},
           #  "number19": {"value": f"{row.NUM}"}
           'timeout': 'mmZg_W655WecmcPLw3CraEvQ7cJsZQXoYKCKzZwh098',
           # {"thing3": {"value": "随访任务"},
           #  "thing6": {"value": out_type[row.WARN_LEVEL]},
           #  "character_string29": {"value": f"{row.NUM}"}
           'timeout_chief': '1fdeP35LI4IrCwqQEbLdRXIe2v04Uw9sFjZKUfjLC90',
           # {"thing8": {"value": "随访任务严重超时"},
           #  "thing5": {"value": inf['HOUSE_DOC_NAME']},
           #  "character_string7": {"value": f"{row.NUM}"}
           'unbind': 't4EespIFACe6PqXVEXET1HJsY_cnC0uTzZ8W3UObFw0',
           # {"thing1": {"value": info['NAME']},
           #  "thing3": {"value": '账号被其他用户绑定'},
           #  "time2": {"value": time.strftime("%Y年{}月{}日 %H:%M").format(*(time.localtime()[1:3]))}}

           }
