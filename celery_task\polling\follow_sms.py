import time

from celery_task.WeChat.interface import send_template
from celery_task.celery import app
from celery_task.privacy.bind import notice
from custom.db.execute import db
from custom.function.method_sql import query_openid, query_phone, query_name


# 每日定时通知
@app.task
def start():
    dsms = db(None, None, f"SELECT * FROM yzzxyy.follow_dsms", 'mysql')
    for row in dsms.itertuples(index=False):
        time.sleep(5)
        # 普通消息通知(新任务)
        if row.VOIP == 0 and row.CHIEF == 0 and row.WARN_LEVEL == 0:
            open_id = query_openid(row.CODE)
            send_template({"thing33": {"value": "随访任务"},
                       "thing4": {"value": "计划任务"},
                       "number19": {"value": f"{row.NUM}"}},
                      open_id, 'generate')
        # 普通消息通知（任务超时）
        elif row.VOIP == 0 and row.CHIEF == 0 and row.WARN_LEVEL > 0:
            open_id = query_openid(row.CODE)
            out_type = {1: '任务超时', 2: '严重超时'}
            send_template({"thing3": {"value": "随访任务"},
                       "thing6": {"value": out_type[row.WARN_LEVEL]},
                       "character_string29": {"value": f"{row.NUM}"}},
                      open_id, 'timeout')
        # 主任消息通知
        elif row.VOIP == 0 and row.CHIEF == 1:
            inf = db(['HOUSE_DOC_NAME', 'HOUSE_DOC_CODE', 'CHIEF_DOC_CODE', 'CHIEF_DOC_NAME', 'NAME', 'DEPT_CODE'],
                     'HNYZ_ZXYY.FIN_IPR_INMAININFO',
                     {'INPATIENT_NO': row.INPATIENT_NO}, 'oracle').iloc[0]
            open_id = query_openid(inf['CHIEF_DOC_CODE'])
            send_template({"thing8": {"value": "随访任务严重超时"},
                       "thing5": {"value": inf['HOUSE_DOC_NAME']},
                       "character_string7": {"value": f"{row.NUM}"}},
                      open_id, 'timeout_chief')
        # 语音通知(都是给主管医生的)
        else:
            out_type = {1: '超时', 2: '严重超时', 3: '严重超时已被行政监管'}
            phone = query_phone(row.CODE)
            if not phone:
                continue
            name = query_name(row.CODE)
            msg = f'尊敬的{name}医生，您有{row.NUM}项随访任务{out_type.get(row.WARN_LEVEL)}，请尽快登录智慧医疗管理系统中处理。'
            notice(phone, msg, play_size=2)
    db(None, None, "DELETE FROM yzzxyy.follow_dsms", 'mysql')
