from celery_task.celery import app
from custom.db.execute import db
import custom.settins
import json
import re
import pandas as pd
from datetime import datetime, timedelta
from celery_task.wx_small_program.messageBus import send_worker


def get_next_time(to_hour: bool, start_date: datetime, add_hours: int):
    """获取下次随访时间"""
    if to_hour:
        next_time = (start_date + timedelta(hours=add_hours)).strftime("%Y-%m-%d %H:%M")
    else:
        next_time = ((start_date + timedelta(hours=add_hours)).strftime("%Y-%m-%d") +
                     ' ' + custom.settins.notification_time)
    return next_time


def get_follow_code_refactored(dept_code, house_doc_code, house_doc_name):
    """获取随访人工号和姓名 - 重构版本"""
    dedicated = db('DEDICATED_CODE', 'follow.dedicated', {'DEPT_CODE': dept_code}, 'mysql')
    if dedicated:
        name = db('name', 'yzzxyy.info_user', {'id': dedicated}, 'mysql')
        return dedicated, name
    else:
        return house_doc_code, house_doc_name


@app.task
def start(test_model=False):
    """
    重构后的随访超时处理主函数
    """
    # 1. 查询新数据库中的随访计划和对应状态
    sql = f'''SELECT p.ID, p.INPATIENT_NO, p.NEXT_ACTION_TIME, p.CYCLE, p.HOURS, p.CONTENT,
                     p.DEPT_CODE, p.HOUSE_DOC_CODE, p.HOUSE_DOC_NAME,
                     s.STATE, s.DOWN, s.NEXT_TIME
              FROM follow.plan p
              LEFT JOIN follow.state s ON s.FOLLOW_PLAN_ID = p.ID
              WHERE (s.DOWN = 0 OR s.DOWN IS NULL) 
                AND (s.NEXT_TIME <= NOW() OR p.NEXT_ACTION_TIME <= NOW())'''

    data_timeout = db(None, None, sql, 'mysql')
    if data_timeout.empty:
        return

    # 2. 批量获取患者基本信息
    all_inpatient_nos = data_timeout['INPATIENT_NO'].unique().tolist()
    main_info = pd.DataFrame()
    if all_inpatient_nos:
        inpatient_nos_str = "', '".join(all_inpatient_nos)
        main_info = db(
            None, None,
            f"""SELECT f.INPATIENT_NO, f.NAME, f.OUT_DATE, f.DEPT_NAME, f.IDENNO,
                       f.DEPT_CODE, f.HOUSE_DOC_CODE, f.HOUSE_DOC_NAME,
                       f.CHIEF_DOC_CODE, f.CHIEF_DOC_NAME,
                       COALESCE(h.PRESENT_TEL, f.HOME_TEL, f.WORK_TEL) AS PRESENT_TEL,
                       COALESCE(h.CONTACT_TEL, f.LINKMAN_TEL, f.WORK_TEL) AS CONTACT_TEL,
                       COALESCE(h.CERTIFICATE_NO, f.IDENNO) AS CERTIFICATE_NO
                FROM HNYZ_ZXYY.FIN_IPR_INMAININFO f
                LEFT JOIN NMRWS.NMRWS_MR_HOMEPAGE h ON h.INPATIENT_NO = f.INPATIENT_NO
                WHERE f.INPATIENT_NO IN ('{inpatient_nos_str}')""", 'oracle')

    # 3. 合并数据
    data_merged = data_timeout.merge(main_info, on='INPATIENT_NO', how='left', suffixes=('', '_main'))

    # 4. 处理逻辑
    state_updates = []  # 状态更新
    notice_to_insert = []  # 通知插入
    doctor_notices = []  # 医生通知
    all_cycle_states = []  # 周期性状态记录

    for _, row in data_merged.iterrows():
        current_state = row.get('STATE', '待处理')

        # 检查是否有住院行为
        if _check_hospitalization(row):
            # 标记为已完成
            state_updates.append({
                'plan_id': row['ID'],
                'state': '已完成',
                'down': 1,
                'next_time': None
            })
            continue

        # 检查是否有挂号行为
        if _check_registration(row):
            # 标记为已完成
            state_updates.append({
                'plan_id': row['ID'],
                'state': '已完成',
                'down': 1,
                'next_time': None
            })
            continue

        # 检查是否为周期性任务且尚未生成周期状态
        if row.get('CYCLE', False) and (current_state == '待处理' or current_state is None):
            # 为周期性任务生成所有周期状态
            cycle_states = _generate_cycle_states(row['ID'], row, test_model)
            all_cycle_states.extend(cycle_states)

            # 标记当前任务为已处理（因为已经生成了所有周期）
            state_updates.append({
                'plan_id': row['ID'],
                'state': '已完成',
                'down': 1,
                'next_time': None
            })
            continue

        # 根据当前状态进行处理,因为从数字变成了枚举，只能每一个状态一个函数处理
        if current_state == '待处理' or current_state is None:
            _process_pending_state(row, state_updates, notice_to_insert)
        elif current_state == '通知':
            _process_notification_state(row, state_updates, doctor_notices, test_model)
        elif current_state == '任务生成':
            _process_task_generated_state(row, state_updates, doctor_notices)
        elif current_state == '超时':
            _process_timeout_state(row, state_updates, doctor_notices)
        elif current_state == '二次超时':
            _process_second_timeout_state(row, state_updates, doctor_notices)

    # 5. 批量执行数据库更新
    _execute_batch_updates(state_updates, notice_to_insert, doctor_notices, test_model)

    # 6. 插入周期性状态记录,将周期性任务一次性生成所有的随访计划，并一次性插入到state表
    if all_cycle_states:
        _insert_cycle_states(all_cycle_states, test_model)
    # print(f"为周期性任务生成了 {len(all_cycle_states)} 个状态记录")

    if not test_model:
        end_time = str(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        sql = f'''UPDATE yzzxyy.info_db SET value = '{end_time}' WHERE index_information = 'follow_timeout_time' '''
        db(None, None, sql, 'mysql')


def _check_hospitalization(row):
    """检查是否有住院行为"""
    if pd.isna(row.get('IDENNO')) or pd.isna(row.get('OUT_DATE')):
        return False

    out_date_str = row['OUT_DATE'].strftime('%Y-%m-%d %H:%M:%S')
    sql = f"""SELECT COUNT(1) as cnt FROM HNYZ_ZXYY.FIN_IPR_INMAININFO
              WHERE IDENNO = '{row['IDENNO']}' AND DEPT_CODE = '{row['DEPT_CODE']}'
              AND IN_DATE > TO_DATE('{out_date_str}', 'YYYY-MM-DD HH24:MI:SS')"""
    result = db(None, None, sql, 'oracle')
    return not result.empty and result.iloc[0]['CNT'] > 0


def _check_registration(row):
    """检查是否有挂号行为"""
    id_card = row.get('CERTIFICATE_NO', row.get('IDENNO'))
    if pd.isna(id_card):
        return False

    interval_hours = (custom.settins.key_patients_waiting_time_for_execution +
                      custom.settins.effective_registration_time_before_reminding_key_patients) * 24
    sql = f"""SELECT COUNT(1) as cnt FROM HNYZ_ZXYY.FIN_OPR_REGISTER 
              WHERE TRANS_TYPE = 1 AND IDENNO = '{id_card}'
              AND REG_DATE >= CURRENT_TIMESTAMP - INTERVAL '{interval_hours}' HOUR(4)"""
    result = db(None, None, sql, 'oracle')
    return not result.empty and result.iloc[0]['CNT'] > 0


def _process_pending_state(row, state_updates, notice_to_insert):
    """处理待处理状态"""
    # 发送通知
    tel_list = _get_patient_phone_list(row)
    if tel_list:
        content = _generate_notification_content(row)
        notice_to_insert.append({
            'INPATIENT_NO': row.get('INPATIENT_NO', ''),
            'PATIENT_NAME': row.get('NAME', ''),
            'ID_CARD': row.get('IDENNO', ''),
            'CONTENT': row.get('CONTENT', ''),
            'COMPLETE': content,
            'DEPT_CODE': row.get('DEPT_CODE', ''),
            'TEL': json.dumps(tel_list),
            'INPATIENT_PHONE': tel_list[0] if tel_list else None,  # 取第一个电话号码
            'IS_SENT': 0  # 默认未发送
        })

    # 更新状态为通知
    next_time = get_next_time(False, datetime.now(),
                              custom.settins.key_patients_waiting_time_for_execution * 24)
    state_updates.append({
        'plan_id': row['ID'],
        'state': '通知',
        'down': 0,
        'next_time': next_time
    })


def _process_notification_state(row, state_updates, doctor_notices, test_model=False):
    """处理通知状态"""
    # 生成人工任务（如果需要）
    follow_code, follow_name = get_follow_code_refactored(
        row['DEPT_CODE'], row['HOUSE_DOC_CODE'], row['HOUSE_DOC_NAME'])

    if not test_model:
        sql = f"""INSERT INTO follow.plan
                  (DEPT_CODE, HOUSE_DOC_CODE, HOUSE_DOC_NAME)
                  VALUES ('{row['DEPT_CODE']}', '{row['HOUSE_DOC_CODE']}', '{row['HOUSE_DOC_NAME']}')"""
        db(None, None, sql, 'mysql')

    # 添加医生通知（新任务通知）
    doctor_notices.append({
        'code': follow_code,
        'warn_level': 0,
        'voip': False,
        'chief': False,
        'num': 1
    })

    # 更新状态为任务生成
    next_time = get_next_time(False, datetime.now(), custom.settins.timeout_reminder * 24)
    state_updates.append({
        'plan_id': row['ID'],
        'state': '任务生成',
        'down': 0,
        'next_time': next_time
    })


def _process_task_generated_state(row, state_updates, doctor_notices):
    """处理任务生成状态"""
    # 获取随访专员信息
    follow_code, _ = get_follow_code_refactored(
        row['DEPT_CODE'], row['HOUSE_DOC_CODE'], row['HOUSE_DOC_NAME'])

    # 添加医生通知（第一次超时 - 微信通知）
    doctor_notices.append({
        'code': follow_code,
        'warn_level': 1,
        'voip': False,
        'chief': False,
        'num': 1
    })

    # 更新为超时状态
    next_time = get_next_time(False, datetime.now(), custom.settins.serious_timeout_reminder * 24)
    state_updates.append({
        'plan_id': row['ID'],
        'state': '超时',
        'down': 0,
        'next_time': next_time
    })


def _process_timeout_state(row, state_updates, doctor_notices):
    """处理超时状态"""
    # 获取随访专员信息
    follow_code, _ = get_follow_code_refactored(
        row['DEPT_CODE'], row['HOUSE_DOC_CODE'], row['HOUSE_DOC_NAME'])

    # 添加医生通知（严重超时 - 语音通知）
    doctor_notices.append({
        'code': follow_code,
        'warn_level': 2,
        'voip': True,
        'chief': False,
        'num': 1
    })

    # 更新为二次超时状态
    next_time = get_next_time(False, datetime.now(), custom.settins.director_reminder * 24)
    state_updates.append({
        'plan_id': row['ID'],
        'state': '二次超时',
        'down': 0,
        'next_time': next_time
    })


def _process_second_timeout_state(row, state_updates, doctor_notices):
    """处理二次超时状态"""
    # 获取随访专员和主任信息
    follow_code, _ = get_follow_code_refactored(
        row['DEPT_CODE'], row['HOUSE_DOC_CODE'], row['HOUSE_DOC_NAME'])

    # 添加医生通知（严重超时已被行政监管 - 语音通知）
    doctor_notices.append({
        'code': follow_code,
        'warn_level': 3,
        'voip': True,
        'chief': False,
        'num': 1
    })

    # 添加主任通知（严重超时 - 微信通知）
    doctor_notices.append({
        'inpatient_no': row['INPATIENT_NO'],
        'code': row.get('CHIEF_DOC_CODE', ''),
        'warn_level': 3,
        'voip': False,
        'chief': True,
        'num': 1
    })

    # 更新为严重超时状态
    state_updates.append({
        'plan_id': row['ID'],
        'state': '严重超时',
        'down': 0,
        'next_time': None
    })


def _get_patient_phone_list(row):
    """获取患者电话列表"""
    tel_set = set()
    pattern = r'^1\d{10}$'

    if row.get('PRESENT_TEL') and re.match(pattern, str(row['PRESENT_TEL'])):
        tel_set.add(str(row['PRESENT_TEL']))
    if row.get('CONTACT_TEL') and re.match(pattern, str(row['CONTACT_TEL'])):
        tel_set.add(str(row['CONTACT_TEL']))

    return list(tel_set)


def _generate_notification_content(row):
    """生成通知内容"""
    try:
        time_str = f"按{row['OUT_DATE'].strftime('%Y年%m月%d日')}"
    except:
        time_str = '按'

    return (f'尊敬的{row.get("NAME", "")}{row.get("APPELLATION", "")}您好，这里是永州市中心医院智能随访提醒电话。'
            f"{time_str}{row.get('DEPT_NAME', '')}出院随访计划，"
            f"您的主管医生{row.get('HOUSE_DOC_NAME', '')}提醒您。{row.get('CONTENT', '')}。目前已到计划时间，请及时完成随访计划。祝您身体健康")


def _generate_cycle_states(plan_id, plan_row, test_model=False):
    """为周期性随访任务生成所有的状态记录"""
    if not plan_row.get('CYCLE', False):
        return []

    cycle_states = []
    # 使用计划的下次行动时间作为基准时间
    base_time = plan_row.get('NEXT_ACTION_TIME', datetime.now())
    if isinstance(base_time, str):
        try:
            base_time = datetime.strptime(base_time, '%Y-%m-%d %H:%M:%S')
        except:
            base_time = datetime.now()

    hours_interval = plan_row.get('HOURS', 168)  # 默认7天
    max_cycles = custom.settins.number_of_cycles  # 最大周期数

    # 生成所有周期的时间点，一次性插入到state表中
    for cycle in range(1, max_cycles + 1):
        next_time = base_time + timedelta(hours=hours_interval * cycle)
        cycle_states.append({
            'plan_id': plan_id,
            'next_time': next_time.strftime('%Y-%m-%d %H:%M:%S'),
            'state': '待处理',
            'down': 0
        })

    return cycle_states


def _execute_batch_updates(state_updates, notice_to_insert, doctor_notices, test_model):
    """批量执行数据库更新"""
    if not test_model:
        # 更新状态表
        for update in state_updates:
            # 先检查是否存在记录
            existing = db(['ID'], 'follow.state', {'FOLLOW_PLAN_ID': update['plan_id']}, 'mysql')

            if existing.empty:
                # 插入新记录
                sql = f"""INSERT INTO follow.state (FOLLOW_PLAN_ID, NEXT_TIME, STATE, DOWN)
                         VALUES ({update['plan_id']},
                                {f"'{update['next_time']}'" if update['next_time'] else 'NULL'},
                                '{update['state']}', {update['down']})"""
            else:
                # 更新现有记录
                sql = f"""UPDATE follow.state
                         SET NEXT_TIME = {f"'{update['next_time']}'" if update['next_time'] else 'NULL'},
                             STATE = '{update['state']}', DOWN = {update['down']}
                         WHERE FOLLOW_PLAN_ID = {update['plan_id']}"""
            db(None, None, sql, 'mysql')

        # 插入通知历史 - 使用新的notice_history表，包含新增的WARN_TYPE、NUM、IS_SENT和INPATIENT_PHONE字段
        if notice_to_insert:
            values = []
            for notice in notice_to_insert:
                inpatient_phone = notice.get('INPATIENT_PHONE', 'NULL')
                inpatient_phone_str = f"'{inpatient_phone}'" if inpatient_phone and inpatient_phone != 'NULL' else 'NULL'

                # 计算待处理的随访任务条数
                num_tasks = _get_pending_follow_tasks_count(notice.get('DEPT_CODE', ''))

                values.append(f"""('{notice['INPATIENT_NO']}',
                                  '{notice['ID_CARD']}', '短信',
                                  '{notice['COMPLETE'].replace("'", "")}', NOW(),
                                  {notice.get('IS_SENT', 0)}, {inpatient_phone_str},
                                  '患者通知', {num_tasks})""")

            if values:
                sql = f"""INSERT INTO follow.notice_history
                         (INPATIENT_NO, DEDICATED_CODE, NOTICE_TYPE, CONTENT, UPDATE_TIME, IS_SENT, INPATIENT_PHONE, WARN_TYPE, NUM)
                         VALUES {', '.join(values)}"""
                db(None, None, sql, 'mysql')

                # 同时调用消息总线将消息插入到消息总线表中
                for notice in notice_to_insert:
                    try:
                        # 调用消息总线的send_worker方法
                        send_worker.delay(
                            task_type=1,  # 微信服务号消息
                            urgency_level=0,  # 即时发送
                            scheduled_time=None,
                            template_id=43355,  # 默认模板ID，generate
                            template_params={"thing33": {"value": "随访任务"},
                                             "thing4": {"value": "计划任务"},
                                             "number19": {"value": 1}},
                            push_mode=2,  # 按电话号码推送
                            target_id=notice.get('INPATIENT_PHONE', '')
                        )
                    except Exception as e:
                        print(f"调用消息总线失败: {e}")

        # 插入医生通知到notice_history表
        if doctor_notices:
            _insert_doctor_notices(doctor_notices)


def _insert_doctor_notices(doctor_notices):
    """插入医生通知到新数据库的notice_history表"""
    if not doctor_notices:
        return

    try:
        # 生成医生通知内容
        notice_values = []

        for notice in doctor_notices:
            code = notice['code']
            warn_level = notice['warn_level']
            is_chief = notice.get('chief', False)
            is_voip = notice.get('voip', False)
            num = notice.get('num', 1)
            inpatient_no = notice.get('inpatient_no', '')

            # 根据警告级别和类型生成通知内容
            if is_chief:
                # 主任通知
                content = f"随访任务严重超时，主管医生工号：{code}，任务数量：{num}"
                notice_type = '短信'  # 主任通知使用微信模板消息，这里记录为短信
                warn_type = '主任通知'
            else:
                # 普通医生通知
                warn_type_map = {
                    0: '新任务',
                    1: '任务超时',
                    2: '严重超时',
                    3: '严重超时已被行政监管'
                }
                warn_type = warn_type_map.get(warn_level, '未知')
                content = f"随访任务{warn_type}，任务数量：{num}，请尽快登录智慧医疗管理系统中处理。"
                notice_type = '语音' if is_voip else '短信'

            # 构建插入值 - 医生通知不需要住院号，直接把医生工号写到随访专员编号字段
            notice_values.append(f"""('', '{code}', '{notice_type}',
                                     '{content.replace("'", "")}', NOW(), 0, NULL, '{warn_type}', {num})""")

        # 批量插入医生通知
        if notice_values:
            sql = f"""INSERT INTO follow.notice_history
                     (INPATIENT_NO, DEDICATED_CODE, NOTICE_TYPE, CONTENT, UPDATE_TIME, IS_SENT, INPATIENT_PHONE, WARN_TYPE, NUM)
                     VALUES {', '.join(notice_values)}"""
            db(None, None, sql, 'mysql')
            # print(f"成功插入 {len(doctor_notices)} 条医生通知记录到notice_history表")

            # 同时调用消息总线将医生通知插入到消息总线表中
            for notice in doctor_notices:
                try:
                    code = notice['code']
                    warn_level = notice['warn_level']
                    is_chief = notice.get('chief', False)
                    is_voip = notice.get('voip', False)
                    num = notice.get('num', 1)
                    # 获取主管医生姓名
                    df = db(None, None, f"SELECT HNYZ_ZXYY.FUN_GET_EMPL_NAME(28264) AS employee_name FROM dual",
                            'oracle')
                    doctor_name = df['EMPLOYEE_NAME'].iloc[0]

                    # 生成通知内容
                    if is_chief:
                        content = f"随访任务严重超时，工号为{code}的主管医生{doctor_name}，剩余随访任务数量：{num}"
                    else:
                        warn_type_map = {
                            0: '新任务',
                            1: '任务超时',
                            2: '严重超时',
                            3: '严重超时已被行政监管'
                        }
                        warn_type = warn_type_map.get(warn_level, '未知')
                        content = f"随访任务{warn_type}，任务数量：{num}，请尽快登录智慧医疗管理系统中处理。"

                    if is_chief:
                        # 调用消息总线的send_worker方法，主任的
                        send_worker.delay(
                            task_type=1,  # 微信服务号消息
                            optional_content=content,
                            urgency_level=0,  # 即时发送
                            scheduled_time=None,
                            template_id=62022,  # 医生通知模板ID,timeout
                            template_params={"thing8": {"value": "随访任务严重超时"},
                                             "thing5": {"value": doctor_name},  # 主管医生名字
                                             "character_string7": {"value": f"{num}"}},
                            push_mode=0,  # 按工号推送
                            target_id=code
                        )
                    elif is_voip:
                        content_voip = f"随访任务严重超时，工号为{code}的主管医生{doctor_name}，剩余随访任务数量：{num}"
                        # 调用消息总线的send_worker方法，最高等级通知行政，语音
                        send_worker.delay(
                            task_type=2,  # TTS消息
                            optional_content=content_voip,
                            urgency_level=0,  # 即时发送
                            scheduled_time=None,
                            push_mode=0,  # 按工号推送
                            target_id=code
                        )
                    else:
                        # 调用消息总线的send_worker方法，医生的
                        send_worker.delay(
                            task_type=1,  # 微信服务号消息
                            optional_content=content,
                            urgency_level=0,  # 即时发送
                            scheduled_time=None,
                            template_id=45417,  # 医生通知模板ID,timeout
                            template_params={"thing3": {"value": "随访任务"},
                                             "thing6": {"value": "严重超时"},
                                             "character_string29": {"value": f"{num}"}},
                            push_mode=0,  # 按工号推送
                            target_id=code
                        )
                except Exception as e:
                    print(f"调用消息总线失败 (医生通知): {e}")

        # 为了兼容现有的follow_sms.py处理逻辑，同时插入到老的follow_dsms表
        # 这样可以确保现有的微信和语音通知功能继续工作
        # _insert_to_legacy_dsms_table(doctor_notices)

    except Exception as e:
        print(f"插入医生通知失败: {e}")


def _get_pending_follow_tasks_count(dept_code):
    """计算指定科室待处理的随访任务条数"""
    try:
        sql = f"""SELECT COUNT(1) as cnt
                  FROM follow.plan p
                  LEFT JOIN follow.state s ON s.FOLLOW_PLAN_ID = p.ID
                  WHERE p.DEPT_CODE = '{dept_code}'
                    AND (s.DOWN = 0 OR s.DOWN IS NULL)
                    AND s.STATE IN ('待处理', '通知', '任务生成', '超时', '二次超时')"""
        result = db(None, None, sql, 'mysql')
        return result.iloc[0]['cnt'] if not result.empty else 1
    except Exception as e:
        print(f"计算待处理任务数量失败: {e}")
        return 1  # 默认返回1


def _insert_cycle_states(cycle_states, test_model=False):
    """批量插入周期性状态记录"""
    if not cycle_states:
        return

    if test_model:
        print(f"[测试模式] 将插入 {len(cycle_states)} 个周期状态记录")
        for state in cycle_states:
            print(f"  - 计划ID: {state['plan_id']}, 时间: {state['next_time']}, 状态: {state['state']}")
        return

    try:
        # 批量插入周期状态，分批处理避免SQL过长
        batch_size = 100
        for i in range(0, len(cycle_states), batch_size):
            batch = cycle_states[i:i + batch_size]
            values = []
            for state in batch:
                values.append(f"""({state['plan_id']}, '{state['next_time']}',
                                  '{state['state']}', {state['down']})""")

            if values:
                sql = f"""INSERT INTO follow.state (FOLLOW_PLAN_ID, NEXT_TIME, STATE, DOWN)
                         VALUES {', '.join(values)}"""
                db(None, None, sql, 'mysql')

        print(f"成功插入 {len(cycle_states)} 个周期状态记录")

    except Exception as e:
        print(f"插入周期状态记录失败: {e}")


if __name__ == '__main__':
    start()
