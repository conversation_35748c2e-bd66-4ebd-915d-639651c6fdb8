# -*- coding: utf-8 -*-
import ast
import asyncio
import datetime
import re


from alibabacloud_dyplsapi20170525.client import Client as Dyplsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dyplsapi20170525 import models as dyplsapi_20170525_models
from alibabacloud_tea_util import models as util_models

from custom.db.mysql import UsingMysql


class Privacy:
    def __init__(self):
        pass

    @staticmethod
    def create_client() -> Dyplsapi20170525Client:
        """
        使用AK&SK初始化账号Client
        @return: Client
        @throws Exception
        """
        with UsingMysql() as um:
            ali_m = um.fetch_decrypt("select value from info_db where index_information = 'ali_yzszxyy' ", None,
                                     'value')
        ali_params = ast.literal_eval(ali_m)
        # 阿里登录key
        config = open_api_models.Config(access_key_id=ali_params['id'], access_key_secret=ali_params['key'])
        # Endpoint 请参考 https://api.aliyun.com/product/Dyplsapi
        config.endpoint = f'dyplsapi.aliyuncs.com'
        return Dyplsapi20170525Client(config)

    @staticmethod
    async def bind_privacy_async(doctor_phone: str, patient_phone: str, time: int):
        client = Privacy.create_client()
        expiration = (datetime.datetime.now() + datetime.timedelta(minutes=time)).strftime("%Y-%m-%d %H:%M:%S")
        bind_axb_request = dyplsapi_20170525_models.BindAxbRequest(
            pool_key='FC100000181368019',
            phone_no_a=doctor_phone,
            phone_no_b=patient_phone,
            phone_no_x='16742737850',
            expiration=expiration,
            call_restrict='CONTROL_BX_DISABLE'
        )
        runtime = util_models.RuntimeOptions()
        try:
            # 复制代码运行请自行打印 API 的返回值
            resp = await client.bind_axb_with_options_async(bind_axb_request, runtime)
            if resp.body.code == 'isv.BIND_CONFLICT':  # 绑定冲突
                match = re.search(r'id=(\d+)', resp.body.message)
                if match:
                    id_value = match.group(1)
                    # 解绑
                    unbind_subscription_request = dyplsapi_20170525_models.UnbindSubscriptionRequest(
                        pool_key='FC100000181368019',
                        subs_id=id_value,
                        secret_no='16742737850'
                    )
                    await client.unbind_subscription_with_options_async(unbind_subscription_request, runtime)
                    # 绑定
                    resp = await client.bind_axb_with_options_async(bind_axb_request, runtime)
            return vars(resp.body.secret_bind_dto) if resp.body.code == 'OK' else {}
        except Exception as error:
            print(error)
            return {}


if __name__ == '__main__':
    test = asyncio.run(Privacy.bind_privacy_async('18974680622', '15074620622', 10))
    # patient_phone = test.get('extension', '')  # 患者号码
    # privacy_phone = test.get('secret_no', '')  # 隐私号码
    # subs_id = test.get('subs_id', '')  # 绑定ID，用于通话记录查询

    print(test)
