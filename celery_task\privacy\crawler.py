import base64
import json
import os
from datetime import datetime
from typing import Tuple, Union

import requests
import random
import time

from celery_task.celery import app
from celery_task.privacy.common import create_retry_session, recognize_text, generate_batch_insert
from custom.db.execute import db
from custom.db.redis import redis_client

TIMEOUT = (3.05, 27)  # 连接超时3.05s，读取超时27s
SSL_VERIFY = True  # 生产环境必须验证SSL证书
CA_BUNDLE = True  # CA证书路径 '/path/to/cert.pem'（或设置为True使用系统默认）

common_session = create_retry_session()  # web公共session
common_headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0',
    'sec-ch-ua-platform': '"Windows"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua": '"Chromium";v="136", "Microsoft Edge";v="136", "Not.A/Brand";v="99"'
}  # 通用设置


def session(url, headers=None, cookies: dict = None, method: str = 'get', payload=None, download_file=None):
    try:
        post_kwargs = {
            'url': url,
            'verify': CA_BUNDLE if SSL_VERIFY else False,
            'timeout': TIMEOUT,
            'headers': common_headers,
        }
        if headers:
            post_kwargs['headers'].update(headers)
        if cookies:
            post_kwargs['cookies'] = cookies
        if download_file:
            post_kwargs['stream'] = True
        if payload and method == 'post_data':
            post_kwargs['data'] = payload
            post_kwargs['timeout'] = (post_kwargs['timeout'][0], None)
        if payload and method == 'post_json':
            post_kwargs['json'] = payload
        if payload and method == 'get':
            post_kwargs['params'] = payload

        response = common_session.get(**post_kwargs) if method == 'get' else common_session.post(**post_kwargs)
        response.raise_for_status()  # 自动触发HTTP错误异常
        content_type = response.headers.get('Content-Type', '')
        if 'application/json' in content_type:
            try:
                json_response = response.json()
                return {'success': True,
                        'msg': json_response,
                        'cookies': {cookie.name: cookie.value for cookie in common_session.cookies}}
            except json.JSONDecodeError:
                return {'success': False, 'msg': '响应包含无效的JSON格式'}
        elif 'image/jpeg' in content_type:
            base64_image = base64.b64encode(response.content).decode('utf-8')
            return {'success': True, 'msg': base64_image,
                    'cookies': {cookie.name: cookie.value for cookie in common_session.cookies}}
        elif 'text/html' in content_type:
            return {'success': True,
                    'msg': response.text,
                    'cookies': {cookie.name: cookie.value for cookie in common_session.cookies}}
        elif 'application/octet-stream' in content_type and download_file:
            try:
                file_path = os.path.join(download_file['path'], download_file['name'])
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            f.flush()
                return {'success': True,
                        'msg': f"文件已保存到：{file_path}",
                        'cookies': {cookie.name: cookie.value for cookie in common_session.cookies}}
            except Exception as e:
                return {'success': False, 'msg': f'录音文件下载失败: {str(e)}'}
        else:
            return {'success': False, 'msg': f'未知响应类型: {content_type}'}

    except requests.exceptions.SSLError as e:
        return {'success': False, 'msg': 'SSL证书验证失败'}
    except requests.exceptions.ConnectionError as e:
        return {'success': False, 'msg': f"连接失败: {str(e)}"}
    except requests.exceptions.Timeout as e:
        return {'success': False, 'msg': f"请求超时: {str(e)}"}
    except requests.exceptions.HTTPError as e:
        return {'success': False, 'msg': f"HTTP错误: {str(e)}"}
    except requests.exceptions.RequestException as e:
        return {'success': False, 'msg': f"请求异常: {str(e)}"}
    except Exception as e:
        return {'success': False, 'msg': f"未知异常: {str(e)}"}


def login() -> dict:
    """ 登录并返回
    :return {'success': True,
             'cookies': {'SESSION': 'NTQ1ODUyMDAtMjMyYS00MmFkLTkwMDgtMWExN2M5MzhlYzBh'},
             'msg': verif.get('msg', '')}"""
    cookies = {}
    max_retries = 10
    current_retry = 0
    verif = ''

    while current_retry < max_retries:
        time.sleep(random.uniform(0.2, 1.0))
        timestamp = int(time.time() * 1000)  # 13位毫秒级时间戳
        url = f'https://www.hn-yzj.cn/authserver/getcode?{timestamp}'  # 时间戳作为防缓存参数
        image = session(url)
        if not image['success']:
            print(f"验证码获取失败: {image['msg']}")
            break
        cookies = image.get('cookies', cookies)
        verif = recognize_text(image['msg'])
        if verif['success']:
            break
        current_retry += 1
    if not verif.get('data', ''):
        return {'success': False, 'msg': verif.get('msg', '')}
    login_result = session(
        'https://www.hn-yzj.cn/authserver/login',
        payload=f"username=07468320999&password=791403E8FBA478333CB84492208541D5&vcode={verif.get('data', '')}&loginType=1&sms=",
        method='post_data',
        headers={'Sec-Fetch-Site': 'same-origin',
                 'Sec-Fetch-Mode': 'cors',
                 'Sec-Fetch-Dest': 'empty',
                 'X-Requested-With': 'XMLHttpRequest',
                 'Accept': 'application/json, text/javascript, */*; q=0.01',
                 'Referer': 'https://www.hn-yzj.cn/authserver/login',
                 'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                 'Origin': 'https://www.hn-yzj.cn'})
    if login_result.get('msg', {}).get('status', '') != 'success':
        return {'success': False, 'msg': str(login_result.get('msg', {}))}

    session('https://www.hn-yzj.cn/cloud/',
            headers={'Sec-Fetch-Mode': 'navigate',
                     'Sec-Fetch-User': '?1',
                     'Sec-Fetch-Dest': 'document',
                     'Upgrade-Insecure-Requests': '1',
                     'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
                     'Accept-Encoding': 'gzip, deflate, br, zstd',
                     'X-Requested-With': '',
                     'Content-Type': '',
                     'Origin': '',
                     'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6'})
    authorization = session('https://www.hn-yzj.cn/cloud/oauth2/authorization/MemberSystem')
    if authorization.get('success', False):
        cookies = authorization.get('cookies', {})
        redis_client.set('privacy_modify_cookies', json.dumps(cookies))
        return {'success': True, 'cookies': cookies}
    else:
        return {'success': False, 'msg': str(authorization.get('msg', {}))}


def retry_session(kwargs, max_retries=5) -> Tuple[bool, Union[str, dict]]:
    """ 带cookies获取、更新，重试机制的session """
    for attempt in range(max_retries):
        # 获取或更新cookies
        if attempt > 0 or not redis_client.exists('privacy_modify_cookies'):
            login_result = login()
            if not login_result.get('success', False):
                continue  # 登录失败继续重试
            cookies = login_result.get('cookies', {})
            redis_client.set('privacy_modify_cookies', json.dumps(cookies))
        else:
            cookies = json.loads(redis_client.get('privacy_modify_cookies'))
        # 发送请求
        kwargs['cookies'] = cookies
        result = session(**kwargs)
        # 结果验证
        if not isinstance(result.get('msg', {}), str):
            return True, result
    return False, f'{max_retries}次尝试后仍然失败'


@app.task
def log():
    """ 用于历史记录获取 """
    start_time = db('value', 'yzzxyy.info_db',
                    {'index_information': 'privacy_crawler_time'}, 'mysql')
    formatted_time = datetime.now().strftime("%Y-%m-%d+%H:%M:%S")

    kwargs = {
        'url': "https://www.hn-yzj.cn/cloud/ent/call/list",
        'payload': {"p": 1,  # 页码
                    "s": 100,  # 每页数量
                    "beg": start_time,  # 开始时间
                    "end": formatted_time,  # 结束时间
                    "hjbs": 1,  # 呼叫结果 0全部 1呼出 2呼入
                    "hjjg": 0,  # 通话结果 0全部 1成功 2失败 3漏话
                    "talkOperate": 0},
        'headers': {'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    "Accept": "application/json, text/plain, */*",
                    "Referer": "https://www.hn-yzj.cn/cloud/home/<USER>",
                    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
                    'Origin': 'https://www.hn-yzj.cn'}}

    success, call_list = retry_session(kwargs)
    if not success:
        raise ValueError(call_list)

    filtered_list = []
    for d in call_list.get('msg', {}).get('data', []):
        filtered_list.append({k: d[k] for k in (
            'USER_ID',  # √ 修改分机号绑定号码的时候要用
            'BILL_PHONE',  # √ 修改分机号绑定号码的时候要用
            'EXT_NO',  # 分机号

            'CALL_FLAG',  # 呼叫类型  1呼出 2 呼入
            'START_TIME',  # 通话开始时间
            'END_TIME',  # 通话结束时间
            'DURATION_TIME',  # √ 通话长度 96
            'CALL_RESULT',  # √ 呼叫结果  # 1成功 2失败 3漏话

            'RECORD_FILE',  # √ 录音文件 '07468333120/20250605/20250605121339_1426325670_07468333120_18874613900.wav'
            'CALL_ID',  # '20250605121339_1426325670' 录音下载后面的位置1  通话唯一ID
            'CALLER',  # 主叫号码  录音下载后面的位置2  '07468333120'
            'CALLED',  # 被叫号码  录音下载后面的位置3  '18874613900'
        )})

        # 对获取筛选后记录进行更新（因为通话记录没有保存原始绑定的号码，只显示了外呼号码）
        affect = db(None, None,
                    f"""UPDATE yzzxyy.follow_call
                        SET CALL_ID = '{d['CALL_ID']}'
                        WHERE CALL_2 = '{d['CALLED']}'
                          AND TIME BETWEEN '{d['START_TIME']}' - INTERVAL 1 HOUR
                            AND '{d['END_TIME']}'
                            AND CALL_ID IS NULL""", 'mysql')
        if int(affect) > 0:
            # 更新绑定表的目前绑定数量，需先检查是否是主动绑定的呼叫
            db(None, None,
               f""" UPDATE yzzxyy.follow_bind
                            SET BINDING_TIME = CURRENT_TIMESTAMP, CAPTURE = CAPTURE - 1
                            WHERE USER_ID = '{d['USER_ID']}'  """, 'mysql')

        # 录音文件下载
        if not d['RECORD_FILE']:
            continue
        kwargs = {'url': "https://www.hn-yzj.cn/cloud/download/callFile",
                  'download_file': {'path': r'/app/record', 'name': f'{d["CALL_ID"]}.wav'},
                  'headers': {'Sec-Fetch-Site': 'same-origin',
                              'Sec-Fetch-Mode': 'navigate',
                              'Sec-Fetch-User': '?1',
                              'Sec-Fetch-Dest': 'document',
                              'Upgrade-Insecure-Requests': '1',
                              "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
                              "Referer": "https://www.hn-yzj.cn/cloud/home/<USER>"},
                  'payload': {"path": d['RECORD_FILE'],
                              "fileName": f"{d['CALL_ID']}_{d['CALLER']}_{d['CALLED']}.wav"},
                  }
        suc, msg = retry_session(kwargs)
        if not suc:
            print(msg)

    if filtered_list:
        sql_statements = generate_batch_insert(filtered_list, 'yzzxyy.follow_record', 'CALL_ID', 10)
        for sql in sql_statements:
            db(None, None, sql, 'mysql')
    db(None, None,
       f"""UPDATE yzzxyy.info_db SET value = '{formatted_time}' WHERE index_information = 'privacy_crawler_time'""",
       'mysql')


@app.task
def update_number_pool():
    """ 更新电信号码池 """
    kwargs = {'url': "https://www.hn-yzj.cn/cloud/ext/em/list",
              'payload': {"p": 1, "s": 100,  # 每页数量
                          },
              'headers': {'Sec-Fetch-Site': 'same-origin',
                          'Sec-Fetch-Mode': 'cors',
                          'Sec-Fetch-Dest': 'empty',
                          "Accept": "application/json, text/plain, */*",
                          "Referer": "https://www.hn-yzj.cn/cloud/home/<USER>",
                          }}
    success, call_list = retry_session(kwargs)
    if not success:
        raise ValueError(call_list)

    filtered_list = [
        {k: d.get(k, '') for k in (
            'USER_ID',  # √ 修改分机号绑定号码的时候要用
            'BINDING_PHONE',  # √ 修改分机号绑定号码的时候要用
            'EXT_NO',  # 分机号
            'MOBILE_PHONE',  # 主叫号码
            'USER_NAME',  # 别名
            'USER_TYPE',  # = {int} 3
        )}
        for d in call_list.get('msg', {}).get('page', {}).get('data', [])]

    if filtered_list:
        sql_statements = generate_batch_insert(filtered_list, 'yzzxyy.follow_bind', 'USER_ID', 10)
        for sql in sql_statements:
            db(None, None, sql, 'mysql')
