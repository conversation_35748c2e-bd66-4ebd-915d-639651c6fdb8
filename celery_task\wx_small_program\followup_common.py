import json, re
import pandas
from typing import Any
from typing import Dict, List
from celery_task.wx_small_program.address_utils import get_address


def transform_db_data_enhanced(db_string: str) -> dict:
    """
    参数:
        db_string: 数据库中的原始字符串
        例如:
        - "{'已完成': ['在外院完成', '其他'], '未完成':['拒绝完成', '其他']}"
        - "{'患者有问题无法解答': [], '患者要求主管医生联系': [], '其他': []}"
    返回:
        转换后的结构化字典
    """
    try:
        # 安全转换字符串为字典
        corrected_json = re.sub(r',(\s*})', r'\1', db_string)
        json_str = corrected_json.replace("'", '"')
        raw_data: Dict[str, List[str]] = json.loads(json_str)
    except (json.JSONDecodeError, AttributeError) as e:
        raise ValueError(f"无效的数据库字符串格式: {e}")

    # 预设ID映射表（可根据需要扩展）
    ID_MAPPING = {
        # 主类别映射
        "已完成": "completed",
        "未完成": "uncompleted",
        "患者有问题无法解答": "patient_questions",
        "患者要求主管医生联系": "request_doctor",
        "联系方式错误": "contact_error",
        "患者拒绝回访": "refused_revisit",
        "患者不适宜回访": "patient_unsuitable",
        "存活": "alive",
        "死亡": "dead",
        "其他": "other",

        # 子选项映射
        "在外院完成": "external",
        "拒绝完成": "refused",
        "其他": "other"
    }

    def generate_id(label: str, parent_id: str = None, index: int = None) -> str:
        """
        智能生成ID:
        1. 先检查映射表
        2. 无映射则生成小写+下划线格式
        3. 确保ID唯一性
        """
        # 优先使用预设映射
        if label in ID_MAPPING:
            base_id = ID_MAPPING[label]
        else:
            # 生成小写+下划线格式ID
            base_id = label.lower().replace(" ", "_")

        # 如果是子选项且需要确保唯一性
        if parent_id:
            if not any(base_id in v for v in ID_MAPPING.values()):
                return f"{parent_id}_{base_id}"
            return f"{base_id}_{parent_id}" if index is None else f"{base_id}_{parent_id}_{index}"
        return base_id

    # 构建最终结构
    result = {"options": []}

    for category_label, children_labels in raw_data.items():
        parent_id = generate_id(category_label)

        # 处理子选项
        children = []
        if children_labels:  # 只有非空时才处理
            for idx, child_label in enumerate(children_labels, 1):
                children.append({
                    "id": generate_id(child_label, parent_id, idx),
                    "label": child_label
                })
        else:
            pass

        result["options"].append({
            "id": parent_id,
            "label": category_label,
            "children": children
        })

    return result

def normalize_to_list(value: Any) -> list:
    """
    将输入值规范化为列表：
    - 如果是 JSON 字符串，如 '["123"]' → 解析成列表
    - 如果是普通字符串，如 "123" → 返回 ["123"]
    - 如果是 None → 返回 []
    - 如果是列表 → 原样返回
    - 其他类型 → 转为字符串后放入列表
    """
    if value is None:
        return []

    if isinstance(value, list):
        return value

    if isinstance(value, str):
        try:
            parsed = json.loads(value)
            if isinstance(parsed, list):
                return parsed
            else:
                return [str(parsed)]
        except json.JSONDecodeError:
            return [value]

    return [str(value)]


def _tag(keys: list) -> str:
    """ 拼接字段，形成 HTML 文本 """
    styles = {
        'p': f'<p style="line-height: 1.5;">',
        's': f'<strong>',
        'C': '<span style="font-size: 18pt; color: #e03e2d;">',
        '/s': '</strong>',
        '/C': '</span>',
        '/p': '</p >',
    }
    result = ''.join([styles.get(key, '') for key in keys])
    return result


def get_basic_information(data_basic: pandas.Series) -> str:
    """ 随访病人基本信息 """
    result = _tag(['p', 's']) + "姓名：" + _tag(['C']) + data_basic['姓名'] + _tag(['/C', '/s', '/p'])
    result += _tag(['p', 's']) + '性别：' + _tag(['/s']) + data_basic['性别'] + _tag(['/p'])
    result += _tag(['p', 's']) + '入院年龄：' + _tag(['/s']) + data_basic['入院年龄'] + _tag(['/p'])

    result += _tag(['p', 's']) + '住院号：' + _tag(['/s']) + data_basic['INPATIENT_NO'] + _tag(['/p'])
    result += _tag(['p', 's']) + '住院天数：' + _tag(['/s']) + str(data_basic['住院天数']) + _tag(['/p'])
    result += (_tag(['p', 's']) + '入院日期：' + _tag(['/s'])
               + data_basic['入院时间'].strftime('%Y-%m-%d') + _tag(['/p']))
    result += _tag(['p', 's']) + '入院科室：' + _tag(['/s']) + data_basic['入院科室'] + _tag(['/p'])
    result += (_tag(['p', 's']) + '出院日期：' + _tag(['/s'])
               + data_basic['出院时间'].strftime('%Y-%m-%d') + _tag(['/p']))
    result += _tag(['p', 's']) + '出院科室：' + _tag(['/s']) + data_basic['出院科室'] + _tag(['/p'])
    result += _tag(['p', 's']) + '主管医师：' + _tag(['/s']) + data_basic['主管医生'] + _tag(['/p'])
    result += _tag(['p', 's']) + '总花费：' + _tag(['/s']) + str(data_basic['总花费']) + _tag(['/p'])

    address = get_address(data_basic['住址县'], data_basic['住址市'], data_basic['住址省'])
    result += _tag(['p', 's']) + '住址：' + _tag(['/s']) + address + data_basic['住址其他'].replace(address, '')
    result += _tag(['p', 's']) + '身份证号：' + _tag(['/s']) + data_basic['身份证'] + _tag(['/p'])
    return result


def get_admission_information(data_homepage: pandas.DataFrame, data_element: pandas.DataFrame) -> str:
    """ 随访病人入院信息 """
    result = ''

    # 首页诊断信息
    if data_homepage.empty:
        result += _tag(['p', 's', 'C']) + '注意：首页未填写' + _tag(['/C', '/s', '/p'])
    try:
        outpatient_diagnosis_main = data_homepage[(data_homepage['诊断类型'] == '门诊诊断') &
                                                  (data_homepage['主诊断'] == 1)]['诊断名称'].iloc[0]
        result += _tag(['p', 's']) + '门诊主诊断：' + _tag(['/s']) + outpatient_diagnosis_main + _tag(['/p'])
    except IndexError:
        pass
    try:
        inpatient_diagnosis = data_homepage[(data_homepage['诊断类型'] == '入院诊断')]['诊断名称'].tolist()
        inpatient_diagnosis = [f"{_tag(['/p', 'p'])}&nbsp; &nbsp; {str(i + 1)}、{val}"
                               for i, val in enumerate(inpatient_diagnosis)]
        result += (_tag(['p', 's']) + '入院诊断：' + _tag(['/s', '/p'])
                   + _tag(['p']) + ''.join(inpatient_diagnosis) + _tag(['/p']))
    except IndexError:
        pass

    # 文书病史信息
    try:
        result += (_tag(['p', 's']) + '主诉：' + _tag(['/s'])
                   + data_element[data_element['ELEMENT_NAME'] == '主诉']['VALUE'].iloc[0] + _tag(['/p']))
    except IndexError:
        pass
    try:
        past_history = (data_element[data_element['ELEMENT_NAME'] == '既往史']['VALUE'].iloc[0]
                        .replace("过敏史：", f"{_tag(['/p', 'p', 's'])}过敏史：{_tag(['/s'])}")
                        .replace("手术史：", f"{_tag(['/p', 'p', 's'])}手术史：{_tag(['/s'])}")
                        .replace("输血史：", f"{_tag(['/p', 'p', 's'])}输血史：{_tag(['/s'])}")
                        .replace("预防接种史：", f"{_tag(['/p', 'p', 's'])}预防接种史：{_tag(['/s'])}")
                        .replace("外伤史：", f"{_tag(['/p', 'p', 's'])}外伤史：{_tag(['/s'])}"))
        result += _tag(['p', 's']) + '现病史：' + _tag(['/s']) + past_history + _tag(['/p'])
    except IndexError:
        pass
    return result


def get_discharge_information(data_basic: pandas.Series,
                              data_homepage: pandas.DataFrame,
                              data_element: pandas.DataFrame) -> str:
    """ 随访病人出院信息 """
    result = ''
    if data_homepage.empty:
        result += _tag(['p', 's', 'C']) + '注意：首页未填写' + _tag(['/C', '/s', '/p'])
    try:
        conversion = data_element[data_element['ELEMENT_NAME'] == '患者转归']['VALUE'].iloc[0]
    except IndexError:
        conversion = data_basic['离院方式']
    try:
        conversion_add = data_element[data_element['ELEMENT_NAME'] == '患者转至']['VALUE'].iloc[0]
    except IndexError:
        conversion_add = data_basic['接收机构'] or data_basic['接收社区']
    result = (_tag(['p', 's']) + '转归：' + _tag(['/s']) +
              _tag(['/p', 'p']).join([conversion or '', conversion_add or '']) + _tag(['/p']))

    try:
        discharge_diagnosis = data_homepage[(data_homepage['诊断类型'] == '入院诊断')]['诊断名称'].tolist()
        discharge_diagnosis = [f"{_tag(['/p', 'p'])}&nbsp; &nbsp; {str(i + 1)}、{val}"
                               for i, val in enumerate(discharge_diagnosis)]
        result += (_tag(['p', 's']) + '出院诊断：' + _tag(['/s', '/p'])
                   + _tag(['p']) + ''.join(discharge_diagnosis) + _tag(['/p']))
    except IndexError:
        pass

    try:
        advice = data_element[data_element['ELEMENT_NAME'] == '出院医嘱']['VALUE'].iloc[0]
        result += (_tag(['p', 's']) + '出院医嘱：' + _tag(['/s', '/p']) + '&nbsp; &nbsp; '
                   + advice.replace('\n', f"{_tag(['/p', 'p'])}&nbsp; &nbsp; "))
    except IndexError:
        pass
    try:
        plan = data_element[data_element['ELEMENT_NAME'] == '随访计划']['VALUE'].iloc[0]
        result += (_tag(['p', 's']) + '随访计划：' + _tag(['/s', '/p']) + '&nbsp; &nbsp; '
                   + plan.replace('\n', f"{_tag(['/p', 'p'])}&nbsp; &nbsp; "))
    except IndexError:
        pass

    return result


def make_json_safe_html(html: str) -> str:
    """
    将 HTML 字符串转换为可嵌入 JSON 的合法字符串。
    保留 HTML 标签，转义双引号和换行。
    """
    return html.strip().replace('\\', '\\\\').replace('"', '\\"').replace('\n', '\\n')