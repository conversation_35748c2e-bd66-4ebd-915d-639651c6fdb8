import json
import pandas as pd
import re
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app
from typing import Union, List, Dict
from datetime import datetime
# from openai import OpenAI  # 注释掉，改用本地AI
import time
import threading
import queue
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from dataclasses import dataclass

# 超时异常类
class TimeoutError(Exception):
    pass

# 多线程配置
@dataclass
class ThreadConfig:
    max_workers: int = 5  # 最大工作线程数
    queue_size: int = 10  # 任务队列大小
    ai_rate_limit: float = 2.0  # AI调用间隔（秒）
    db_timeout: float = 10.0  # 数据库操作超时
    retry_attempts: int = 3  # 重试次数

# 全局配置实例
config = ThreadConfig()

# 线程安全的计数器和锁
class ThreadSafeCounter:
    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()

    def increment(self):
        with self._lock:
            self._value += 1
            return self._value

    def get(self):
        with self._lock:
            return self._value

# 全局统计
stats = {
    'processed': ThreadSafeCounter(),
    'success': ThreadSafeCounter(),
    'failed': ThreadSafeCounter()
}

# AI调用限流器
class RateLimiter:
    def __init__(self, rate_limit: float):
        self.rate_limit = rate_limit
        self.last_call = 0
        self.lock = threading.Lock()

    def acquire(self):
        with self.lock:
            now = time.time()
            time_since_last = now - self.last_call
            if time_since_last < self.rate_limit:
                sleep_time = self.rate_limit - time_since_last
                time.sleep(sleep_time)
            self.last_call = time.time()

# 全局限流器
ai_limiter = RateLimiter(config.ai_rate_limit)

# 数据库操作锁（确保数据库写入的线程安全）
db_lock = threading.Lock()

# 注释掉公网AI客户端初始化，改用本地AI
# client = OpenAI(
#     api_key="sk-uiuzrfenqtzwvwycvrklaookndhuktnwkjnmuvtfplwjccld",
#     base_url="https://api.siliconflow.cn/v1",
#     timeout=30.0  # 设置30秒超时
# )




def process_patient_treatments():
    """
    主任务函数：处理患者治疗记录，使用AI判断治疗项目并更新最早时间（单线程版本）
    """
    try:
        # 步骤1: 查询所有Earliest_Time为空的记录
        sql = """
        SELECT ID, INPATIENT_NO, OPERATION_DATE, type, ITEM_NAME
        FROM ai.test_patient_treatments
        WHERE Earliest_Time IS NULL AND ITEM_NAME IS NOT NULL
        """

        # 执行查询
        records_df = db(None, None, sql, 'mysql')

        if records_df.empty:
            print("未找到Earliest_Time为空的记录")
            return

        print(f"找到{len(records_df)}条需要处理的记录")

        # 一条一条处理记录
        for index, row in records_df.iterrows():
            print(f"正在处理第{index+1}条记录 (ID: {row['ID']})...")

            # 直接处理单条记录并立即写入数据库
            success = process_single_record(row)

            if success:
                print(f"✓ 记录 ID={row['ID']} 处理成功")
            else:
                print(f"✗ 记录 ID={row['ID']} 处理失败")

            print("-----------------------------------------")

            # 添加短暂延迟，避免AI服务过载
            import time
            time.sleep(1)

        print("患者治疗记录处理完成")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def process_patient_treatments_multithreaded(max_workers: int = None):
    """
    多线程版本：处理患者治疗记录，使用AI判断治疗项目并更新最早时间

    Args:
        max_workers: 最大工作线程数，默认使用配置值
    """
    if max_workers:
        config.max_workers = max_workers

    try:
        # 步骤1: 查询所有需要处理的记录
        sql = """
        SELECT ID, INPATIENT_NO, OPERATION_DATE, type, ITEM_NAME
        FROM ai.test_patient_treatments
        WHERE Earliest_Time IS NULL AND ITEM_NAME IS NOT NULL
        """

        records_df = db(None, None, sql, 'mysql')

        if records_df.empty:
            print("未找到Earliest_Time为空的记录")
            return

        total_records = len(records_df)
        print(f"找到{total_records}条需要处理的记录，使用{config.max_workers}个线程处理")

        # 重置统计
        stats['processed'] = ThreadSafeCounter()
        stats['success'] = ThreadSafeCounter()
        stats['failed'] = ThreadSafeCounter()

        start_time = time.time()

        # 使用线程池处理记录
        with ThreadPoolExecutor(max_workers=config.max_workers) as executor:
            # 提交所有任务
            future_to_record = {
                executor.submit(process_single_record_threadsafe, row): row
                for _, row in records_df.iterrows()
            }

            # 处理完成的任务
            for future in as_completed(future_to_record):
                row = future_to_record[future]
                processed_count = stats['processed'].increment()

                try:
                    success = future.result()
                    if success:
                        stats['success'].increment()
                        print(f"✓ 记录 ID={row['ID']} 处理成功 ({processed_count}/{total_records})")
                    else:
                        stats['failed'].increment()
                        print(f"✗ 记录 ID={row['ID']} 处理失败 ({processed_count}/{total_records})")

                except Exception as e:
                    stats['failed'].increment()
                    print(f"✗ 记录 ID={row['ID']} 处理异常: {str(e)} ({processed_count}/{total_records})")

                # 每处理10条记录输出一次进度
                if processed_count % 10 == 0:
                    success_count = stats['success'].get()
                    failed_count = stats['failed'].get()
                    print(f"进度: {processed_count}/{total_records}, 成功: {success_count}, 失败: {failed_count}")

        # 输出最终统计
        end_time = time.time()
        execution_time = end_time - start_time
        success_count = stats['success'].get()
        failed_count = stats['failed'].get()

        print(f"多线程处理完成!")
        print(f"总记录数: {total_records}")
        print(f"处理成功: {success_count}")
        print(f"处理失败: {failed_count}")
        print(f"执行时间: {execution_time:.2f}秒")
        print(f"平均处理速度: {total_records/execution_time:.2f}条/秒")

    except Exception as e:
        print(f"多线程处理过程中发生错误: {str(e)}")
        raise


def process_single_record_threadsafe(row) -> bool:
    """
    线程安全版本：处理单条记录，调用AI分析并立即写入数据库
    :param row: 单条患者治疗记录
    :return: 是否处理成功
    """
    thread_name = threading.current_thread().name
    record_id = row['ID']

    try:
        # 应用AI调用限流
        ai_limiter.acquire()

        operation_date = row['OPERATION_DATE'] if pd.notna(row['OPERATION_DATE']) else "无"

        # 构建AI提示词
        prompt = f"""
        你是一名医生，请严格按以下规则获取对应的"治疗项目"中的首次特定治疗对应的时间：
    1.特定治疗指代：放疗、化疗、靶向治疗、内分泌治疗、免疫治疗
    2.要求输出特定治疗对应的最早时间
    3 未找到特定治疗时，结果输出为1970-01-01 00:00:00
    5. 最终严格按照下面输出要求返回。
患者信息：
- 记录ID: {record_id}
- 治疗项目: {row['ITEM_NAME']}
请返回JSON格式结果：
{{"id": {record_id}, "earliest_time": 首次特定治疗对应的时间}}
直接给出JSON结果："""

        # 调用AI服务（带超时处理）
        try:
            # response = client.chat.completions.create(
            #     model="Qwen/Qwen3-8B",
            #     messages=[
            #         {"role": "system", "content": "You are a helpful assistant designed to output JSON."},
            #         {"role": "user", "content": prompt}
            #     ],
            #     response_format={"type": "json_object"},
            #     timeout=30.0  # 单次请求30秒超时
            # )
            import requests
            response = requests.post(
                "http://192.168.186.168:12434/api/generate",
                json={
                    "model": 'qwen3:8b-q4_K_M',
                    "prompt": prompt,
                    "stream": False,
                     "think": True
                }
            )
        except Exception as e:
            if "timeout" in str(e).lower():
                print(f"[{thread_name}] AI请求超时 (ID: {record_id}): {str(e)}")
                raise TimeoutError(f"AI请求超时: {str(e)}")
            else:
                raise e

        # 处理AI响应
        # 本地AI响应格式处理
        if hasattr(response, 'json'):
            response_data = response.json()
            ai_output = response_data.get('response', '').strip()
        else:
            # 如果是公网AI响应格式
            ai_output = response.choices[0].message.content.strip()
        print(f"[{thread_name}] AI响应 (ID: {record_id}): {ai_output}")

        # 解析响应
        result = parse_single_treatment_response(ai_output)

        if result and result.get('earliest_time'):
            # 线程安全的数据库写入
            earliest_time = result['earliest_time']
            success = update_record_threadsafe(record_id, earliest_time)

            if success:
                print(f"[{thread_name}] 成功更新记录 ID={record_id} 的最早时间为 {earliest_time}")
                return True
            else:
                print(f"[{thread_name}] 数据库更新失败 (ID: {record_id})")
                return False
        else:
            print(f"[{thread_name}] 记录 ID={record_id} 未找到相关治疗措施或解析失败")
            return False

    except Exception as e:
        print(f"[{thread_name}] AI服务调用失败 (ID: {record_id}): {str(e)}")
        return False


def update_record_threadsafe(record_id: int, earliest_time: str) -> bool:
    """
    线程安全的数据库更新操作，带重试机制
    :param record_id: 记录ID
    :param earliest_time: 最早时间
    :return: 是否更新成功
    """
    with db_lock:
        max_retries = 3
        retry_delay = 1  # 秒

        for attempt in range(max_retries):
            try:
                update_sql = f"""
                UPDATE ai.test_patient_treatments
                SET Earliest_Time = '{earliest_time}'
                WHERE ID = {record_id}
                """

                db(None, None, update_sql, 'mysql')
                return True

            except Exception as e:
                error_msg = str(e)
                if attempt < max_retries - 1:
                    if "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                        print(f"数据库连接失败 (ID: {record_id}), 第{attempt + 1}次重试中... 错误: {error_msg}")
                        time.sleep(retry_delay)
                        retry_delay *= 2  # 指数退避
                        continue
                    else:
                        print(f"数据库更新失败 (ID: {record_id}): {error_msg}")
                        return False
                else:
                    print(f"数据库更新最终失败 (ID: {record_id}), 已重试{max_retries}次: {error_msg}")
                    return False

        return False


def process_single_record(row) -> bool:
    """
    处理单条记录：调用AI分析并立即写入数据库
    :param row: 单条患者治疗记录
    :return: 是否处理成功
    """
    try:
        operation_date = row['OPERATION_DATE'] if pd.notna(row['OPERATION_DATE']) else "无"

        # 构建AI提示词
        prompt = f"""请分析以下患者治疗记录，判断ITEM_NAME中的治疗项目是否属于{row['type']}的治疗措施：

患者信息：
- 记录ID: {row['ID']}
- 住院号: {row['INPATIENT_NO']}
- 病症类型: {row['type']}
- 手术时间: {operation_date}
- 治疗项目: {row['ITEM_NAME']}

任务要求：
1. 分析ITEM_NAME中的治疗项目，判断哪些项目属于{row['type']}的治疗措施
2. 对于属于治疗措施的项目，提取其中最早的时间
3. 将提取的最早治疗时间与手术时间进行比较，取较早的时间
4. 如果手术时间为空，直接使用治疗项目的最早时间
5. 如果没有找到相关治疗项目，返回手术时间，如果手术时间也没有，就返回'1970-01-01'


注意事项：
- ITEM_NAME格式为：项目名称(时间), 项目名称(时间), ...
- 时间格式为：YYYY-MM-DD HH:MM:SS
- 只考虑与{row['type']}直接相关的放疗、化疗、靶向治疗、内分泌治疗、免疫治疗
- 其他治疗通常不算主要治疗措施
- 取得治疗措施的最早时间后一定要记得跟手术时间对比，取较早的时间

请返回JSON格式结果：
{{"id": {row['ID']}, "earliest_time": "最早相关治疗时间活着手术时间"}}

直接给出JSON结果："""

        # 调用AI服务（带超时处理）
        try:
            # 注释掉公网AI调用
            # response = client.chat.completions.create(
            #     model="Qwen/Qwen3-8B",
            #     messages=[
            #         {"role": "system", "content": "You are a helpful assistant designed to output JSON."},
            #         {"role": "user", "content": prompt}
            #     ],
            #     response_format={"type": "json_object"},
            #     timeout=30.0  # 单次请求30秒超时
            # )

            # 使用本地AI
            import requests
            response = requests.post(
                "http://192.168.186.168:12434/api/generate",
                json={
                    "model": 'qwen3:8b-q4_K_M',
                    "prompt": prompt,
                    "stream": False,
                    "think": True
                }
            )
        except Exception as e:
            if "timeout" in str(e).lower():
                print(f"AI请求超时: {str(e)}")
                raise TimeoutError(f"AI请求超时: {str(e)}")
            else:
                raise e

        # 处理AI响应
        # 本地AI响应格式处理
        if hasattr(response, 'json'):
            response_data = response.json()
            ai_output = response_data.get('response', '').strip()
        else:
            # 如果是公网AI响应格式
            ai_output = response.choices[0].message.content.strip()
        print(f"AI响应: {ai_output}")

        # 解析响应
        result = parse_single_treatment_response(ai_output)

        if result and result.get('earliest_time'):
            # 立即写入数据库
            earliest_time = result['earliest_time']
            update_sql = f"""
            UPDATE ai.test_patient_treatments
            SET Earliest_Time = '{earliest_time}'
            WHERE ID = {row['ID']}
            """

            db(None, None, update_sql, 'mysql')
            print(f"成功更新记录 ID={row['ID']} 的最早时间为 {earliest_time}")
            return True
        else:
            print(f"记录 ID={row['ID']} 未找到相关治疗措施或解析失败")
            return False

    except Exception as e:
        print(f"AI服务调用失败: {str(e)}")
        return False


def parse_single_treatment_response(response_text: str) -> Dict:
    """
    解析单条AI返回的治疗分析响应
    :param response_text: AI返回的文本
    :return: 结构化治疗数据
    """
    try:
        # 尝试直接解析为JSON
        data = json.loads(response_text)
        if isinstance(data, dict):
            return data
        elif isinstance(data, list) and len(data) > 0:
            return data[0]
    except json.JSONDecodeError:
        pass

    # 如果直接解析失败，尝试提取JSON部分
    try:
        # 查找可能的JSON对象
        json_matches = re.findall(r'\{.*?\}', response_text, re.DOTALL)
        if json_matches:
            return json.loads(json_matches[0])
    except Exception as e:
        print(f"JSON解析失败: {str(e)}")

    # 最后尝试手动提取数据
    pattern = r'"id"\s*:\s*(\d+)\s*,\s*"earliest_time"\s*:\s*(?:"([^"]+)"|null)'
    match = re.search(pattern, response_text)

    if match:
        id_val, earliest_time = match.groups()
        return {
            "id": int(id_val),
            "earliest_time": earliest_time if earliest_time else None
        }

    return {}


def update_earliest_times(data: List[Dict]):
    """
    更新最早时间到数据库
    :param data: 更新数据列表 [{"id": 123, "earliest_time": "2024-01-01 10:00:00"}, ...]
    """
    for item in data:
        earliest_time = item['earliest_time']
        if earliest_time:
            update_sql = f"""
            UPDATE ai.test_patient_treatments
            SET Earliest_Time = '{earliest_time}'
            WHERE ID = {item['id']}
            """
        else:
            # 如果没有找到相关治疗，可以选择跳过或设置为特殊值
            print(f"记录 ID={item['id']} 未找到相关治疗措施，跳过更新")
            continue

        try:
            result = db(None, None, update_sql, 'mysql')
            print(f"成功更新记录 ID={item['id']} 的最早时间为 {earliest_time}")
        except Exception as e:
            print(f"更新记录 ID={item['id']} 失败: {str(e)}")


def start_treatment_analysis(use_multithreading: bool = False, max_workers: int = None):
    """
    启动患者治疗分析任务
    :param use_multithreading: 是否使用多线程
    :param max_workers: 最大工作线程数
    """
    if use_multithreading:
        print("使用多线程模式处理患者治疗记录")
        process_patient_treatments_multithreaded(max_workers)
    else:
        print("使用单线程模式处理患者治疗记录")
        process_patient_treatments()


def main_with_retry(max_retries=9999999999999, use_multithreading: bool = True, max_workers: int = 5):
    """
    带重试机制的主函数
    :param max_retries: 最大重试次数
    :param use_multithreading: 是否使用多线程
    :param max_workers: 最大工作线程数
    """
    retry_count = 0
    mode_desc = f"多线程模式(线程数: {max_workers or config.max_workers})" if use_multithreading else "单线程模式"

    while retry_count < max_retries:
        try:
            print(f"开始执行患者治疗分析任务 - {mode_desc} (第{retry_count + 1}次尝试)...")

            # 记录开始时间
            start_time = time.time()

            # 执行主要任务
            start_treatment_analysis(use_multithreading, max_workers)

            # 检查执行时间
            execution_time = time.time() - start_time
            print(f"任务执行时间: {execution_time:.2f}秒")

            print("患者治疗分析任务执行完成！")
            return True

        except TimeoutError as e:
            retry_count += 1
            print(f"执行超时: {str(e)}")

            if retry_count < max_retries:
                print(f"将在5秒后进行第{retry_count + 1}次重试...")
                time.sleep(5)
            else:
                print(f"已达到最大重试次数({max_retries})，任务失败")
                return False

        except KeyboardInterrupt:
            print("用户中断执行")
            return False

        except Exception as e:
            retry_count += 1
            print(f"执行过程中发生错误: {str(e)}")

            if retry_count < max_retries:
                print(f"将在5秒后进行第{retry_count + 1}次重试...")
                time.sleep(5)
            else:
                print(f"已达到最大重试次数({max_retries})，任务失败")
                return False

    return False


def configure_threading(max_workers: int = 5, ai_rate_limit: float = 2, queue_size: int = 10):
    """
    配置多线程参数
    :param max_workers: 最大工作线程数
    :param ai_rate_limit: AI调用间隔（秒）
    :param queue_size: 任务队列大小
    """
    global config, ai_limiter

    if max_workers is not None:
        config.max_workers = max_workers
        print(f"设置最大工作线程数: {max_workers}")

    if ai_rate_limit is not None:
        config.ai_rate_limit = ai_rate_limit
        ai_limiter = RateLimiter(ai_rate_limit)
        print(f"设置AI调用间隔: {ai_rate_limit}秒")

    if queue_size is not None:
        config.queue_size = queue_size
        print(f"设置任务队列大小: {queue_size}")


def get_performance_stats():
    """获取性能统计信息"""
    return {
        'processed': stats['processed'].get(),
        'success': stats['success'].get(),
        'failed': stats['failed'].get(),
        'success_rate': stats['success'].get() / max(stats['processed'].get(), 1) * 100
    }


if __name__ == '__main__':
    import argparse

    # 命令行参数解析
    parser = argparse.ArgumentParser(description='患者治疗记录AI分析工具')
    parser.add_argument('--multithreading', '-m', action='store_true',
                       help='使用多线程模式 (默认已开启)')
    parser.add_argument('--single-thread', '-s', action='store_true',
                       help='强制使用单线程模式')
    parser.add_argument('--workers', '-w', type=int, default=None,
                       help='最大工作线程数 (默认: 5)')
    parser.add_argument('--rate-limit', '-r', type=float, default=None,
                       help='AI调用间隔秒数 (默认: 2.0)')
    parser.add_argument('--max-retries', type=int, default=9999999999999,
                       help='最大重试次数')

    args = parser.parse_args()

    # 配置多线程参数
    if args.workers or args.rate_limit:
        configure_threading(
            max_workers=args.workers,
            ai_rate_limit=args.rate_limit
        )

    # 确定使用模式（默认多线程，除非指定单线程）
    use_multithreading = not args.single_thread

    # 输出配置信息
    if use_multithreading:
        print("=== 多线程模式配置 ===")
        print(f"最大工作线程数: {config.max_workers}")
        print(f"AI调用间隔: {config.ai_rate_limit}秒")
        print(f"任务队列大小: {config.queue_size}")
        print("=====================")
    else:
        print("=== 单线程模式 ===")
        print("使用原始单线程处理逻辑")
        print("================")

    # 执行任务
    success = main_with_retry(
        max_retries=args.max_retries,
        use_multithreading=use_multithreading,
        max_workers=args.workers
    )

    # 输出最终统计（仅多线程模式）
    if use_multithreading and success:
        stats_info = get_performance_stats()
        print("=== 最终统计 ===")
        print(f"总处理数: {stats_info['processed']}")
        print(f"成功数: {stats_info['success']}")
        print(f"失败数: {stats_info['failed']}")
        print(f"成功率: {stats_info['success_rate']:.2f}%")
        print("===============")

    exit(0 if success else 1)