import re
import base64
import json
from typing import Optional, Dict, Union
import io
import urllib.parse

import requests
from bs4 import BeautifulSoup # type: ignore

from .form_fetcher import fetch_html_form_data


def fetch_temperature_image(
    url: str,
    cookies: Optional[Dict[str, str]] = None,
    timeout: int = 10,
    **_: Dict,
) -> str:
    """抓取体温单页面中的图片src。
    """
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/137.0.0.0 Safari/537.36"
        ),
        "Accept": (
            "text/html,application/xhtml+xml,application/xml;"
            "q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8"
        ),
    }

    # 发起 GET 请求获取页面
    resp = requests.get(
        url,
        headers=headers,
        cookies=cookies,
        timeout=timeout,
        allow_redirects=True,
        verify=False,
    )

    # 尝试自动侦测并设定正确的编码
    try:
        resp.encoding = resp.apparent_encoding or resp.encoding
    except Exception:
        pass

    # 使用 BeautifulSoup 解析HTML
    soup = BeautifulSoup(resp.text, 'html.parser')

    # 查找 img 标签
    img_tags = soup.find_all('img')
    if not img_tags:
        raise ValueError("未能在页面中找到 img 标签。")

    # 获取第一个 img 标签的 src
    img_src = img_tags[0].get('src')
    if not img_src:
        raise ValueError("img 标签中没有 src 属性。")

    # 如果是相对路径，需要拼接完整URL
    if img_src.startswith('/'):
        # 从原URL中提取基础URL
        base_url = '/'.join(url.split('/')[:3])  # http://domain:port
        img_url = base_url + img_src
    elif img_src.startswith('http'):
        img_url = img_src
    else:
        # 相对路径，需要基于当前页面路径
        base_url = '/'.join(url.split('/')[:-1])
        img_url = base_url + '/' + img_src

    # 直接返回图片URL
    return img_url


def fetch_pdf_data(
    url: str | None = None,
    cookies: Optional[Dict[str, str]] = None,
    timeout: int = 10,
    raw_b64: bool = True,
    **_: Dict,
) -> bytes | str | Dict:
    """抓取页面内容。
    """
    default_url = (
        "http://175.16.7.53:8089/WordControl.aspx?"
        "userCode=&PatientId=WlkwMTAwMTM5OTE0MzU=&recordId=NTY2MDIzMA=="
        "&Out=MA==&RecordName=%E5%85%A5%E9%99%A2%E8%AE%B0%E5%BD%95"
    )
    url = url or default_url
    url = urllib.parse.unquote(url)
    # 判断URL中是否包含“疼痛综合评定-永州”，如果包含则调用表单抓取函数并启用自动提交
    if '护理记录单2.0' in url or '医嘱单' in url:
        return fetch_html_form_data(url, cookies, timeout, auto_submit_first_form=True, **_)

    # 判断URL中是否包含护理记录相关关键词，如果包含则调用表单抓取函数
    if 'NursingRecordList' in url:
        return fetch_html_form_data(url, cookies, timeout, **_)

    # 判断URL中是否包含体温单相关关键词，如果包含则调用图片抓取函数
    if 'Nis体温单' in url or 'TemperatureChart' in url or 'Temperature' in url:
        return fetch_temperature_image(url, cookies, timeout, **_)

    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/137.0.0.0 Safari/537.36"
        ),
        "Accept": (
            "text/html,application/xhtml+xml,application/xml;"
            "q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8"
        ),
    }

    # 发起 GET 请求
    resp = requests.get(
        url,
        headers=headers,
        cookies=cookies,
        timeout=timeout,
        allow_redirects=True,
        verify=False,
    )

    # 尝试自动侦测并设定正确的编码
    try:
        resp.encoding = resp.apparent_encoding or resp.encoding
    except Exception:
        pass

    # 正则提取 var pdfData = atob('.....') 或 var pdfData = '.....'
    pattern = (
        r"var\s+pdfData\s*=\s*atob\(\s*['\"]([^'\"]+)['\"]\s*\)"
        r"|var\s+pdfData\s*=\s*['\"]([^'\"]+)['\"]"
    )
    m = re.search(pattern, resp.text, re.IGNORECASE | re.DOTALL)
    if not m:
        raise ValueError("未能在页面中找到 pdfData 变量，页面结构可能已变更。")

    # 兼容两种写法
    b64_str = (m.group(1) or m.group(2) or "").replace(
        "\n", "").replace("\r", "").replace(" ", "")
    if not b64_str:
        raise ValueError("提取到的 pdfData 为空，请检查页面内容。")

    if raw_b64:
        return b64_str

    # 解码成真正的 PDF 字节
    try:
        pdf_bytes: bytes = base64.b64decode(b64_str)
    except base64.binascii.Error as exc:
        raise ValueError(f"Base64 解码失败: {exc}") from exc

    return pdf_bytes


if __name__ == '__main__':
    pass
