#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 重构后随访功能
"""

from custom.db.execute import db
import pandas as pd
from datetime import datetime


def setup_test_data():
    """快速设置测试数据"""
    print("设置测试数据...")
    
    # 1. 清理旧的测试数据
    try:
        db(None, None, "DELETE FROM follow.state WHERE FOLLOW_PLAN_ID IN (SELECT ID FROM follow.plan WHERE INPATIENT_NO LIKE 'TEST%')", 'mysql')
        db(None, None, "DELETE FROM follow.plan WHERE INPATIENT_NO LIKE 'TEST%'", 'mysql')
        db(None, None, "DELETE FROM follow.plan_prove WHERE INPATIENT_NO LIKE 'TEST%'", 'mysql')
        print("✓ 清理旧数据完成")
    except:
        pass
    
    # 2. 插入 plan_prove 数据
    prove_sql = """
    INSERT INTO follow.plan_prove (INPATIENT_NO, UPDATE_TIME, OUT_DATE, IS_SUBMITTED) VALUES
    ('TEST001', '2024-01-15 10:30:00', '2024-01-10 09:00:00', '已提交'),
    ('TEST002', '1970-01-01 00:00:00', '2024-01-12 14:20:00', '未提交')
    """
    db(None, None, prove_sql, 'mysql')
    print("✓ 插入 plan_prove 数据")
    
    # 3. 插入 plan 数据
    plan_sql = """
    INSERT INTO follow.plan 
    (NEXT_ACTION_TIME, INPATIENT_NO, CYCLE, HOURS, CONTENT, PLAN_SOURCE, DEPT_CODE, HOUSE_DOC_CODE, HOUSE_DOC_NAME) VALUES
    ('2024-01-20 08:00:00', 'TEST001', 0, 288, '术后随访检查', '医嘱生成', '0001', 'DOC001', '张医生'),
    ('2024-01-25 08:00:00', 'TEST002', 1, 168, '定期复查', '诊断匹配', '0002', 'DOC002', '李医生')
    """
    db(None, None, plan_sql, 'mysql')
    print("✓ 插入 plan 数据")
    
    # 4. 获取plan ID并插入state数据
    plans = db(['ID', 'INPATIENT_NO'], 'follow.plan', "INPATIENT_NO LIKE 'TEST%'", 'mysql')
    for _, row in plans.iterrows():
        state_sql = f"""
        INSERT INTO follow.state (FOLLOW_PLAN_ID, NEXT_TIME, STATE, DOWN) 
        VALUES ({row['ID']}, '2024-01-20 08:00:00', '待处理', 0)
        """
        db(None, None, state_sql, 'mysql')
    print("✓ 插入 state 数据")
    
    # 5. 插入专人数据（注意：DEDICATED_CODE字段长度限制为6个字符）
    dedicated_sql = """
    INSERT INTO follow.dedicated (DEPT_CODE, DEDICATED_CODE) VALUES
    ('0001', 'NUR001')
    ON DUPLICATE KEY UPDATE DEDICATED_CODE = VALUES(DEDICATED_CODE)
    """
    db(None, None, dedicated_sql, 'mysql')
    print("✓ 插入 dedicated 数据")


def test_queries():
    """测试查询"""
    print("\n测试查询功能...")
    
    # 1. 查询 plan_prove
    prove_data = db(['INPATIENT_NO', 'IS_SUBMITTED'], 'follow.plan_prove', 
                   "INPATIENT_NO LIKE 'TEST%'", 'mysql')
    print(f"plan_prove 表: {len(prove_data)} 条记录")
    print(prove_data.to_string(index=False))
    
    # 2. 查询 plan
    plan_data = db(['INPATIENT_NO', 'CONTENT', 'PLAN_SOURCE'], 'follow.plan', 
                  "INPATIENT_NO LIKE 'TEST%'", 'mysql')
    print(f"\nplan 表: {len(plan_data)} 条记录")
    print(plan_data.to_string(index=False))
    
    # 3. 联合查询
    join_sql = """
    SELECT p.INPATIENT_NO, p.CONTENT, s.STATE, s.DOWN
    FROM follow.plan p
    LEFT JOIN follow.state s ON s.FOLLOW_PLAN_ID = p.ID
    WHERE p.INPATIENT_NO LIKE 'TEST%'
    """
    join_data = db(None, None, join_sql, 'mysql')
    print(f"\n联合查询: {len(join_data)} 条记录")
    print(join_data.to_string(index=False))


def test_functions():
    """测试重构后的函数"""
    print("\n测试重构后的函数...")
    
    try:
        # 测试 get_follow_code_refactored
        from celery_task.polling.follow_timeout_refactored import get_follow_code_refactored
        
        code, name = get_follow_code_refactored('0001', 'DOC001', '张医生')
        print(f"get_follow_code_refactored: {name} ({code})")
        
        # 测试患者数据处理
        from celery_task.polling.follow_generate_refactored import get_follow_up_plan_refactored
        
        test_data = pd.DataFrame({
            'INPATIENT_NO': ['TEST001'],
            'NAME': ['测试患者'],
            'SEX_CODE': ['M'],
            'IDENNO': ['430123199001011234'],
            'HOME_TEL': ['13800138001'],
            'LINKMAN_TEL': ['13900139001'],
            'OUT_DATE': [datetime(2024, 1, 10, 9, 0, 0)],
            'DEPT_CODE': ['0001'],
            'UPDATE_TIME': [datetime(2024, 1, 15, 10, 30, 0)]
        })
        
        result = get_follow_up_plan_refactored(test_data)
        print(f"get_follow_up_plan_refactored: 生成 {len(result)} 条计划")
        if not result.empty:
            print(f"示例: {result.iloc[0]['content']}")
        
        print("✓ 函数测试完成")
        
    except Exception as e:
        print(f"✗ 函数测试失败: {e}")


def test_timeout_logic():
    """测试超时逻辑"""
    print("\n测试超时逻辑...")
    
    # 查询需要处理的超时任务
    timeout_sql = """
    SELECT p.ID, p.INPATIENT_NO, p.CONTENT, s.STATE, s.NEXT_TIME
    FROM follow.plan p
    LEFT JOIN follow.state s ON s.FOLLOW_PLAN_ID = p.ID
    WHERE p.INPATIENT_NO LIKE 'TEST%'
      AND (s.DOWN = 0 OR s.DOWN IS NULL)
    """
    timeout_data = db(None, None, timeout_sql, 'mysql')
    print(f"找到 {len(timeout_data)} 个待处理任务")
    print(timeout_data.to_string(index=False))
    
    # 模拟状态更新
    if not timeout_data.empty:
        first_id = timeout_data.iloc[0]['ID']
        update_sql = f"""
        UPDATE follow.state 
        SET STATE = '通知', NEXT_TIME = DATE_ADD(NOW(), INTERVAL 1 DAY)
        WHERE FOLLOW_PLAN_ID = {first_id}
        """
        db(None, None, update_sql, 'mysql')
        print(f"✓ 更新任务 {first_id} 状态为'通知'")
        
        # 再次查询验证
        updated_data = db(None, None, timeout_sql, 'mysql')
        print("更新后的状态:")
        print(updated_data.to_string(index=False))


def cleanup():
    """清理测试数据"""
    print("\n清理测试数据...")
    try:
        db(None, None, "DELETE FROM follow.state WHERE FOLLOW_PLAN_ID IN (SELECT ID FROM follow.plan WHERE INPATIENT_NO LIKE 'TEST%')", 'mysql')
        db(None, None, "DELETE FROM follow.plan WHERE INPATIENT_NO LIKE 'TEST%'", 'mysql')
        db(None, None, "DELETE FROM follow.plan_prove WHERE INPATIENT_NO LIKE 'TEST%'", 'mysql')
        print("✓ 清理完成")
    except Exception as e:
        print(f"清理失败: {e}")


def main():
    """主函数"""
    print("=== 快速测试重构后的随访功能 ===")
    
    try:
        setup_test_data()
        test_queries()
        test_functions()
        test_timeout_logic()
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup()
    
    print("\n测试完成！")


if __name__ == '__main__':
    main()
