import json
import pandas as pd
import requests
import re
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app
from typing import Union, List, Dict

def check_lymph_nodes():
    """
    主任务函数：提取所有类型癌症的淋巴结清扫数量并更新数据库
    处理流程：
        1. 查询所有LYMPH_NODE_COUNT为空的记录
        2. 使用AI提取淋巴结清扫数量
        3. 将结果更新回LYMPH_NODE_COUNT字段
    """
    try:
        # 步骤1: 查询所有LYMPH_NODE_COUNT为空的记录
        sql = """
        SELECT ID, TREATMENT_CODE, CHECK_RESULT
        FROM ai.test_lymph_specification
        WHERE LYMPH_NODE_COUNT IS NULL
        """

        # 执行查询
        records_df = db(None, None, sql, 'mysql')  # 假设使用MySQL连接

        if records_df.empty:
            print("未找到LYMPH_NODE_COUNT为空的记录")
            return
        print(f"找到{len(records_df)}条需要处理的记录")

        batch_size = 5
        total_rows = len(records_df)
        # update_data = []

        for start in range(0, total_rows, batch_size):
            end = min(start + batch_size, total_rows)
            batch_df = records_df.iloc[start:end]
            # print(batch_df)
            # 调用AI函数处理当前批次
            values_str = extract_lymph_node_count(batch_df)
            # print("-----------------------------------------")
            # print(batch_result)
            update_lymph_node_counts(values_str)
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def replace_spaces_in_matches(text):
    # 定义匹配模式：\d\\d\s+\d\\d
    pattern = r'\d+/\d+\s+\d+/\d'

    # 查找所有匹配项
    matches = re.findall(pattern, text)

    # 遍历每个匹配项，将其中的连续空格替换为逗号
    for match in set(matches):  # 用set去重避免重复替换
        # 替换原始匹配项中的 \s+ 为 ","
        replacement = re.sub(r'\s+', ',', match)
        # 在原文中替换更新
        text = text.replace(match, replacement)

    return re.sub(r"\s+", "", text).replace("\\r", "").replace("\\n", "").replace("\\t", "").replace("\/", "/")

def extract_lymph_node_count(records_df: DataFrame) -> str:
    """
    使用AI提取淋巴结清扫数量（适用于所有癌症类型）
    :param records_df: 包含患者检查结果的DataFrame
    :return: 淋巴结数量列表 [{"id": 123, "lymph_node_count": 10}, ...]
    """
    # 构建AI提示词
    prompt =f"""
    你是一名专业病理科医生，请严格按以下规则分析"报告"中的淋巴结总数量：
        1. 文本中"X/Y"表示有淋巴结总数量为Y，其中X为阳性淋巴结数量（已包含在总数中）（如"总计4/6"应返回6）
        2. 对于无总数的，应累加不同部位淋巴结数量
        3. 请避免重复计数
        4. 无任何淋巴结描述时返回0
        5. 最终严格按照下面输出要求返回。
    以下是报告列表内容：
     {[{"报告": str(value) if not pd.isna(value) else ""} for value in records_df["CHECK_RESULT"]]}
     以下是输出要求：
     1、输出只允许为一段整数结果文本，无任何文字及说明
     2、报告列表中一个元素限定输出一个正整数的淋巴结总数量，不同列表元素的结果用”;“分隔。
     3、输出结果的数量必须和列表中元素数量严格一致
     输出举例：5;6;7;2;9
        """

    # for _, row in records_df.iterrows():
    #     # 使用ID作为唯一标识
    #     prompt += fr"\n记录ID:{row['ID']}): 记录ID:{row['ID']}的检查结果{row['CHECK_RESULT']}"

    # prompt += """
    #
    # """
    prompt = replace_spaces_in_matches(prompt)

    # 调用AI服务
    try:
        response = requests.post(
            "http://***************:12434/api/generate",
            json={
                "model": 'qwen3:8b-q4_K_M',
                "prompt": prompt,
                "stream": False,
                # "think": False
            }
        )
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        print(f"AI服务调用失败: {str(e)}")
        return []

    # 处理AI响应
    ai_output = response.json().get("response", "").strip()
    clean_response = re.sub(r'<think>.*?</think>', '', ai_output, flags=re.DOTALL).strip()
    values_str = ', '.join(
        f"('{str(records_df['ID'].iloc[index])}', '{value.strip()}')"
        for index, value in enumerate(clean_response.split(';')))
    return values_str


# def parse_lymph_response(response_text: str) -> List[Dict]:
#     """
#     解析淋巴结数量响应
#     :param response_text: AI返回的文本
#     :return: 结构化淋巴结数据列表
#     """
#     try:
#         # 尝试直接解析为JSON
#         return json.loads(response_text)
#     except json.JSONDecodeError:
#         print("无法直接解析JSON响应，尝试备用解析方法")
#
#     # 正则提取备用方案 - 适配新的表结构
#     pattern = r'\{\s*"id"\s*:\s*(\d+)\s*,\s*"lymph_node_count"\s*:\s*(\d+)\s*\}'
#     matches = re.findall(pattern, response_text)
#
#     results = []
#     for id_val, count in matches:
#         try:
#             results.append({
#                 "id": int(id_val),
#                 "lymph_node_count": int(count)
#             })
#         except ValueError:
#             print(f"解析失败: id={id_val}, count={count}")
#             continue
#
#     if not results:
#         print(f"无法解析AI响应: {response_text}")
#
#     return results


def parse_ai_response(response_text: str) -> List[Dict]:
    """
    解析AI返回的JSON格式响应
    :param response_text: AI返回的文本
    :return: 结构化患者数据列表
    """
    try:
        # 尝试直接解析为JSON
        data = json.loads(response_text)
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return [data]
    except json.JSONDecodeError:
        pass

    # 如果直接解析失败，尝试提取JSON部分
    try:
        # 查找可能的JSON数组或对象
        json_matches = re.findall(r'\[.*\]|\{.*\}', response_text, re.DOTALL)
        if json_matches:
            return json.loads(json_matches[0])
    except Exception as e:
        print(f"JSON解析失败: {str(e)}")

    # 最后尝试手动提取数据
    patients = []
    pattern = r'\{\s*"id"\s*:\s*(\d+)\s*,\s*"lymph_node_count"\s*:\s*(\d+)\s*\}'
    matches = re.findall(pattern, response_text)

    for id, lymph_node_count in matches:
        try:
            patients.append({
                "id": id.strip(),
                "lymph_node_count": int(lymph_node_count)
            })
        except ValueError:
            continue

    return patients
def update_lymph_node_counts(values_str: str):
    """
    更新淋巴结数量到数据库
    :param data: 更新数据列表 [{"id": 123, "lymph_node_count": 10}, ...]
    """

    # 构建 SQL 语句
    sql = f"""
    INSERT INTO ai.test_lymph_specification (id, lymph_node_count) 
    VALUES {values_str}
    ON DUPLICATE KEY UPDATE 
    LYMPH_NODE_COUNT = VALUES(LYMPH_NODE_COUNT)"""
    # sql = f"""INSERT INTO ai.test_lymph_specification (ID, LYMPH_NODE_COUNT) VALUES
    #                     {data.apply(lambda x: f"('{x['id']}', '{x['UPDATE_TIME']}', '{x['OUT_DATE']}')",
    #                     axis=1).str.cat(sep=', ')}
    #                     ON DUPLICATE KEY UPDATE UPDATE_TIME = VALUES(UPDATE_TIME), OUT_DATE = VALUES(OUT_DATE)
    #         """  # 如果插入的数据在表中已经存在（基于主键或唯一索引），则更新该行的 UPDATE_TIME 和 OUT_DATE 字段
    db(None, None, sql, 'mysql')
    # print(1)

    # for item in data:
    #     update_sql = f"""
    #     UPDATE ai.test_lymph_specification
    #     SET LYMPH_NODE_COUNT = {item['lymph_node_count']}
    #     WHERE ID = {item['id']}
    #     """
    #     try:
    #         result = db(None, None, update_sql, 'mysql')
    #         if result:
    #             print(f"成功更新记录 ID={item['id']} 的淋巴结数量为 {item['lymph_node_count']}")
    #         else:
    #             print(f"更新记录 ID={item['id']} 失败: 数据库操作未返回结果")
    #     except Exception as e:
    #         print(f"更新记录 ID={item['id']} 失败: {str(e)}")


# 定时任务入口
@app.task
def start_lymph_node_extraction():
    check_lymph_nodes()


if __name__ == "__main__":
    # 测试执行
    # start_lymph_node_extraction()
    sql = """
       SELECT ID, CHECK_RESULT
       FROM ai.test_lymph_specification
       WHERE ID = 16
       """
    batch_df = db(None, None, sql, 'mysql')

    values_str = extract_lymph_node_count(batch_df)
    update_lymph_node_counts(values_str)

