'''
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2025-06-23 21:11:04
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2025-06-24 15:04:40
FilePath: \新建文件夹\remote celery worker\celery_task\record\proxy.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''

from __future__ import annotations
# 把任意对象序列化后编码成纯 ASCII，保证可安全通过 Redis 传输。
import base64
# 动态导入模块的核心库。
import importlib
# 尝试把函数返回值序列化为 JSON。
import json
from typing import Dict, Any, Optional, Callable

from celery_task.celery import app

'''
外网 接收用户请求 /pkg/func?a=1&async=1，
在视图里将 URL 转成 operation="pkg.func"，发布 Celery 任务 proxy.fetch。
任务落到 Redis 队列。
内网 Worker 取到任务，进入本文件 fetch：
外网 读取到结果：如果同步模式，当场等待并返回；
如果异步模式，前端轮询查询任务 ID；
最终解码 content_b64 得到真实数据，HTTP 200 响应给浏览器。
'''


# 工具函数，用于动态调用函数，就是把字符串变为可执行的函数
def _import_callable(dotted_path: str) -> Callable:
    """根据 `模块.对象` 字符串导入可调用对象。到时候就把获取数据的函数导入进来"""
    print("导入函数：",dotted_path)
    try:
        module_path, attr = dotted_path.rsplit('.', 1)
        # 动态加载模块；
        module = importlib.import_module(module_path)
        # getattr 取到对象；判断其是否可调用。
        func = getattr(module, attr)
        if not callable(func):
            raise TypeError(f"目标 {dotted_path} 不是可调用对象")
        return func
    except (ImportError, AttributeError, ValueError) as exc:
        raise ImportError(f"无法导入 {dotted_path}: {exc}")


# 把 fetch 注册为 Celery 任务，指定全局唯一名称 celery_task.proxy.fetch。
@app.task(name="celery_task.proxy.fetch")
def fetch(operation: str = '',
          args: Optional[list[Any]] = None,
          kwargs: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """在内网执行本地函数并返回结果。

    参数:
        operation - 形如 ``your_package.module.func`` 的可调用对象路径。
        args      - 位置参数列表。
        kwargs    - 关键字参数字典。

    返回值统一 JSON 结构；其中 ``content_b64`` 字段为函数结果的 JSON
    字符串经 base64 编码后的内容，确保通用性。
    """
    # 用空列表 / 空字典替代 None，避免后续 *args / **kwargs 解包时报错。
    args = args or []
    kwargs = kwargs or {}
    print("fetch 任务开始: operation=",operation,", args=",args,", kwargs=",kwargs)
    try:
        # 如果未指定 operation，则默认调用 pdf 抓取函数
        if not operation:
            operation = "celery_task.record.pdf_fetcher.fetch_pdf_data"

        func = _import_callable(operation)
        print("导入函数: ",func)
        # -- 增强的参数处理 --
        # 检查是否是由外网 proxy 视图封装的 kwargs 结构
        is_proxied_request = all(k in kwargs for k in ['method', 'query', 'body', 'headers'])
        print("is_proxied_request: ",is_proxied_request)
        if is_proxied_request:
            # 是，则解包参数，以便业务函数能直接接收
            final_kwargs = kwargs.get('query', {})  # 基础是查询参数
            # 注入 body，以便业务函数可以通过参数名直接访问
            if 'body' in kwargs and kwargs['body'] is not None:
                final_kwargs['body'] = kwargs['body']
            # 保留 method 和 headers，以防某些高级场景需要
            final_kwargs['method'] = kwargs['method']
            final_kwargs['headers'] = kwargs['headers']
        else:
            # 否，则认为是内部直接调用，保持原样
            final_kwargs = kwargs
        print("final_kwargs: ",final_kwargs)
        # 内网获取数据，函数动态调用
        result = func(*args, **final_kwargs)
        print("result: ",result)
        # 根据结果类型决定编码方式；若 bytes 直接使用，可用于返回文件。
        if isinstance(result, (bytes, bytearray)):
            encoded = base64.b64encode(result).decode()
            headers: Dict[str, str] = {"Content-Type": "application/pdf"}
        else:
            # 尝试将结果序列化为 JSON
            try:
                result_json = json.dumps(result, ensure_ascii=False, default=str)
                encoded = base64.b64encode(result_json.encode()).decode()
            except TypeError:
                encoded = base64.b64encode(str(result).encode()).decode()
            headers = {}

        return {
            "ok": True,
            "content_b64": encoded,
            "status": 200,
            "headers": headers,  # 透传内容类型等信息
        }
    except Exception as exc:
        return {
            "ok": False,
            "content_b64": "",
            "status": 500,
            "headers": {},
            "err": str(exc)
        } 