/*
 Navicat Premium Dump SQL

 Source Server         : 随访数据库
 Source Server Type    : MySQL
 Source Server Version : 80405 (8.4.5)
 Source Host           : ***************:33060
 Source Schema         : follow

 Target Server Type    : MySQL
 Target Server Version : 80405 (8.4.5)
 File Encoding         : 65001

 Date: 29/07/2025 08:41:17
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for advice_to_plan
-- ----------------------------
DROP TABLE IF EXISTS `advice_to_plan`;
CREATE TABLE `advice_to_plan`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `ADVICE` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '医嘱',
  `PLAN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '计划',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '由出院医嘱AI生成随访计划' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for database_generate
-- ----------------------------
DROP TABLE IF EXISTS `database_generate`;
CREATE TABLE `database_generate`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `QUESTIONNAIRE_ID` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '试卷id',
  `DATABASE_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库名',
  `TABLE_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表名',
  `INPATIENT_NO_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '住院号字段名',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '库表匹配生成随访计划' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for dedicated
-- ----------------------------
DROP TABLE IF EXISTS `dedicated`;
CREATE TABLE `dedicated`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DEPT_CODE` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '科室代码',
  `DEDICATED_CODE` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '专人工号',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `follow_dedicated_pk`(`DEPT_CODE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '回访专人' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for icd_generate
-- ----------------------------
DROP TABLE IF EXISTS `icd_generate`;
CREATE TABLE `icd_generate`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `ICD_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '设定者选定的ICD_CODE',
  `operator` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '类型设定者',
  `operate_time` datetime NOT NULL COMMENT '操作时间',
  `entire_scope` tinyint NULL DEFAULT NULL COMMENT '是否是全院范围',
  `depts` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '如果不是全院，是哪些科室',
  `questionnaire_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '问卷ID',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '根据ICD_CODE生成随访计划' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notice_history
-- ----------------------------
DROP TABLE IF EXISTS `notice_history`;
CREATE TABLE `notice_history`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '住院号',
  `DEDICATED_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '随访专员工号',
  `NOTICE_TYPE` enum('短信','语音','电话') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知类型',
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '通知内容',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '随访通知历史记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for papers
-- ----------------------------
DROP TABLE IF EXISTS `papers`;
CREATE TABLE `papers`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `QUESTION_LIST` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '问题列表',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '问卷库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for plan
-- ----------------------------
DROP TABLE IF EXISTS `plan`;
CREATE TABLE `plan`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `NEXT_ACTION_TIME` datetime NULL DEFAULT NULL COMMENT '下次随访时间',
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '住院号',
  `CYCLE` tinyint NULL DEFAULT NULL COMMENT '是否是周期性的',
  `HOURS` int NULL DEFAULT NULL COMMENT '据下一次随访生效的小时数',
  `CONTENT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '回访内容',
  `END_DATE` datetime NULL DEFAULT NULL COMMENT '随访结束时间',
  `PLAN_SOURCE` enum('医嘱生成','诊断匹配','库表匹配') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成来源',
  `DIAG_MATCH_SOURCE_TYPE` enum('科室计划','问卷计划') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '诊断匹配来源类型',
  `SOURCE_ID` int NULL DEFAULT NULL COMMENT '生成来源ID，可以是出院医嘱AI生成随访计划ID(follow_advice_to_plan)或诊断匹配ID',
  `DEPT_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '科室代码',
  `HOUSE_DOC_CODE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主管医生工号',
  `HOUSE_DOC_NAME` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主管医生姓名',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `plan_SOURCE_ID_index`(`SOURCE_ID` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '随访方案' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for plan_prove
-- ----------------------------
DROP TABLE IF EXISTS `plan_prove`;
CREATE TABLE `plan_prove`  (
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '住院号/门诊号',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '诊断证明提交时间',
  `OUT_DATE` datetime NULL DEFAULT NULL COMMENT '出院时间',
  `IS_SUBMITTED` enum('未提交','已提交') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '未提交' COMMENT '是否已提交诊断证明',
  PRIMARY KEY (`INPATIENT_NO`) USING BTREE,
  INDEX `follow_plan_prove_UPDATE_TIME_index`(`UPDATE_TIME` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访方案-诊断证明提交时间' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for questionnaire_record
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_record`;
CREATE TABLE `questionnaire_record`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `FOLLOW_STATE_ID` int NULL DEFAULT NULL COMMENT '随访状态ID(follow_state)',
  `PAPER_ID` int NULL DEFAULT NULL COMMENT '问卷库ID(papers)',
  `OPERATOR` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '操作员',
  `OPERATE_TIME` datetime NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `questionnaire_record_FOLLOW_STATE_ID_index`(`FOLLOW_STATE_ID` ASC) USING BTREE,
  INDEX `questionnaire_record_PAPER_ID_index`(`PAPER_ID` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '问卷记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for questionnaire_results
-- ----------------------------
DROP TABLE IF EXISTS `questionnaire_results`;
CREATE TABLE `questionnaire_results`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `QUESTIONS_ID` int NULL DEFAULT NULL COMMENT '问卷题库ID(questions)-题号',
  `RECORD_ID` int NULL DEFAULT NULL COMMENT '问卷记录ID(questionnaire_record)',
  `ANSWER_OPTION` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '选项类型答案',
  `ANSWER_TEXT` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '文本类型答案',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `questionnaire_results_QUESTIONS_ID_index`(`QUESTIONS_ID` ASC) USING BTREE,
  INDEX `questionnaire_results_RECORD_ID_index`(`RECORD_ID` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '问卷结果表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for questions
-- ----------------------------
DROP TABLE IF EXISTS `questions`;
CREATE TABLE `questions`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `QUESTION_NAME` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '题干',
  `QUESTION_TYPE` enum('一级菜单单选','二级菜单单选','问答') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '问题类型',
  `QUESTION_OPTIONS` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '选项',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '问卷题库' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for state
-- ----------------------------
DROP TABLE IF EXISTS `state`;
CREATE TABLE `state`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `FOLLOW_PLAN_ID` int NULL DEFAULT NULL COMMENT '随访方案ID(follow_plan)',
  `NEXT_TIME` datetime NULL DEFAULT NULL COMMENT '下次随访时间',
  `STATE` enum('待处理','通知','任务生成','超时','二次超时','严重超时') CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '状态',
  `DOWN` tinyint NULL DEFAULT NULL COMMENT '是否已处理',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `state_FOLLOW_PLAN_ID_index`(`FOLLOW_PLAN_ID` ASC) USING BTREE,
  INDEX `state_NEXT_TIME_DOWN_index`(`NEXT_TIME` ASC, `DOWN` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '随访状态表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
