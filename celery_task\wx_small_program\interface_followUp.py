# coding:utf-8
import base64
import copy
import functools
import os
import pickle
import subprocess
import time
import urllib.parse
import webbrowser

import pandas
import requests
from PySide2.QtCore import Qt
from PySide2.QtGui import QIcon, QColor
from PySide2.QtWidgets import QWidget, QListWidgetItem, QTableWidgetItem
from pandas import Data<PERSON>rame
from qfluentwidgets import FluentIcon, InfoBarPosition, InfoBadge, RoundMenu, Action, CheckableMenu, MenuIndicatorType, \
    InfoBar, MessageBox
from qfluentwidgetspro import TopNavigationBar, Toast

from app.common.config import cfg
from app.common.setting import CONFIG_FOLDER
from app.custom.progress_ring import ProgressRing
from app.ui.FollowUpInterface import Ui_FollowUp
from app.view.dialog_binding import BindingBox
from app.view.dialog_follow import WindowFollow
from app.view.dialog_platform import WindowWebSingleton, PhysicalExamDetailsWindow, InspectExamDetailsWindow
from app.view.dialog_warning import WindowFollowWarning
from function.concurrent.TaskExecutor import TaskExecutor
from function.db.execute import db
from function.process.Start import ProcessHandler
from function.program.control_table import SetTable
from function.program.follow_info import get_basic_information, get_admission_information, get_discharge_information, \
    thread_exam, thread_inspect
from function.program.get_db_info import get_follow_task, get_group_info, get_homepage_diagnose, get_element, \
    get_doc_info, get_patient_group, update_patient_group, get_follow_history, get_follow_search, set_condition, \
    get_conditions_by_idcard, delete_warning
from function.program.get_platform import get_report_detailed


class FollowUpInterface(Ui_FollowUp, QWidget):

    def __init__(self, parent=None):
        super().__init__(parent=parent)
        self.setupUi(self)
        self.parent = parent
        self.basic_info = DataFrame()  # 患者信息
        self.group_info = DataFrame()  # 医生个性化分组信息
        self.doctor_info = {}  # 医生信息  {"TEL": '', "NAME": '', "ID": ''}
        self.table = SetTable(self.T1_Tab2_TableWidget, pager=self.T1_Tab2_Pager)  # 分页表格
        self.patient_platform_result = {'province_tree': None, 'province_other': None}  # 患者平台就诊信息存储，用于是否本院就诊过滤用
        self.platform_session = requests.Session()  # 省平台会话
        self.patient_info = {}  # 患者信息
        self.search_condition = {}  # 患者查询条件
        self.total_search = DataFrame()  # 病程追踪信息
        self.total_warning = DataFrame()  # 预警信息

        self._set_ui()  # 升级初始UI
        self._init_timeout_data()  # 初始化数据
        self._connect_signal_to_slot()

    def _set_ui(self):
        #  T1二级顶部导航栏
        index = self.T1_HLayout1.indexOf(self.T1_LabelPlace1)  # 获取布局位置
        self.T1_HLayout1.removeWidget(self.T1_LabelPlace1)
        self.T1_LabelPlace1.deleteLater()
        self.T1_TopNavigationBar = TopNavigationBar(self)
        self.T1_TopNavigationBar.setObjectName('T1_TopNavigationBar')
        self.T1_HLayout1.insertWidget(index, self.T1_TopNavigationBar, 1)

        self.T1_TopNavigationBar.addItem('Info', "本次就诊信息", FluentIcon.INFO)
        self.T1_TopNavigationBar.addItem('Visit', "省内就诊记录", FluentIcon.FEEDBACK)
        self.T1_TopNavigationBar.addItem('Result', "近期外院结果", FluentIcon.CERTIFICATE)
        self.T1_TopNavigationBar.addItem('History', "历史回访记录", FluentIcon.HISTORY)
        self.T1_TopNavigationBar.setCurrentItem('Info')

        self.T1_TabWidget.tabBar().hide()
        self.T1_SwitchButton.hide()

        self.T1_Tab2_wait = ProgressRing("请稍后...", "数据正在查询中 ...", self.T1_Tab2)
        self.T1_Tab3_wait = ProgressRing("请稍后...", "数据正在查询中 ...", self.T1_Tab3)
        self.T1_Tab4_wait = ProgressRing("请稍后...", "数据正在查询中 ...", self.T1_Tab4)

        # 小上标
        item = self.T1_TopNavigationBar.item('Visit')
        self.info_badge_t2 = InfoBadge.attension(text=0, parent=item.parent(), target=item, position="TopNav")
        self.info_badge_t2.setVisible(False)
        item = self.T1_TopNavigationBar.item('Result')
        self.info_badge_t3 = InfoBadge.attension(text=0, parent=item.parent(), target=item, position="TopNav")
        self.info_badge_t3.setVisible(False)
        item = self.T1_TopNavigationBar.item('History')
        self.info_badge_t4 = InfoBadge.attension(text=0, parent=item.parent(), target=item, position="TopNav")
        self.info_badge_t4.setVisible(False)

        # T1_Button4 菜单设置
        t1_button4_menu = RoundMenu(parent=self)
        t1_button4_menu.addAction(
            Action(FluentIcon.DICTIONARY, '查看病历',
                   triggered=functools.partial(self._connect_t1_button4_clicked, '查看病历')))
        t1_button4_menu.addAction(
            Action(FluentIcon.BACKGROUND_FILL, '查看检验',
                   triggered=functools.partial(self._connect_t1_button4_clicked, '查看检验')))
        t1_button4_menu.addAction(
            Action(FluentIcon.PHOTO, '查看影像',
                   triggered=functools.partial(self._connect_t1_button4_clicked, '查看影像')))
        t1_button4_menu.addAction(
            Action(FluentIcon.HEART, '查看心电',
                   triggered=functools.partial(self._connect_t1_button4_clicked, '查看心电')))
        self.T1_Button4.setFlyout(t1_button4_menu)

        # T1_Tab3
        self.T1_Tab3_TableWidget.hide()
        self.T1_Tab3_HLayout3.hide()
        self.T1_Tab3_TextBrowser.hide()

    def _init_timeout_data(self):
        self.basic_info = get_follow_task(self.parent.lic.username)  # 获取基础数据
        # 获取分组信息
        if self.group_info.empty:
            self.group_info = get_group_info(self.parent.lic.username)
        # 获取登录医生信息
        if not self.doctor_info:
            self.doctor_info = get_doc_info(self.parent.lic.username)
        # 左侧待随诊患者列表数据显示
        status_icon_map = {
            '回访任务': ":/app/images/timeout1.png",
            '超时任务': ":/app/images/timeout2.png",
            '二次超时': ":/app/images/timeout3.png",
            '严重超时': ":/app/images/timeout4.png"
        }
        for index, row in self.basic_info.iterrows():
            icon_path = status_icon_map.get(row['状态'], ":/app/images/timeout4.png")
            item = QListWidgetItem(QIcon(icon_path), row['姓名'])
            item.setBackground(QColor(243, 243, 243))
            self.T1_RoundListWidget.addItem(item)

        self.T3_ComboGroup.blockSignals(True)
        self.T3_ComboGroup.clear()
        self.T3_ComboGroup.addItem('全部')
        self.T3_ComboGroup.addItems(list(self.group_info['GROUP_NAME']))
        self.T3_ComboGroup.setCurrentIndex(-1)
        self.T3_ComboGroup.blockSignals(False)

    def _refresh_patient_data(self):
        """ 更新患者信息 """
        object_clear = [self.T1_Tab1_TextEdit1, self.T1_Tab1_TextEdit2, self.T1_Tab1_TextEdit3,
                        self.T1_Tab2_TableWidget, self.T1_Tab3_ListWidget, self.T1_Tab3_TableWidget,
                        self.T1_Tab4_TableWidget, self.T1_Tab3_TextBrowser, self.T1_ListWidget]
        for obj in object_clear:
            obj.clear()
        badges = [self.info_badge_t2, self.info_badge_t3, self.info_badge_t4]
        for badge in badges:
            badge.setVisible(False)
        self.T1_SwitchButton.blockSignals(True)
        self.T1_SwitchButton.setChecked(False)
        self.T1_SwitchButton.blockSignals(False)
        self.T1_Tab2_Pager.setTotal(0)
        self.T1_Tab4_Pager.setTotal(0)
        self.patient_platform_result = {'province_tree': None, 'province_other': None}

        if self.T1_RoundListWidget.currentRow() == -1:
            return

        def get_patient_info(row):
            basic_info = self.basic_info.iloc[self.T1_RoundListWidget.currentRow()]
            data_homepage = get_homepage_diagnose(basic_info['INPATIENT_NO'])
            data_element = get_element(basic_info['INPATIENT_NO'])
            return {"basic_info": basic_info, "data_homepage": data_homepage, "data_element": data_element,
                    'row': row}

        self.parent.wait.show()
        self.parent.wait.raise_()
        self.patient_info = {}
        self.platform_session = None

        TaskExecutor.run(get_patient_info, row=self.T1_RoundListWidget.currentRow()).then(
            self._show_patient_info,
            lambda e: Toast.error(title='查询出错',
                                  content=str(e.exception),
                                  isClosable=True,
                                  position=InfoBarPosition.TOP,
                                  duration=-1,  # won't disappear automatically
                                  parent=self),
            lambda: self.parent.wait.hide())

        self.T1_Tab2_wait.show()
        self.T1_Tab2_wait.raise_()
        self.T1_Tab3_wait.show()
        self.T1_Tab3_wait.raise_()
        self.T1_Tab4_wait.show()
        self.T1_Tab4_wait.raise_()
        TaskExecutor.run(self.tab4_run, row=self.T1_RoundListWidget.currentRow()).then(
            self.tab4_finish, self.tab4_error)

    def _show_patient_info(self, patient_info):
        """ 线程结束 显示患者内容 """
        self.patient_info = patient_info
        # T1_Tab1 内容更新
        result = [get_basic_information(patient_info['basic_info']),
                  get_admission_information(patient_info['data_homepage'], patient_info['data_element']),
                  get_discharge_information(patient_info['basic_info'], patient_info['data_homepage'],
                                            patient_info['data_element'])]
        # raise ValueError('测试报错')
        if self.T1_RoundListWidget.currentRow() == patient_info['row']:
            self.T1_Tab1_TextEdit1.setHtml(result[0])
            self.T1_Tab1_TextEdit2.setHtml(result[1])
            self.T1_Tab1_TextEdit3.setHtml(result[2])
            follow_tab = ProcessHandler()
            TaskExecutor.run(follow_tab.start, path="follow_tab",
                             arguments=[patient_info['basic_info']['身份证'],
                                        patient_info['basic_info']['出院科室代码'],
                                        patient_info['basic_info']['出院科室'],
                                        self.parent.lic.username,
                                        self.doctor_info['NAME'],
                                        self.doctor_info['ID'],
                                        str(patient_info['row'])]).then(
                self.tab_finish,
                lambda e: self.tab23_error(str(e.exception)))

    def tab_finish(self):
        session_file_name = os.path.join(CONFIG_FOLDER, 'temp_tab')
        if not os.path.exists(session_file_name):
            self.tab23_error('省平台连接错误，请联系信息科')
            return
        with open(session_file_name, 'rb') as f:
            tab_dict = pickle.load(f)
            self.platform_session = tab_dict['session']
            self.patient_platform_result['province_tree'] = tab_dict['province_tree']
            self.patient_platform_result['province_other'] = tab_dict['province_other']
            row = int(tab_dict['row'])
        if self.T1_RoundListWidget.currentRow() != row:
            return
        report = copy.deepcopy(self.patient_platform_result['province_tree'])
        if report is not None:
            report = report[report['就诊医院'] != '永州市中心医院']
            self.info_badge_t2.setText(str(report.shape[0]))  # 小上标
            self.info_badge_t2.adjustSize()
            self.info_badge_t2.setVisible(True)
            self.table.set_pager_content(data=report, special_format='platform', connect=self._connect_tab2_detail)
            self.T1_Tab2_wait.hide()

        self._connect_t1_tab3_segmented('检查')
        self.T1_Tab3_Segmented.blockSignals(True)
        self.T1_Tab3_Segmented.setCurrentItem('检查')
        self.T1_Tab3_Segmented.blockSignals(False)
        self.T1_Tab3_wait.hide()

    def tab23_error(self, e: str):
        Toast.error(title='查询出错',
                    content=e,
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=-1,  # won't disappear automatically
                    parent=self)
        self.T1_Tab2_wait.hide()
        self.T1_Tab3_wait.hide()

    def tab4_run(self, row):
        for n in range(cfg.Timeout.value):
            if self.patient_info:
                break
            time.sleep(1)
        else:
            raise ValueError('网络连接超时')
        result = get_follow_history(dept_code=self.doctor_info['PERMISSION_DEPT_ID'],
                                    id_card=self.patient_info['basic_info']['身份证'])
        if not result.empty:
            result = result.drop(['患者姓名', '身份证号'], axis=1)
        # raise ValueError('测试报错')
        return result if self.T1_RoundListWidget.currentRow() == row else None

    def tab4_finish(self, result):
        if result is None:
            return
        table = SetTable(self.T1_Tab4_TableWidget, pager=self.T1_Tab4_Pager)
        self.info_badge_t4.setText(str(result.shape[0]))  # 小上标
        self.info_badge_t4.adjustSize()
        self.info_badge_t4.setVisible(True)

        table.set_pager_content(data=result)
        self.T1_Tab4_wait.hide()

    def tab4_error(self, e):
        Toast.error(title='查询出错',
                    content=str(e.exception),
                    isClosable=True,
                    position=InfoBarPosition.TOP,
                    duration=-1,  # won't disappear automatically
                    parent=self)
        self.T1_Tab4_wait.hide()

    def _connect_tab2_detail(self, url, title):
        """
        检查检验结果详情弹窗
        title 为 PROVINCE_COMPARISON 的 key
        """
        if title == '病历':  # HIS替代的
            url = ('http://175.16.7.53:8089/PatientMainForm.aspx?hisInpatientID=' +
                   base64.b64encode(url.encode('utf-8')).decode('utf-8'))
            WindowWebSingleton(url, '病历查看')
        elif title == '检验':  # HIS替代的
            if not cfg.lisFolder.value:
                Toast.error(title='警告',
                            content='抱歉，未检测到LIS路径，请在配置页面设置！',
                            isClosable=True,
                            position=InfoBarPosition.TOP,
                            duration=-1,  # won't disappear automatically
                            parent=self)
            else:
                InfoBar.success(title='请稍后',
                                content='正在打开……',
                                orient=Qt.Horizontal,
                                isClosable=True,
                                position=InfoBarPosition.TOP_RIGHT,
                                duration=10000,
                                parent=self)
                command = f""""{cfg.lisFolder.value}" '{url[-10:]}' 住院"""
                subprocess.Popen(command, shell=True)
        elif title == '检查':  # HIS替代的
            url = f'https://175.16.7.80/webpacs/HistoryStudy.html?method=patientid&id={url}'
            # web = WindowWeb(url, '影像学查看')
            # web.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
            # web.showMaximized()
            WindowWebSingleton(url, '影像学查看')
        elif urllib.parse.quote('病案首页记录') in url:
            WindowWebSingleton(url, '病案首页查看', treatment='homepage', parent=self)
        elif urllib.parse.quote('出院小结记录') in url:
            WindowWebSingleton(url, '出院小结查看', treatment='discharge', parent=self)
        elif urllib.parse.quote('体检记录') in url:
            self.report_table = PhysicalExamDetailsWindow(self)
            self.report_table.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
            self.report_table.show()
            TaskExecutor.run(thread_exam, url=url, session=self.platform_session).then(
                self.report_table.set_list,
                lambda e: Toast.error(title='查询出错',
                                      content=str(e.exception),
                                      isClosable=True,
                                      position=InfoBarPosition.TOP,
                                      duration=-1,  # won't disappear automatically
                                      parent=self))
        else:  # 医嘱
            self.report_table = InspectExamDetailsWindow(self)
            self.report_table.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
            self.report_table.show()
            TaskExecutor.run(thread_inspect, url=url, session=self.platform_session).then(
                lambda data: self.report_table.set_list(**data),
                lambda e: Toast.error(title='查询出错',
                                      content=str(e.exception),
                                      isClosable=True,
                                      position=InfoBarPosition.TOP,
                                      duration=-1,  # won't disappear automatically
                                      parent=self))

    def _connect_signal_to_slot(self):
        self.T1_RoundListWidget.itemClicked.connect(self._connect_patient_clicked)  # 左侧待随访患者列表
        self.T1_TopNavigationBar.currentItemChanged.connect(self._connect_change_t1_type)  # T1子分类切换
        self.T1_SwitchButton.checkedChanged.connect(self._connect_t1_switch)  # 本院就诊信息切换
        self.T1_Tab3_Segmented.currentItemChanged.connect(self._connect_t1_tab3_segmented)  # T1 Tab3分段切换
        self.T1_Tab3_ListWidget.itemClicked.connect(self._connect_t1_tab3_list)  # T1 Tab3列表切换
        self.T1_Button1.clicked.connect(self._connect_t1_button1_clicked)  # T1 Tab1按钮1
        self.T1_Button2.clicked.connect(self._connect_t1_button2_clicked)  # T1 Tab1按钮2

        self.T2_EditFilter.textChanged.connect(
            lambda: self.T2_CheckFilter.setChecked(bool(self.T2_EditFilter.text())))  # T2
        self.T2_RangeCalendarPicker.dateRangeChanged.connect(lambda: self.T2_CheckDate.setChecked(True))
        self.T2_ComboType.currentIndexChanged.connect(lambda index: self.T2_CheckType.setChecked(index != 0))
        self.T2_ButtonQuery.clicked.connect(self._connect_t2_button_query_clicked)  # T2查询按钮

        self.T3_EditName.textChanged.connect(
            lambda: self.T3_CheckName.setChecked(bool(self.T3_EditName.text())))
        self.T3_EditInpatient.textChanged.connect(
            lambda: self.T3_CheckInpatient.setChecked(bool(self.T3_EditInpatient.text())))
        self.T3_EditIDCard.textChanged.connect(
            lambda: self.T3_CheckIDCard.setChecked(bool(self.T3_EditIDCard.text())))
        self.T3_RangeCalendarPickerIn.dateRangeChanged.connect(lambda: self.T3_CheckInTime.setChecked(True))
        self.T3_RangeCalendarPickerOut.dateRangeChanged.connect(lambda: self.T3_CheckOutTime.setChecked(True))
        self.T3_EditInDiag.textChanged.connect(
            lambda: self.T3_CheckInDiag.setChecked(bool(self.T3_EditInDiag.text())))
        self.T3_EditOutDiag.textChanged.connect(
            lambda: self.T3_CheckOutDiag.setChecked(bool(self.T3_EditOutDiag.text())))
        self.T3_ComboGroup.currentIndexChanged.connect(lambda index: self.T3_CheckGroup.setChecked(index != 0))
        self.T3_ButtonInspect.clicked.connect(self._connect_t3_button_inspect_clicked)  # T3查询按钮
        self.T3_ButtonQuery.clicked.connect(self._connect_t3_button_query_clicked)  # T3查询按钮
        self.T3_ButtonReset.clicked.connect(self._connect_t3_button_reset_clicked)  # T3重置按钮
        self.T3_TableWidget.customContextMenuRequested.connect(self._connect_t3_table_context_menu)  # T3右键菜单

        self.T4_EditName.textChanged.connect(
            lambda: self.T4_CheckName.setChecked(bool(self.T4_EditName.text())))
        self.T4_EditIDCard.textChanged.connect(
            lambda: self.T4_CheckIDCard.setChecked(bool(self.T4_EditIDCard.text())))
        self.T4_RangeCalendarPicker.dateRangeChanged.connect(lambda: self.T4_Time.setChecked(True))
        self.T4_ButtonFilter.clicked.connect(self._connect_t4_button_filter_clicked)  # T4筛选按钮
        self.T4_ButtonReset.clicked.connect(self._connect_t4_button_reset_clicked)  # T4重置按钮
        self.T4_TableWidget.customContextMenuRequested.connect(self._connect_t4_table_context_menu)  # T3右键菜单

    def _connect_patient_clicked(self, item):
        """ 左侧待随访患者列表点击事件 """
        row = self.T1_RoundListWidget.row(item)
        basic_info = self.basic_info.iloc[row]

        self.T1_ListWidget.clear()
        self._refresh_patient_data()
        self.T1_TopNavigationBar.setCurrentItem('Info')
        self.T1_TabWidget.setCurrentIndex(0)
        self._connect_change_t1_type('Info')

        # 设置回访目的
        for index, stand in enumerate(basic_info['INFO']):
            aim = stand.replace('<<MERGE>>', basic_info['COMPLETE'][index])
            item = QListWidgetItem(aim)
            item.setIcon(QIcon(':/app/images/question.png'))
            self.T1_ListWidget.addItem(item)

        self._refresh_group_menu()

    def _refresh_group_menu(self):
        """ 刷新患者分组菜单 """
        t1_button3_menu = RoundMenu(parent=self)
        t1_button3_menu.addAction(
            Action(FluentIcon.CANCEL_MEDIUM, '回访取消',
                   triggered=functools.partial(self._connect_t1_button3_clicked, '回访取消')))
        t1_button3_menu.addAction(
            Action(FluentIcon.CARE_UP_SOLID, '回访升级',
                   triggered=functools.partial(self._connect_t1_button3_clicked, '回访升级')))
        t1_button3_menu.addSeparator()  # 添加分隔符
        t1_button3_menu.addAction(
            Action(FluentIcon.RINGER, '设置预警',
                   triggered=functools.partial(self._connect_t1_button3_clicked, '设置预警')))

        basic_info = self.basic_info.iloc[self.T1_RoundListWidget.currentRow()]
        group_patient = get_patient_group(basic_info['身份证'])
        if group_patient.empty:
            group_patient = pandas.DataFrame(columns=['ID', 'TAG'])
        group = pandas.merge(self.group_info, group_patient, on='ID', how='left')
        submenu = CheckableMenu(title='设置分组', parent=self, indicatorType=MenuIndicatorType.RADIO)
        submenu.setIcon(FluentIcon.FOLDER_ADD)
        for index, row in group.iterrows():
            action = Action(FluentIcon.CALENDAR, row['GROUP_NAME'], checkable=True,
                            triggered=self._connect_t1_button3_clicked)
            action.setChecked(not pandas.isnull(row['TAG']))
            action.setObjectName(f"action_group1")
            submenu.addActions([action])
        t1_button3_menu.addMenu(submenu)  # 添加子菜单到临时主菜单
        self.T1_Button3.setFlyout(t1_button3_menu)

    def _connect_change_t1_type(self, route_key):
        """ 切换T1子分类类型 """
        self.T1_SwitchButton.hide()
        if route_key == 'Visit':
            self.T1_SwitchButton.show()
        route_index = {
            'Info': 0,
            'Visit': 1,
            'Result': 2,
            'History': 3
        }
        if route_key in route_index:
            self.T1_TabWidget.setCurrentIndex(route_index[route_key])

    def _connect_t1_button3_clicked(self, btn_name=None):
        """ T1按钮3点击事件 """
        basic_info = self.basic_info.iloc[self.T1_RoundListWidget.currentRow()]
        if self.basic_info.empty:
            return
        if btn_name == '回访取消':
            finish = [False]
            win_update = WindowFollow(basic_info, 'Cancel', callback_finish=finish)
            win_update.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
            win_update.exec_()
            if finish[0]:
                self.T1_RoundListWidget.clear()
                self._init_timeout_data()
                self.T1_RoundListWidget.setCurrentRow(-1)
                self._refresh_patient_data()
        elif btn_name == '回访升级':
            if basic_info['主管医生工号'] == basic_info['回访专人工号']:
                InfoBar.warning(title='无升级权限',
                                content='只有回访专员才可升级问题',
                                orient=Qt.Horizontal,
                                isClosable=True,
                                position=InfoBarPosition.BOTTOM_RIGHT,
                                duration=3000,
                                parent=self)
                return
            else:
                finish = [False]
                win_update = WindowFollow(basic_info, 'Upgrade', callback_finish=finish)
                win_update.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
                win_update.exec_()
                if finish[0]:
                    self.T1_RoundListWidget.clear()
                    self._init_timeout_data()
                    self.T1_RoundListWidget.setCurrentRow(-1)
                    self._refresh_patient_data()
        elif btn_name == '设置预警':
            callback = {'condition': []}
            win_update = WindowFollowWarning(callback,
                                             patient_ids=basic_info["身份证"],
                                             user=self.parent.lic.username,
                                             purpose='warning')
            win_update.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
            win_update.exec_()
            set_condition(callback, self.parent.lic.username, basic_info["身份证"])
        else:  # 设置分组
            group_id = self.group_info[self.group_info['GROUP_NAME'] == self.sender().text()]['ID'].iloc[0]
            update_patient_group(basic_info["身份证"], group_id, self.sender().isChecked())
            self._refresh_group_menu()

    def _connect_t1_button4_clicked(self, btn_name=None):
        """ T1按钮4点击事件 """
        row = self.T1_RoundListWidget.currentRow()
        if row == -1:
            return
        basic_info = self.basic_info.iloc[row]
        if btn_name == '查看病历':
            url = ('http://175.16.7.53:8089/PatientMainForm.aspx?hisInpatientID=' +
                   base64.b64encode(basic_info['INPATIENT_NO'].encode('utf-8')).decode('utf-8'))
            WindowWebSingleton(url, '病历查看')
        elif btn_name == '查看检验':
            if not cfg.lisFolder.value:
                Toast.error(title='警告',
                            content='抱歉，未检测到LIS路径，请在配置页面设置！',
                            isClosable=True,
                            position=InfoBarPosition.TOP,
                            duration=-1,  # won't disappear automatically
                            parent=self)
            else:
                InfoBar.success(title='请稍后',
                                content='正在打开……',
                                orient=Qt.Horizontal,
                                isClosable=True,
                                position=InfoBarPosition.BOTTOM_RIGHT,
                                duration=10000,
                                parent=self)
                command = f""""{cfg.lisFolder.value}" '{basic_info['INPATIENT_NO'][-10:]}' 住院"""
                subprocess.Popen(command, shell=True)
        elif btn_name == '查看影像':
            condition = [basic_info['CARD_NO'], basic_info['INPATIENT_NO']]
            if basic_info['身份证'] and len(basic_info['身份证']) >= 15:
                condition.append(basic_info['身份证'])
            url = 'https://175.16.7.80/webpacs/HistoryStudy.html?method=patientid&id=' + ','.join(condition)
            WindowWebSingleton(url, '影像学查看')
        elif btn_name == '查看心电':
            url = f"http://175.16.8.200/fileindex.asp?Caseno={basic_info['INPATIENT_NO'][4:]}"
            webbrowser.open(url)

    def _connect_t1_switch(self, is_checked: bool):
        report = copy.deepcopy(self.patient_platform_result['province_tree'])
        if not is_checked:
            report = report[report['就诊医院'] != '永州市中心医院']
        # 小上标
        self.info_badge_t2.setText(str(report.shape[0]))
        self.info_badge_t2.adjustSize()
        self.info_badge_t2.setVisible(True)

        self.table.set_pager_content(data=report, special_format='platform', connect=self._connect_tab2_detail)

    def _connect_t1_tab3_list(self, item):
        """ T1 Tab3 列表点击事件 """

        def _run(url, hospital, current_row, session):
            callback_dict = {'detail': get_report_detailed(session.get(url)),
                             'hospital': hospital}
            return callback_dict if self.T1_Tab3_ListWidget.currentRow() == current_row else None

        def _error(e):
            Toast.error(title='查询出错',
                        content=str(e.exception),
                        isClosable=True,
                        position=InfoBarPosition.TOP,
                        duration=-1,  # won't disappear automatically
                        parent=self)
            self.T1_Tab3_detail_wait.hide()

        row = self.T1_Tab3_ListWidget.row(item)
        key = self.T1_Tab3_Segmented.currentRouteKey()
        self.T1_Tab3_TableWidget.hide()
        self.T1_Tab3_TextBrowser.hide()
        self.T1_Tab3_HLayout3.show()
        if key == '检验':
            info_inspect = self.patient_platform_result['province_other']['inspect'].iloc[row]
            TaskExecutor.run(_run, url=info_inspect['URL'], hospital=info_inspect['HOSPITAL'],
                             session=self.platform_session,
                             current_row=self.T1_Tab3_ListWidget.currentRow()).then(
                self._show_platform_table_detail, _error)
        elif key == '检查':
            info_exam = self.patient_platform_result['province_other']['exam'].iloc[row]
            TaskExecutor.run(_run, url=info_exam['URL'], hospital=info_exam['HOSPITAL'], session=self.platform_session,
                             current_row=self.T1_Tab3_ListWidget.currentRow()).then(
                self._show_platform_browser_detail, _error)

    def _connect_t2_button_query_clicked(self):
        """ T2 查询按钮点击事件 """
        id_card = None
        patient_name = None
        date_s = None
        date_e = None
        types = None
        filter_text = self.T2_EditFilter.text()
        if self.T2_CheckFilter.isChecked() and self.T2_EditFilter.text():
            # select INPATIENT_NO, PATIENT_NO, IDENNO from HNYZ_ZXYY.FIN_IPR_INMAININFO
            if len(filter_text) == 8:  # 13577657
                id_card = db('IDENNO', 'HNYZ_ZXYY.FIN_IPR_INMAININFO',
                             {'PATIENT_NO': f"00{filter_text}"}, 'oracle')
            elif len(filter_text) == 10:  # **********
                id_card = db('IDENNO', 'HNYZ_ZXYY.FIN_IPR_INMAININFO',
                             {'PATIENT_NO': filter_text}, 'oracle')
            elif len(filter_text) == 14:  # ZY010013577468
                id_card = db('IDENNO', 'HNYZ_ZXYY.FIN_IPR_INMAININFO',
                             {'INPATIENT_NO': filter_text}, 'oracle')
            elif len(filter_text) >= 15:
                id_card = filter_text
            else:
                patient_name = filter_text
        if self.T2_CheckDate.isChecked():
            date_s = self.T2_RangeCalendarPicker.startDate.toString(Qt.ISODate)
            date_e = self.T2_RangeCalendarPicker.endDate.toString(Qt.ISODate)
        if self.T2_CheckType.isChecked():
            types = self.T2_ComboType.currentText()

        report_filter = get_follow_history(self.doctor_info['PERMISSION_DEPT_ID'], id_card,
                                           patient_name, date_s, date_e, types)
        assert isinstance(report_filter, pandas.DataFrame)
        SetTable(self.T2_TableWidget, pager=self.T2_Pager, resize=False).set_pager_content(report_filter)

    def _connect_t3_button_inspect_clicked(self):
        if self.search_condition.get('Inspect', None) is None:
            self.search_condition['Inspect'] = []
        callback = {'condition': self.search_condition['Inspect']}
        win_update = WindowFollowWarning(callback)
        win_update.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
        win_update.exec_()
        self.T3_CheckInspect.setChecked(bool(self.search_condition['Inspect']))

    def _connect_t3_button_query_clicked(self):
        condition = {}
        if self.T3_CheckName.isChecked():
            condition['Name'] = self.T3_EditName.text()
        if self.T3_CheckInpatient.isChecked():
            condition['Inpatient'] = self.T3_EditInpatient.text()
        if self.T3_CheckIDCard.isChecked():
            condition['IDCard'] = self.T3_EditIDCard.text()
        if self.T3_CheckInTime.isChecked():
            condition['InTime'] = [self.T3_RangeCalendarPickerIn.startDate.toString(Qt.ISODate),
                                   self.T3_RangeCalendarPickerIn.endDate.toString(Qt.ISODate)]
        if self.T3_CheckOutTime.isChecked():
            condition['OutTime'] = [self.T3_RangeCalendarPickerOut.startDate.toString(Qt.ISODate),
                                    self.T3_RangeCalendarPickerOut.endDate.toString(Qt.ISODate)]
        if self.T3_CheckInDiag.isChecked():
            condition['InDiag'] = self.T3_EditInDiag.text()
        if self.T3_CheckOutDiag.isChecked():
            condition['OutDiag'] = self.T3_EditOutDiag.text()
        if self.T3_CheckInspect.isChecked():
            condition['Inspect'] = self.search_condition.get('Inspect', [])
        if self.T3_CheckGroup.isChecked():
            condition['Group'] = self.T3_ComboGroup.currentText()

        self.total_search = get_follow_search(condition, self.parent.lic.username)
        SetTable(self.T3_TableWidget, pager=self.T3_Pager).set_pager_content(self.total_search)

    def _connect_t3_button_reset_clicked(self):
        """ T3 重置按钮点击事件 """
        self.search_condition = {}
        for widget in ['T3_EditName', 'T3_EditInpatient', 'T3_EditIDCard', 'T3_EditInDiag', 'T3_EditOutDiag',
                       'T3_TableWidget']:
            getattr(self, widget).clear()
        self.T3_ComboGroup.setCurrentIndex(-1)
        for widget in ['T3_CheckName', 'T3_CheckInpatient', 'T3_CheckIDCard', 'T3_CheckInTime', 'T3_CheckOutTime',
                       'T3_CheckInDiag', 'T3_CheckOutDiag', 'T3_CheckInspect', 'T3_CheckGroup']:
            getattr(self, widget).setChecked(False)
        self.T3_TableWidget.clear()
        self.T3_Pager.setTotal(0)

    def _connect_t3_table_context_menu(self, point):
        """ T3 表格右键菜单 """
        id_cards = []
        for i in self.T3_TableWidget.selectionModel().selectedRows():
            id_cards.append(self.total_search.iloc[i.row()]['身份证号_隐藏'])
        if not id_cards:
            return
        # 获取患者分组
        group_patient = db(['GROUP_ID ID', "ID_CARD"], 'yzzxyy.follow_group_patient',
                           f"""ID_CARD in ('{"', '".join(id_cards)}')""", 'mysql')
        group = []
        if not group_patient.empty:
            # 取id_cards对应的group_patient的交集
            result_ids = set(group_patient[group_patient['ID_CARD'] == id_cards[0]]['ID'])
            for item in id_cards[1:]:
                result_ids = result_ids.intersection(set(group_patient[group_patient['ID_CARD'] == item]['ID']))
            # 获取id对应的group_name
            group = self.group_info[self.group_info['ID'].isin(result_ids)]['GROUP_NAME'].tolist()

        # 主菜单
        menu = RoundMenu()
        # 子菜单
        submenu = CheckableMenu(title='分组', parent=self, indicatorType=MenuIndicatorType.RADIO)
        submenu.setIcon(FluentIcon.ADD)
        for index, row in self.group_info.iterrows():
            action = Action(FluentIcon.CALENDAR, row['GROUP_NAME'], checkable=True,
                            triggered=self._connect_t3_table_group_changed)
            action.setChecked(row['GROUP_NAME'] in group)
            submenu.addActions([action])
        # 添加分隔符
        submenu.addSeparator()

        menu.addMenu(submenu)  # 添加子菜单到主菜单
        action = Action(FluentIcon.RINGER, '预警', triggered=self._connect_t3_table_condition_changed)
        menu.addAction(action)
        # 添加分隔符
        submenu.addSeparator()
        action = Action(FluentIcon.DICTIONARY, '查看病历', triggered=self._connect_t3_table_web_view)
        menu.addAction(action)
        # 在指定位置弹出菜单
        menu.exec(self.T3_TableWidget.mapToGlobal(point), ani=True)

    def _connect_t3_table_group_changed(self):
        """ T3 表格分组按钮点击事件 """
        group_id = self.group_info[self.group_info['GROUP_NAME'] == self.sender().text()]['ID'].iloc[0]
        for i in self.T3_TableWidget.selectionModel().selectedRows():
            update_patient_group(id_card=self.total_search.iloc[i.row()]['身份证号_隐藏'],
                                 group_id=group_id, is_insert=self.sender().isChecked())
        self._connect_t3_button_query_clicked()

    def _connect_t3_table_condition_changed(self):
        """ T3 表格条件改变事件 """
        rows = []
        id_cards = []
        for i in self.T3_TableWidget.selectionModel().selectedRows():
            rows.append(i.row())  # 所有选择的行
            id_cards.append(self.total_search.iloc[i.row()]['身份证号_隐藏'])
        # 获取患者预警
        warning_history = None
        conditions_patient = get_conditions_by_idcard(self.parent.lic.username, id_cards)
        if not conditions_patient.empty:
            # 取id_cards对应的group_patient的交集
            result_ids = set(conditions_patient[conditions_patient['ID_CARD'] == id_cards[0]]['CONDITIONS'])
            for item in id_cards[1:]:
                result_ids = result_ids.intersection(
                    set(conditions_patient[conditions_patient['ID_CARD'] == item]['CONDITIONS']))
            warning_history = pandas.DataFrame({'已存在预警': range(1, len(result_ids) + 1),
                                                '条件_隐藏': list(result_ids),
                                                'ID_隐藏': list(result_ids)})
        callback = {'condition': []}
        win_update = WindowFollowWarning(callback,
                                         warning_history=warning_history,
                                         purpose='warning')
        win_update.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
        win_update.exec_()
        for id_card in id_cards:
            set_condition(callback, self.parent.lic.username, id_card)
        self._connect_t3_button_query_clicked()

    def _connect_t3_table_web_view(self):
        """ T3 表格查看病历点击事件 """
        inpatient_no = []
        for i in self.T3_TableWidget.selectionModel().selectedRows():
            inpatient_no.append(self.total_search.iloc[i.row()]['住院号'])
        inpatient_no = list(set(inpatient_no))
        if len(inpatient_no) > 1:
            w = MessageBox('是否同时打开多份病历？', f'同时打开{inpatient_no}', self.window())
            w.setContentCopyable(True)
            w.yesButton.setText('确定打开')
            w.cancelButton.setText('取消打开')
            if not w.exec():
                return
            for no in inpatient_no:
                url = ('http://175.16.7.53:8089/PatientMainForm.aspx?hisInpatientID=' +
                       base64.b64encode(no.encode('utf-8')).decode('utf-8'))
                webbrowser.open(url)
        else:
            url = ('http://175.16.7.53:8089/PatientMainForm.aspx?hisInpatientID=' +
                   base64.b64encode(inpatient_no[0].encode('utf-8')).decode('utf-8'))
            WindowWebSingleton(url, '病历查看')

    def _connect_t4_button_filter_clicked(self):
        # 查询预警规则
        condition = {'USER': self.parent.lic.username}
        if self.T4_CheckIDCard.isChecked():
            condition['ID_CARD'] = self.T4_EditIDCard.text()
        if self.T4_Time.isChecked():
            condition = " AND ".join([f"{key} = '{value}'" for key, value in condition.items()])
            condition += (f" AND TIME BETWEEN '{self.T4_RangeCalendarPicker.startDate.toString(Qt.ISODate)}'"
                          f" AND '{self.T4_RangeCalendarPicker.endDate.toString(Qt.ISODate)}'")
        warning = db(['ID_CARD 身份证号', 'TIME 预警添加时间'], 'yzzxyy.follow_warning',
                     condition, 'mysql')

        if not warning.empty:
            name = db(['DISTINCT IDENNO 身份证号', 'NAME 姓名'], 'HNYZ_ZXYY.COM_PATIENTINFO',
                      f"""IDENNO in ('{"', '".join(warning['身份证号'].unique())}')""", 'oracle')
            warning = pandas.merge(warning, name, on='身份证号', how='left')
            warning = warning.reindex(columns=['姓名'] + [col for col in warning.columns if col != '姓名'])
            warning = warning.sort_values(by=['姓名', '预警添加时间'], ascending=False)

        if self.T4_CheckName.isChecked():
            warning = warning[warning['姓名'] == self.T4_EditName.text()]

        self.total_warning = warning
        SetTable(self.T4_TableWidget, pager=self.T4_Pager, resize=False).set_pager_content(warning)

    def _connect_t4_button_reset_clicked(self):
        """ T4 重置按钮点击事件 """
        self.search_condition = {}
        for widget in ['T4_EditName', 'T4_EditIDCard', 'T4_TableWidget']:
            getattr(self, widget).clear()
        for widget in ['T4_CheckName', 'T4_CheckIDCard', 'T4_Time']:
            getattr(self, widget).setChecked(False)
        self.T4_TableWidget.clear()
        self.T4_Pager.setTotal(0)

    def _connect_t4_table_context_menu(self, point):
        """ T4 表格右键菜单点击事件 """
        # 主菜单
        menu = RoundMenu()
        # 子菜单
        action = Action(FluentIcon.COPY, '删除', triggered=self._connect_t4_table_delete)
        menu.addAction(action)
        # 在指定位置弹出菜单
        menu.exec(self.T4_TableWidget.mapToGlobal(point), ani=True)

    def _connect_t4_table_delete(self):
        """ T4 表格删除按钮点击事件 """
        # self.total_search.iloc[i.row()]['住院号']
        for i in self.T4_TableWidget.selectionModel().selectedRows():
            item = self.total_warning.iloc[i.row()]
            delete_warning(self.parent.lic.username, item['身份证号'], item['预警添加时间'])
        # 重置页面
        self._connect_t4_button_filter_clicked()

    def _show_platform_table_detail(self, result):
        """ T1 Tab3 检验详情点击事件 """
        self.T1_Tab3_TableWidget.clear()
        if result is None or result['detail'].empty:
            return
        self.T1_Tab3_Label.setText(result['hospital'])
        self.T1_Tab3_TableWidget.setRowCount(result['detail'].shape[0])  # 设置行数
        header_list = [key for key in result['detail'].columns if not key.endswith("_隐藏")]  # 获得可视列
        self.T1_Tab3_TableWidget.setColumnCount(len(header_list))
        self.T1_Tab3_TableWidget.setHorizontalHeaderLabels(header_list)
        # 写单元格
        for column, header in enumerate(header_list):
            for row, content_item in enumerate(result['detail'][header]):
                item = QTableWidgetItem(str(content_item))
                if result['detail']['标记'][row] == '↑':
                    item.setForeground(QColor(Qt.red))
                elif result['detail']['标记'][row] == '↓':
                    item.setForeground(QColor(Qt.blue))
                self.T1_Tab3_TableWidget.setItem(row, column, item)
        self.T1_Tab3_TableWidget.setColumnWidth(0, 150)
        self.T1_Tab3_TableWidget.resizeColumnToContents(1)
        self.T1_Tab3_TableWidget.resizeColumnToContents(2)
        self.T1_Tab3_TableWidget.resizeColumnToContents(3)
        self.T1_Tab3_TableWidget.show()
        self.T1_Tab3_HLayout3.hide()

    def _show_platform_browser_detail(self, result):
        """ T1 Tab3 检查详情点击事件 """
        self.T1_Tab3_TextBrowser.clear()
        if result is None or result['detail'].empty:
            return
        self.T1_Tab3_Label.setText(result['hospital'])
        mackdown = ""
        for col in result['detail'].columns:
            if result['detail'][col][0]:
                conclusion = result['detail'][col][0].replace('\n\n', '\n').replace('\n', '\n * ')
                mackdown += f""" \n ## {col} \n * {conclusion} \n"""
        self.T1_Tab3_TextBrowser.setMarkdown(mackdown)
        self.T1_Tab3_TextBrowser.show()
        self.T1_Tab3_HLayout3.hide()

    def _connect_t1_tab3_segmented(self, key):
        """ T1 Tab3 Segmented点击事件 """
        self.T1_Tab3_ListWidget.clear()
        self.T1_Tab3_TableWidget.clear()
        self.T1_Tab3_Label.setText("")
        if key == '检查':
            if self.patient_platform_result['province_other']['exam'].empty:
                return
            for stand in self.patient_platform_result['province_other']['exam']['ITEM']:
                item = QListWidgetItem(stand)
                item.setIcon(QIcon(':/app/images/report.png'))
                self.T1_Tab3_ListWidget.addItem(item)
            self.T1_Tab3_TextBrowser.show()
            self.T1_Tab3_TableWidget.hide()
            self.T1_Tab3_HLayout3.hide()

            self.info_badge_t3.setText(str(self.patient_platform_result['province_other']['exam'].shape[0]))  # 小上标
            self.info_badge_t3.adjustSize()
            self.info_badge_t3.setVisible(True)
        elif key == '检验':
            if self.patient_platform_result['province_other']['inspect'].empty:
                return
            for stand in self.patient_platform_result['province_other']['inspect']['ITEM']:
                item = QListWidgetItem(stand)
                item.setIcon(QIcon(':/app/images/report.png'))
                self.T1_Tab3_ListWidget.addItem(item)
            self.T1_Tab3_TextBrowser.hide()
            self.T1_Tab3_TableWidget.show()
            self.T1_Tab3_HLayout3.hide()

            self.info_badge_t3.setText(str(self.patient_platform_result['province_other']['inspect'].shape[0]))  # 小上标
            self.info_badge_t3.adjustSize()
            self.info_badge_t3.setVisible(True)

    def _connect_t1_button1_clicked(self):
        """ T1 Button1 按钮点击事件 """
        if self.basic_info.empty:
            return
        callback_phone = {'A': '', 'X': ''}
        win_bind = WindowFollow(self.basic_info.iloc[self.T1_RoundListWidget.currentRow()], 'Start',
                                self.doctor_info['TEL'], callback_phone)
        win_bind.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
        win_bind.exec_()
        if callback_phone['X']:
            title = f"绑定成功，请用{callback_phone['A']}拨打监管平台电话【{callback_phone['X']}】"
            content1 = f'系统将会自动接通对应的患者电话，通话全程录音。'
            w = BindingBox(title, content1, "", self)
            w.setClosableOnMaskClicked(True)
            w.exec()

    def _connect_t1_button2_clicked(self):
        """ T1 Button2 按钮点击事件 """
        if self.basic_info.empty:
            return
        info = self.basic_info.iloc[self.T1_RoundListWidget.currentRow()]
        finish = [False]
        win_update = WindowFollow(info, 'Confirm', callback_finish=finish)
        win_update.setWindowFlags(Qt.WindowStaysOnTopHint)  # 置于目前窗口前面
        win_update.exec_()
        if finish[0]:
            self.T1_RoundListWidget.clear()
            self._init_timeout_data()
            self.T1_RoundListWidget.setCurrentRow(-1)
            self._refresh_patient_data()
