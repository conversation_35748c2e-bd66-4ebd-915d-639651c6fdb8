import ast
import base64
import json
import time

import requests

from celery_task.WeChat.common import wx_session, send_text
from celery_task.WeChat.settings import TEMP_ID
from celery_task.celery import app
from celery_task.wx_small_program.wxsm_config import SERVICE_APPID, SERVICE_SECRET, SERVICE_GRANT_TYPE
from custom.db.execute import db
from custom.db.mysql import UsingMysql
from custom.db.redis import redis_client
from urllib.parse import quote

with UsingMysql() as um:
    privacy = ast.literal_eval(
        um.fetch_decrypt("select value from info_db where index_information = 'WeChat' ",
                         None, 'value'))


@app.task
def token() -> None:
    """ 获取Access token 写入redis """
    # 检查redis的wx_access_token过期时间
    if redis_client.ttl('wx_access_token') > 2100:  # 35分钟
        return

    api_url = f"https://api.weixin.qq.com/cgi-bin/stable_token"
    payload = {
        "grant_type": 'client_credential',
        "appid": privacy['appid'],
        "secret": privacy['secret'],
        "force_refresh": False  # 强制刷新token
    }
    suc, result = wx_session(api_url, payload=payload, method='post_json')
    if not suc:
        raise ValueError(result)
    assert isinstance(result, dict)
    if not result.get('access_token', ''):
        raise ValueError(f"获取access_token失败:{result.get('errmsg', '')}")
    if not redis_client.set('wx_access_token', result.get('access_token', ''), ex=result.get('expires_in', 0)):
        raise ValueError('redis设置失败')


@app.task
def qr_code(scene):
    """ 获取还未绑定用户的二维码图片 写入redis
    解码方法：result = res.get()
            decoded_data = base64.b64decode(result.encode('utf-8'))
            detected_format = imghdr.what(None, decoded_data)
            if detected_format not in ['jpeg']:
                raise ValueError("无效的图片格式")
            with open('12.jpg', 'wb') as f:
                try:
                    f.write(decoded_data)
                except (UnicodeEncodeError, binascii.Error) as e:
                    print(f"解码失败: {e}")
    """
    wx_token = redis_client.get('wx_access_token')
    api_url = f"https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token={wx_token}"
    payload = {
        "expire_seconds": 600,
        "action_name": 'QR_STR_SCENE',
        "action_info": {"scene": {"scene_str": scene}}
    }
    suc, result = wx_session(api_url, payload=payload, method='post_json')
    if not suc:
        raise ValueError(result)
    assert isinstance(result, dict)
    if not result.get('ticket', ''):
        raise ValueError(f"获取qr_code失败:{result.get('errmsg', '')}")

    # 下载二维码
    qr_url = f"https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket={quote(result.get('ticket', ''), safe='')}"
    suc, result = wx_session(qr_url)
    if not suc:
        raise ValueError(result)
    return base64.b64encode(result).decode('utf-8')


@app.task
def bind(event_key, open_id) -> None:
    """ event_key为发送过来的关键字， open_id为对方的微信id """
    # 如果传入的event_key以CODE_开头，那么就是获取验证码请求。
    if event_key.startswith('CODE_'):
        ids = event_key.split('_')[1].replace("'", "")
        db_data = db(['name', 'wx_id', 'pwd_reset_code'], 'yzzxyy.info_user', {'id': ids}, 'mysql')
        if db_data.empty:
            send_text(msg=f"系统暂未查询到用户【{ids}】的信息，请联系【信息化自研专班】处理。",
                      open_id=open_id)
            return
        if not db_data.iloc[0]['wx_id']:
            send_text(msg=f"用户【{db_data.iloc[0]['name']}({ids})】尚未完成微信绑定，请先绑定微信",
                      open_id=open_id)
            return
        if db_data.iloc[0]['wx_id'] != open_id:
            send_text(msg=f"用户【{db_data.iloc[0]['name']}({ids})】所绑定的微信不是当前微信，请用绑定的微信扫码！",
                      open_id=open_id)
            return
        send_text(
            msg=f"用户【{db_data.iloc[0]['name']}({ids})】找回密码的验证码为【{db_data.iloc[0]['pwd_reset_code']}】，有效期15分钟。",
            open_id=open_id)
        return
    # 首次扫码关注的用户传入的event_key会加上qrscene
    elif event_key.startswith('qrscene_'):
        event_key = event_key.split('_')[1].replace("'", "")

    ids = event_key.replace("'", "")
    name = db('name', 'yzzxyy.info_user', {'id': ids}, 'mysql')
    db(None, None,
       f"""UPDATE yzzxyy.info_user 
                    SET wx_id = '{open_id.replace("'", "")}' 
                    WHERE id = '{ids}'""",
       'mysql')

    # 新增逻辑，这里通过发请求，获取用户的 unionid，并存入数据库。
    appid = SERVICE_APPID
    secret = SERVICE_SECRET

    sql = f"""
            SELECT id, wx_id FROM yzzxyy.info_user WHERE id = '{ids}'
        """
    df = db(None, None, sql, 'mysql')
    if df.empty:
        return

    user_id = df.iloc[0]['id']
    openid = df.iloc[0]['wx_id']

    # 获取 access_token
    params = {
        'grant_type': SERVICE_GRANT_TYPE,
        'appid': appid,
        'secret': secret
    }
    response = requests.get('https://api.weixin.qq.com/cgi-bin/token', params=params)
    wechat_data = response.json()

    if 'access_token' not in wechat_data:
        return
    access_token = wechat_data['access_token']

    # 请求 unionid
    params = {
        'access_token': access_token,
        'openid': openid,
        'lang': 'zh_CN'
    }
    response = requests.get('https://api.weixin.qq.com/cgi-bin/user/info', params=params)
    user_info = response.json()

    unionid = user_info.get('unionid')
    if not unionid:
        return

    # 更新数据库
    update_sql = f"""
            UPDATE yzzxyy.info_user
            SET unionid = '{unionid}'
            WHERE id = '{user_id}'
        """
    db(None, None, update_sql, 'mysql')

    send_text(msg=(f"感谢您关注【永州市中心医院智慧医疗】\n"
                   f"账号已绑定【{name}({event_key})】\n"
                   f"谢谢您对【永州市中心医院信息化自研专班】的支持"),
              open_id=open_id)


@app.task
def bind_by_idcard(idcard, open_id, raw_text) -> None:
    """ idcard身份证号， open_id为对方的微信id, raw_text患者发来的原始文本 """
    idcard = idcard.replace("'", '').replace('x', 'X')
    his_info = db(None, None,
                  f"""SELECT NAME, DECODE(SEX_CODE, 'M', '男', '女') SEX
                                        FROM HNYZ_ZXYY.FIN_IPR_INMAININFO
                                        WHERE IDENNO IN ('{idcard}', '{idcard.replace('X', 'x')}')
                                         FETCH FIRST 1 ROWS ONLY""",
                  'oracle')
    if his_info.empty:
        send_text(msg="尚未查询到您的住院信息，请检查身份证号是否正确！", open_id=open_id)
        return
    info = his_info.iloc[0]
    if info['NAME'] not in raw_text:
        send_text(msg="姓名与身份证号码不匹配，请检查后重试！", open_id=open_id)
        return

    open_id_db = db('OPEN_ID', 'info_patient', {'ID_CARD': idcard}, 'mysql')
    if not open_id_db:
        # 患者尚未绑定
        db(None, None,
           f"""INSERT INTO info_patient (ID_CARD, NAME, SEX, OPEN_ID) 
           VALUES ('{idcard}', '{info['NAME']}', '{info['SEX']}', '{open_id}')""",
           'mysql')
        send_text(msg=f"尊敬的 {info['NAME']}{'先生' if info['SEX'] == '男' else '女士'} 您好\n"
                      f"您的健康档案已成功绑定当前微信号\n"
                      f"祝您身体健康",
                  open_id=open_id)
    else:
        # 账号已被绑定
        db(None, None,
           f"UPDATE info_patient SET OPEN_ID = '{open_id}' WHERE ID_CARD = '{idcard}'",
           'mysql')
        send_text(msg=f"尊敬的 {info['NAME']}{'先生' if info['SEX'] == '男' else '女士'} 您好\n"
                      f"您的健康档案已更新绑定当前微信号，原微信号绑定自动失效。\n"
                      f"祝您身体健康",
                  open_id=open_id)
        send_template({"thing1": {"value": info['NAME']},
                       "thing3": {"value": '账号被其他用户绑定'},
                       "time2": {"value": time.strftime("%Y年{}月{}日 %H:%M").format(*(time.localtime()[1:3]))}},
                      open_id_db, 'unbind')


@app.task
def send_template(data: dict, open_id, temp_type, url=None, miniprogram=None, client_msg_id=None) -> dict:
    """ event_key为发送过来的关键字， open_id为对方的微信id
    :param data:模板数据{"keyword1": {"value": "巧克力"},
                        "keyword2": {"value": "39.8元"},
                        "keyword3": {"value": "2014年9月22日"}
                        }
    :param open_id: 目标open_id
    :param temp_type: 模板名称
    :param url: 模板跳转URL
    :param miniprogram: 模板跳转小程序设置{"appid": "xiaochengxuappid12345","pagepath": "index?foo=bar"}
    :param client_msg_id: 防重ID，可以自主设置

    """
    wx_token = redis_client.get('wx_access_token')
    api_url = f"https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={wx_token}"
    payload = {"touser": open_id,
               "template_id": TEMP_ID.get(temp_type),
               "url": url,
               "miniprogram": miniprogram,
               "client_msg_id": client_msg_id,
               "data": data}
    suc, result = wx_session(api_url, payload=payload, method='post_json')
    if not suc:
        raise ValueError(result)
    assert isinstance(result, dict)
    return result
