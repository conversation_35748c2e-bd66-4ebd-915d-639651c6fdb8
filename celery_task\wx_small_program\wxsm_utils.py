# 微信小程序使用的工具类
import pandas

from custom.db.execute import db


def get_dept_name(username) -> pandas.DataFrame:
    """ 依据用户名获取权限科室和对应的ID """
    director = db('d_director', 'yzzxyy.info_user', {"id": username}, 'mysql')
    director = [] if not director else director.split('|')

    sql = f"""SELECT HNYZ_ZXYY.FUN_GET_DEPT_NAME(d.DEPT_CODE) AS DEPT_NAME, d.DEPT_CODE AS DEPT_ID
                FROM HNYZ_ZXYY.COM_PRIV_USER p
                         JOIN HNYZ_ZXYY.COM_DEPARTMENT d ON d.DEPT_CODE = p.DEPT_CODE
                WHERE p.USER_CODE = '{username}'
                  AND d.DEPT_TYPE = 'I'
                UNION
                SELECT HNYZ_ZXYY.FUN_GET_DEPT_NAME(DEPT_ID) AS DEPT_NAME, DEPT_ID
                FROM DAWN.DAWN_ORG_EMPL
                WHERE EMPL_ID = '{username}' OR DEPT_ID in ('{"', '".join(director)}') """
    return db(None, None, sql, 'oracle')