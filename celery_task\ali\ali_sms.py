# 阿里云短信模块
# -*- coding: utf-8 -*-
import asyncio
import ast

from alibabacloud_dysmsapi20170525.client import Client as Dysmsapi20170525Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dysmsapi20170525 import models as dysmsapi_20170525_models
from alibabacloud_tea_util import models as util_models

from custom.db.mysql import UsingMysql


class SMS:
    def __init__(self):
        pass

    @staticmethod
    def create_client(sign_name: str) -> Dysmsapi20170525Client:
        """
        使用AK&SK初始化账号Client
        @return: Client
        @throws Exception
        """
        with UsingMysql() as um:
            if sign_name == '永医预警':
                ali_m = um.fetch_decrypt("select value from info_db where index_information = 'ali' ", None, 'value')
            elif sign_name == '永州市中心医院':
                ali_m = um.fetch_decrypt("select value from info_db where index_information = 'ali_yzszxyy' ", None,
                                         'value')
        ali_params = ast.literal_eval(ali_m)

        # 阿里登录key
        config = open_api_models.Config(access_key_id=ali_params['id'], access_key_secret=ali_params['key'])
        # 服务器地址 Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
        config.endpoint = f'dysmsapi.aliyuncs.com'
        return Dysmsapi20170525Client(config)

    @staticmethod
    async def send_sms_async(args: dict, sign_name: str, template_code: str) -> dict:  # 单条
        if sign_name in ['永医预警', '永州市中心医院']:
            basic_args = {'sign_name': sign_name, 'template_code': template_code}
            client = SMS.create_client(sign_name)  # 初始化客户端
        else:
            return {'code': False}
        send_sms_request = dysmsapi_20170525_models.SendSmsRequest(**args, **basic_args)  # 设置短信内容
        runtime = util_models.RuntimeOptions()  # 高级配置（https://next.api.aliyun.com/api/Dysmsapi/2017-05-25/AddSmsSign）
        try:
            resp = await client.send_sms_with_options_async(send_sms_request, runtime)  # 同步发送短信
            return vars(resp.body)
        except Exception as error:
            # 错误 message
            print(error)


if __name__ == '__main__':
    print(asyncio.run(SMS.send_sms_async(
        {'phone_numbers': '15074620622',
         'template_param': '{"bed_no":"88","patient_name":"某某某","patient_no":"13533333","time":"2024/7/22","type":"医疗","software_name":"HIS"}'},
        '永医预警', 'SMS_469065912')))

    # 返回值：{'biz_id': '342208325719057857^0', 'code': 'OK', 'message': 'OK', 'request_id': 'D1B78161-556C-54DF-82E9-4C704303417E'}
