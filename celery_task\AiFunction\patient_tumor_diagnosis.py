import json

import pandas
import pandas as pd
from datetime import datetime, timedelta
import requests
import re
from typing import Union, List, Dict
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app


def check_tumor_records():
    """
    主任务函数：检查肿瘤患者记录并存入数据库
    处理流程：
        1. 从数据库获取起始时间
        2. 计算查询时间范围（起始时间 + 2小时）
        3. 查询符合条件的检查记录
        4. 使用AI判断肿瘤患者
        5. 将确诊患者存入patient_records表
        6. 更新下次查询的时间节点
    """
    try:
        # 步骤1: 获取起始时间
        start_time = db('value', 'yzzxyy.info_db',
                        {'index_information': 'warning_time'},
                        'mysql')

        if not start_time:
            raise ValueError("未找到起始时间配置")

        # 步骤2: 计算时间范围
        start_dt = datetime.strptime(start_time, '%Y-%m-%d %H:%M:%S')
        end_dt = start_dt + timedelta(hours=2)
        end_time = end_dt.strftime('%Y-%m-%d %H:%M:%S')

        # 步骤3: 查询检查记录
        tumor_keywords = "肿|癌|瘤|占位"
        sql = f"""
        SELECT DISTINCT TREATMENT_CODE, CHECK_RESULT
        FROM HISINTERFACE.TH_CHECK_RECORD
        WHERE CHECK_DATE BETWEEN TO_TIMESTAMP('{start_time}', 'YYYY-MM-DD HH24:MI:SS') 
            AND TO_TIMESTAMP('{end_time}', 'YYYY-MM-DD HH24:MI:SS')
            AND REGEXP_LIKE(CHECK_RESULT, '{tumor_keywords}')
        """


        # 执行查询
        records_df = db(None, None, sql, 'oracle')

        if records_df.empty:
            print("未找到符合条件的检查记录")
            # 即使没有记录也需要更新时间节点
            update_time_node(end_time)
            return

        # 按患者分组合并检查结果
        grouped = records_df.groupby(['TREATMENT_CODE'])['CHECK_RESULT'] \
            .apply(lambda x: '; '.join(x)) \
            .reset_index()

        batch_size = 10
        total_rows = len(grouped)
        all_results = []

        for start in range(0, total_rows, batch_size):
            end = min(start + batch_size, total_rows)
            batch_df = grouped.iloc[start:end]

            # 调用AI函数处理当前批次
            batch_result = ai_judge_tumor_patients(batch_df)

            # 确保结果格式正确
            if not isinstance(batch_result, list) or not all(isinstance(item, dict) for item in batch_result):
                raise ValueError(f"ai_judge_tumor_patients 返回了无效格式: {type(batch_result)}")

            all_results.extend(batch_result)

        # 保存所有结果到数据库


        if not all_results:
            print("未发现确诊肿瘤患者")
        else:
            save_to_patient_records(all_results)

        # 步骤6: 更新下次查询的时间节点
        update_time_node(end_time)

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def update_time_node(end_time):
    """更新查询时间节点"""
    update_sql = f"""
    UPDATE yzzxyy.info_db 
    SET value = '{end_time}' 
    WHERE index_information = 'warning_time'
    """
    db(None, None, update_sql, 'mysql')
    print(f"成功更新查询时间节点至: {end_time}")


def ai_judge_tumor_patients(records_df: DataFrame) -> List[Dict]:
    """
    使用AI判断肿瘤患者
    :param records_df: 包含患者检查结果的DataFrame
    :return: 确诊肿瘤患者列表 [{"case_id": 456}, ...]
    """



    # 构建AI提示词
    prompt = """
        请根据以下患者的检查结果判断是否为肿瘤或癌症：
        要求：
        1. 明确诊断为恶性肿瘤以及疑似恶性肿瘤的的情况应为高百分比
        2. 明确良性肿瘤的百分比为0
        3. 对于每一行诊断的结果判断，应该生成可能性百分比，
        4，以上条件并不绝对，你要把自己当成一个医生来判断
        患者检查结果列表：
        """
    # 准备AI输入数据
    # patient_records = []
    # for _, row in records_df.iterrows():
    #     patient_records.append({
    #         'case_id': row['TREATMENT_CODE'],
    #         'check_result': row['CHECK_RESULT']
    #     })

    for _, row in records_df.iterrows():
        prompt += f"\n病历号:{row['TREATMENT_CODE']}): {row['CHECK_RESULT']}"


    # for i, record in enumerate(patient_records):
    #     prompt += f"\n病历号:{record['case_id']}): {record['check_result']}"

    prompt += """
    输出要求：
    1. 输出确诊患者的病历号病历号和它的它可能恶性肿瘤的百分比
    2. 每行一个病历号和它的它可能恶性肿瘤的百分比
    采用jason格式返回
    字段:{
     case_id:病历号，
     percent:百分比
    }
    请直接给出判断结果：
    """

    # 调用AI服务
    try:
        response = requests.post(
            "http://192.168.186.168:12434/api/generate",
            json={
                "model": 'qwen3:8b-q4_K_M',
                "prompt": prompt,
                "stream": False,
                "think": False

            },
            timeout=120  # 增加超时时间
        )
        response.raise_for_status()  # 检查HTTP错误
    except requests.exceptions.RequestException as e:
        print(f"AI服务调用失败: {str(e)}")
        return []

    # 处理AI响应
    ai_output = response.json().get("response", "").strip()
    clean_response = re.sub(r'<think>.*?</think>', '', ai_output, flags=re.DOTALL).strip()


    return parse_ai_response(clean_response)


def save_to_patient_records(patients: List[Dict]):
    """
    将确诊患者存入patient_records表
    :param patients: 患者列表 [{"case_id": "123", "percent": 5}, ...]
    """
    if not patients:
        print("没有患者数据需要保存")
        return

    print(f"准备保存 {len(patients)} 条患者记录")

    # 安全过滤和准备数据
    safe_values = []
    for patient in patients:
        try:
            # 清理case_id - 只保留字母、数字和常见病历号符号
            case_id = patient['case_id']
            clean_id = re.sub(r'[^a-zA-Z0-9_-]', '', str(case_id))

            # 确保percent是整数
            percent = int(patient['percent'])

            if clean_id:  # 确保不是空字符串
                # 创建元组格式的值
                safe_values.append((clean_id, percent))
        except (KeyError, ValueError, TypeError) as e:
            print(f"跳过无效患者记录: {patient} - 错误: {str(e)}")
            continue

    if not safe_values:
        print("过滤后没有有效的病历号可插入")
        return

    print(f"有效病历号数量: {len(safe_values)}")

    # 构建插入SQL - 每100条记录分批插入
    batch_size = 100
    for i in range(0, len(safe_values), batch_size):
        batch = safe_values[i:i + batch_size]

        # 构建值字符串
        values_list = []
        for case_id, percent in batch:
            values_list.append(f"('{case_id}', {percent})")

        values_str = ", ".join(values_list)

        sql = f"""
        INSERT INTO yzzxyy.patient_records (case_id, percent)
        VALUES {values_str}
        """

        print(f"执行SQL:\n{sql}")

        try:
            # 执行插入
            result = db(None, None, sql, 'mysql')
            print(f"成功插入{len(batch)}条肿瘤患者记录，结果: {result}")
        except Exception as e:
            print(f"插入数据库时出错: {str(e)}")
            # 可以选择记录错误但继续处理下一批


def parse_ai_response(response_text: str) -> List[Dict]:
    """
    解析AI返回的JSON格式响应
    :param response_text: AI返回的文本
    :return: 结构化患者数据列表
    """
    try:
        # 尝试直接解析为JSON
        data = json.loads(response_text)
        if isinstance(data, list):
            return data
        elif isinstance(data, dict):
            return [data]
    except json.JSONDecodeError:
        pass

    # 如果直接解析失败，尝试提取JSON部分
    try:
        # 查找可能的JSON数组或对象
        json_matches = re.findall(r'\[.*\]|\{.*\}', response_text, re.DOTALL)
        if json_matches:
            return json.loads(json_matches[0])
    except Exception as e:
        print(f"JSON解析失败: {str(e)}")

    # 最后尝试手动提取数据
    patients = []
    pattern = r'{\s*"case_id"\s*:\s*"([^"]+)"\s*,\s*"percent"\s*:\s*(\d+)\s*}'
    matches = re.findall(pattern, response_text)

    for case_id, percent in matches:
        try:
            patients.append({
                "case_id": case_id.strip(),
                "percent": int(percent)
            })
        except ValueError:
            continue

    return patients
# 定时任务入口
@app.task
def start_tumor_check():
    check_tumor_records()


if __name__ == "__main__":
    # 测试执行
    # start_tumor_check()
    batch_df = pandas.DataFrame({'TREATMENT_CODE': ['001'], 'CHECK_RESULT':["""1.左上肺占位并双肺结节灶、左侧部分肋骨骨质破坏伴邻近胸壁软组织增厚，考虑肿瘤性病变并多发转移，请结合相关检查及追踪复查；
2.考虑支气管疾患并肺气肿，双肺多发炎症灶，部分间质性，另气管及左侧主支气管内高密度影，考虑痰栓；
3.主动脉及冠状动脉硬化；
以上请结合临床及随诊复查。"""]})
    ai_judge_tumor_patients(batch_df)