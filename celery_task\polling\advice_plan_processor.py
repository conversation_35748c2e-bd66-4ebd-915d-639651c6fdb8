import copy
import json
import pandas as pd
import re
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app
from typing import Union, List, Dict
from datetime import datetime
import time
import threading
import queue
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass

# 超时异常类
class TimeoutError(Exception):
    pass

# 多线程配置
@dataclass
class ThreadConfig:
    max_workers: int = 5  # 最大工作线程数
    queue_size: int = 10  # 任务队列大小
    ai_rate_limit: float = 2.0  # AI调用间隔（秒）
    db_timeout: float = 10.0  # 数据库操作超时
    retry_attempts: int = 3  # 重试次数

# 全局配置实例
config = ThreadConfig()

# 线程安全的计数器和锁
class ThreadSafeCounter:
    def __init__(self):
        self._value = 0
        self._lock = threading.Lock()

    def increment(self):
        with self._lock:
            self._value += 1
            return self._value

    def get(self):
        with self._lock:
            return self._value

# 全局统计
stats = {
    'processed': ThreadSafeCounter(),
    'success': ThreadSafeCounter(),
    'failed': ThreadSafeCounter()
}

# AI调用限流器
class RateLimiter:
    def __init__(self, rate_limit: float):
        self.rate_limit = rate_limit
        self.last_call = 0
        self.lock = threading.Lock()

    def acquire(self):
        with self.lock:
            now = time.time()
            time_since_last = now - self.last_call
            if time_since_last < self.rate_limit:
                sleep_time = self.rate_limit - time_since_last
                time.sleep(sleep_time)
            self.last_call = time.time()

# 全局限流器
ai_limiter = RateLimiter(config.ai_rate_limit)

# 数据库操作锁（确保数据库写入的线程安全）
db_lock = threading.Lock()


def build_optimized_prompt(advice_content: str) -> str:
    """
    构建优化后的AI提示词

    :param advice_content: 医嘱内容
    :return: 优化后的提示词
    """
    return f"""你是专业医疗随访计划专家。仔细分析医嘱内容，提取所有随访相关信息，生成完整的JSON格式随访计划。
医嘱内容：{advice_content}
分析重点：
1.寻找时间关键词：如"每"、"月"、"年"、"天"、"周"，“半年”等
2.识别具体时间：如"1个月"、"3-4个月"、"半年"、"2年内"、"每年"等
3.判断周期性任务，如出现“每”等
输出JSON格式示例：
[{{"cycle":1,"interval":3,"unit":"月","exact_date":"","content":"每3个月复查CT","end_time":,"end_unit":"","end_date":""}},{{"cycle":0,"interval":1,"unit":"月","exact_date":"","content":"术后1个月复查","end_time":0,"end_unit":"","end_date":""}}]
字段规则：
- cycle：1=定期重复随访，0=单次随访
- interval：时间间隔数值（如3个月写3，半年写6），且可以是小数，如果是“3-4月”就填3.5
- unit：时间单位（天/周/月/年）
- content：具体随访内容和检查项目
- end_time：结束时间，如果没有直接写结束时间，就不用管
- end_unit：结束时间的单位
如果医嘱中没有明确的随访安排，返回：[{{"cycle":0,"interval":0,"unit":"","exact_date":"","content":"","end_time":0,"end_unit":"","end_date":""}}]
请仔细分析医嘱内容，提取所有随访信息，直接返回JSON数组："""


def clean_prompt_text(prompt: str) -> str:
    """
    清理提示词文本，删除多余的换行和空格
    
    :param prompt: 原始提示词
    :return: 清理后的提示词
    """
    # 删除多余的换行符
    cleaned = re.sub(r'\n+', ' ', prompt)
    # 删除多余的空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 删除首尾空格
    return cleaned.strip()

def process_advice_to_plan():
    """
    主任务函数：从医嘱记录中提取内容，使用AI生成随访计划
    """
    try:
        # 步骤1: 查询医嘱内容
        advice_sql = "SELECT VALUE FROM HIT_MDC.MDC_RCD_IN_RECORD_ITEM WHERE ELEMENT_ID = '369'"
        
        # 执行查询获取医嘱内容
        advice_records = db(None, None, advice_sql, 'oracle')
        
        if advice_records.empty:
            print("未找到医嘱记录")
            return
            
        print(f"找到{len(advice_records)}条医嘱记录")
        
        # 处理每条医嘱记录
        for index, row in advice_records.iterrows():
            advice_content = row['VALUE'] if pd.notna(row['VALUE']) else ""
            
            if not advice_content.strip():
                print(f"第{index+1}条记录医嘱内容为空，跳过")
                continue
                
            print(f"正在处理第{index+1}条医嘱记录...")
            
            # 调用AI生成随访计划
            plan_json = generate_follow_plan_with_ai(advice_content)
            
            # 插入到advice_to_plan表
            success = insert_advice_plan(advice_content, plan_json)
            
            if success:
                print(f"✓ 第{index+1}条医嘱记录处理成功")
            else:
                print(f"✗ 第{index+1}条医嘱记录处理失败")
                
            print("-" * 50)
            
            # 添加延迟避免AI服务过载
            time.sleep(1)
            
        print("医嘱随访计划生成完成")
        
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def generate_follow_plan_with_ai(advice_content: str) -> str:
    """
    使用AI将医嘱内容转换为随访计划JSON格式
    
    :param advice_content: 医嘱内容
    :return: 随访计划JSON字符串
    """
    try:
        # 构建优化后的AI提示词
        prompt = build_optimized_prompt(advice_content)
        
        # 清理提示词：删除多余的换行和空格
        cleaned_prompt = clean_prompt_text(prompt)
        NEW_STR=copy.deepcopy(cleaned_prompt)
        # 调用本地AI服务
        import requests
        response = requests.post(
            "http://***************:12434/api/generate",
            json={
                "model": 'qwen3:8b-q4_K_M',
                "prompt": NEW_STR,
                "stream": False,
                "think": True
            },
            #timeout=60  # 60秒超时
        )
        
        if response.status_code == 200:
            response_data = response.json()
            ai_output = response_data.get('response', '').strip()
            print(f"AI响应: {ai_output}")
            
            # 解析并验证JSON格式
            plan_json = parse_and_validate_plan_json(ai_output)
            return plan_json
        else:
            print(f"AI服务调用失败，状态码: {response.status_code}")
            return get_default_plan_json()
            
    except Exception as e:
        print(f"AI服务调用异常: {str(e)}")
        return get_default_plan_json()


def parse_and_validate_plan_json(ai_response: str) -> str:
    """
    解析和验证AI返回的随访计划JSON

    :param ai_response: AI返回的响应文本
    :return: 验证后的JSON字符串
    """
    try:
        # 清理AI响应，移除markdown格式标记
        cleaned_response = ai_response.strip()

        # 移除可能的markdown代码块标记
        if cleaned_response.startswith('```json'):
            cleaned_response = cleaned_response[7:]  # 移除 ```json
        if cleaned_response.startswith('```'):
            cleaned_response = cleaned_response[3:]   # 移除 ```
        if cleaned_response.endswith('```'):
            cleaned_response = cleaned_response[:-3]  # 移除结尾的 ```

        cleaned_response = cleaned_response.strip()

        # 尝试直接解析整个响应
        try:
            parsed_json = json.loads(cleaned_response)
            if isinstance(parsed_json, list):
                # 验证并补充字段
                for item in parsed_json:
                    if not isinstance(item, dict):
                        raise ValueError("JSON元素不是字典格式")

                    required_fields = ['cycle', 'interval', 'unit', 'exact_date',
                                     'content', 'end_time', 'end_unit', 'end_date']
                    for field in required_fields:
                        if field not in item:
                            item[field] = "" if field in ['unit', 'exact_date', 'content', 'end_unit', 'end_date'] else 0

                return json.dumps(parsed_json, ensure_ascii=False)
        except json.JSONDecodeError:
            pass

        # 如果直接解析失败，尝试提取JSON数组部分
        json_matches = re.findall(r'\[.*?\]', cleaned_response, re.DOTALL)
        if json_matches:
            json_str = json_matches[0]
            # 验证JSON格式
            parsed_json = json.loads(json_str)

            # 验证是否为列表格式
            if isinstance(parsed_json, list):
                # 验证每个元素是否包含必要字段
                for item in parsed_json:
                    if not isinstance(item, dict):
                        raise ValueError("JSON元素不是字典格式")

                    required_fields = ['cycle', 'interval', 'unit', 'exact_date',
                                     'content', 'end_time', 'end_unit', 'end_date']
                    for field in required_fields:
                        if field not in item:
                            item[field] = "" if field in ['unit', 'exact_date', 'content', 'end_unit', 'end_date'] else 0

                return json.dumps(parsed_json, ensure_ascii=False)
            else:
                raise ValueError("JSON不是列表格式")
        else:
            raise ValueError("未找到有效的JSON格式")

    except Exception as e:
        print(f"JSON解析失败: {str(e)}")
        return get_default_plan_json()


def get_default_plan_json() -> str:
    """
    获取默认的随访计划JSON
    
    :return: 默认JSON字符串
    """
    default_plan = [
        {
            "cycle": 0,
            "interval": 0,
            "unit": "",
            "exact_date": "",
            "content": "",
            "end_time": 0,
            "end_unit": "",
            "end_date": ""
        }
    ]
    return json.dumps(default_plan, ensure_ascii=False)


def insert_advice_plan(advice_content: str, plan_json: str) -> bool:
    """
    将医嘱和随访计划插入到advice_to_plan表中

    :param advice_content: 医嘱内容
    :param plan_json: 随访计划JSON
    :return: 是否插入成功
    """
    try:
        # 转义单引号以防SQL注入
        advice_escaped = advice_content.replace("'", "''")
        plan_escaped = plan_json.replace("'", "''")

        insert_sql = f"""
        INSERT INTO follow.advice_to_plan (ADVICE, PLAN)
        VALUES ('{advice_escaped}', '{plan_escaped}')
        """

        # 执行插入操作
        result = db(None, None, insert_sql, 'mysql')
        print(f"成功插入医嘱随访计划记录")
        return True

    except Exception as e:
        print(f"插入数据库失败: {str(e)}")
        return False


def main():
    """
    主函数：执行医嘱到随访计划的转换任务
    """
    print("开始执行医嘱随访计划生成任务...")

    try:
        # 处理医嘱记录
        process_advice_to_plan()

        print("医嘱随访计划生成任务完成！")
        return True

    except Exception as e:
        print(f"任务执行失败: {str(e)}")
        return False


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
