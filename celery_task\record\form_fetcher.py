import requests
from bs4 import BeautifulSoup
from typing import Optional, Dict, Union
from urllib.parse import urljoin


def _parse_html_and_extract_form(
        soup: BeautifulSoup,
        url: str,
        return_type: str = "json"
) -> Dict[str, Union[str, Dict, list]]:
    """
    辅助函数，从一个BeautifulSoup对象中解析并提取表单数据。
    """
    # 如果要求返回HTML内容，直接返回
    if return_type == "html":
        title = soup.find('title')
        page_title = title.get_text(strip=True) if title else "表单页面"
        return {
            "status": "success",
            "url": url,
            "content_type": "text/html",
            "html_content": soup.prettify(),
            "title": page_title
        }

    # 移除script和style标签，避免干扰
    for tag in soup(["script", "style"]):
        tag.decompose()

    # 查找表单
    forms = soup.find_all('form')

    if not forms:
        # 如果没有找到form标签，查找可能包含表单元素的容器
        content_selectors = [
            '#content', '.content', '#main', '.main',
            '#container', '.container', 'body > div',
            'body > table', 'body'
        ]

        for selector in content_selectors:
            try:
                container = soup.select_one(selector)
                if container:
                    # 在容器中查找表单元素
                    inputs = container.find_all(
                        ['input', 'select', 'textarea', 'button'])
                    if inputs:
                        # 创建虚拟表单数据
                        form_data = _extract_form_elements(container)
                        return {
                            "status": "success",
                            "url": url,
                            "form_data": form_data,
                            "form_action": "",
                            "form_method": "GET"
                        }
            except:
                continue

        return {
            "status": "error",
            "url": url,
            "error": "页面中未找到表单或表单元素"
        }

    # 处理找到的表单（取第一个）
    form = forms[0]
    form_action = form.get('action', '')
    form_method = form.get('method', 'GET').upper()

    # 提取表单元素
    form_data = _extract_form_elements(form)
    print("已经到这里了")
    return {
        "status": "success",
        "url": url,
        "form_data": form_data,
        "form_action": form_action,
        "form_method": form_method
    }


def fetch_html_form_data(
        url: str,
        cookies: Optional[Dict[str, str]] = None,
        timeout: int = 10,
        return_type: str = "json",  # "json" or "html"
        auto_submit_first_form: bool = True,
        **_: Dict,
) -> Dict[str, Union[str, Dict, list]]:
    """抓取页面中的form表单内容，解析并返回JSON格式数据或完整HTML。
    如果 auto_submit_first_form 为 True，则会自动提交抓取到的第一个表单，
    然后解析提交后返回页面的内容。
    """
    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/137.0.0.0 Safari/537.36"
        ),
        "Accept": (
            "text/html,application/xhtml+xml,application/xml;"
            "q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8"
        ),
    }

    try:
        # --- 阶段1: 抓取初始页面 ---
        resp = requests.get(
            url,
            headers=headers,
            cookies=cookies,
            timeout=timeout,
            allow_redirects=True,
            verify=False,
        )
        resp.raise_for_status()
        resp.encoding = resp.apparent_encoding or resp.encoding or 'utf-8'

        # --- 阶段2: 自动提交表单 ---
        initial_soup = BeautifulSoup(resp.text, 'html.parser')
        initial_forms = initial_soup.find_all('form')
        if not initial_forms:
            return {"status": "error", "url": url, "error": "自动提交失败：初始页面未找到表单。"}

        form = initial_forms[0]
        form_action = form.get('action', '')
        form_method = form.get('method', 'GET').upper()

        if form_method != 'POST':
            return {"status": "error", "url": url, "error": f"自动提交仅支持POST方法，但找到的是 {form_method}。"}

        initial_form_data = _extract_form_elements(form)
        # 确保 input 有 name 属性
        payload = {inp['name']: inp['value'] for inp in initial_form_data.get('inputs', []) if inp.get('name')}
        submission_url = urljoin(url, form_action)

        # --- 阶段3: 发起POST请求，获取最终页面 ---
        post_resp = requests.post(
            submission_url,
            headers=headers,
            cookies=cookies,
            data=payload,
            timeout=timeout,
            allow_redirects=True,
            verify=False
        )
        post_resp.raise_for_status()
        post_resp.encoding = post_resp.apparent_encoding or post_resp.encoding or 'utf-8'

        # --- 阶段4: 解析最终页面 ---
        final_soup = BeautifulSoup(post_resp.text, 'html.parser')
        final_data = {}
        has_data = False

        # 检查是否为门诊医嘱页面
        is_outpatient_page = bool(
            final_soup.find(lambda tag: tag.name in ['h1', 'title', 'div'] and '门诊医嘱' in tag.get_text(strip=True)))

        # 检查并解析各种可能的表格
        if final_soup.find('h1', string='护理记录单'):
            result = _parse_nursing_record_sheet(final_soup)
            if result.get("status") == "success":
                final_data["nursing_records"] = result.get("data")
                has_data = True

        if is_outpatient_page:
            # 专门处理门诊医嘱页面
            order_table = final_soup.find('table', id='LongOrder')
            if order_table:
                result = _parse_order_table(order_table)
                if result.get("status") == "success":
                    final_data["outpatient_orders"] = result.get("data")
                    has_data = True
        else:
            # 处理住院医嘱（长期和临时）
            long_term_table = final_soup.find('table', id='LongOrder')
            if long_term_table:
                result = _parse_order_table(long_term_table)
                if result.get("status") == "success":
                    final_data["long_term_orders"] = result.get("data")
                    has_data = True

            temp_order_table = final_soup.find('table', id='TempOrder')
            if temp_order_table:
                result = _parse_order_table(temp_order_table)
                if result.get("status") == "success":
                    final_data["temporary_orders"] = result.get("data")
                    has_data = True

        if has_data:
            return {"status": "success", "data": final_data}
        else:
            # 如果没有找到特定表格，则使用通用表单解析逻辑
            return _parse_html_and_extract_form(final_soup, post_resp.url, return_type)

    except requests.RequestException as e:
        return {
            "status": "error",
            "url": url,
            "error": f"请求失败：{str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "url": url,
            "error": f"内容处理失败：{str(e)}"
        }


def _parse_nursing_record_sheet(soup: BeautifulSoup) -> Dict:
    """
    专门解析"护理记录单"页面的HTML表格，提取结构化数据。
    """
    try:
        table = soup.select_one("#newTable > table")
        if not table:
            return {"status": "error", "error": "解析失败：未能在页面中找到护理记录单表格。"}

        # 表头是固定的，直接定义以提高稳定性
        headers = [
            "日期", "时间", "病情程度", "护理级别",
            "生命体征_体温", "生命体征_脉搏", "生命体征_心率", "生命体征_呼吸", "生命体征_血压",
            "血氧饱和度", "意识", "瞳孔_左", "瞳孔_右",
            "入量_途径", "入量_名称", "入量_量",
            "出量_名称", "出量_性状", "出量_色", "出量_量",
            "氧疗_方式", "氧疗_流量",
            "其他_伤口", "其他_皮肤", "其他_安全护理",
            "病情观察、护理措施及效果", "签名"
        ]

        tbody = table.find('tbody')
        if tbody:
            data_rows = tbody.find_all('tr')[2:]
        else:
            # Fallback for tables without a <tbody> tag, skip header rows
            data_rows = table.find_all('tr')[2:]

        if not data_rows:
            return {"status": "error", "error": "解析失败：找到了表格但未能提取任何数据行。"}

        # 用于合并跨行文本
        combined_records = []
        temp_record = None

        for row in data_rows:
            cells = row.find_all('td')
            # 跳过不规范的行
            if len(cells) != len(headers):
                continue

            row_data = [cell.get_text(strip=True) for cell in cells]

            # 如果第一列有日期，说明是新记录的开始
            if row_data[0]:
                if temp_record:
                    combined_records.append(temp_record)

                # 创建嵌套字典，而不是扁平字典
                temp_record = {}
                for header, value in zip(headers, row_data):
                    if "_" in header:
                        parts = header.split("_", 1)
                        main_key = parts[0]
                        sub_key = parts[1]
                        if main_key not in temp_record:
                            temp_record[main_key] = {}
                        temp_record[main_key][sub_key] = value
                    else:
                        temp_record[header] = value
            else:
                # 如果不是新记录，将观察文本追加到上一条记录
                if temp_record:
                    observation_text = row_data[headers.index("病情观察、护理措施及效果")]
                    if observation_text:
                        temp_record["病情观察、护理措施及效果"] += " " + observation_text

        if temp_record:
            combined_records.append(temp_record)

        return {"status": "success", "data": combined_records}

    except Exception as e:
        return {"status": "error", "data": [], "error": f"解析护理记录单时发生错误: {str(e)}"}


def _parse_order_table(table: BeautifulSoup) -> Dict:
    """
    专门解析医嘱单表格（长期或临时），提取结构化数据，并过滤掉签名列。
    """
    try:
        header_row = table.find('tr')
        if not header_row:
            return {"status": "error", "error": "解析失败：医嘱单表格没有表头行。"}

        all_headers = [th.get_text(strip=True) for th in header_row.find_all('th')]
        if not all_headers:
            return {"status": "error", "error": "解析失败：未能从表头行提取任何列名。"}

        # 补偿HTML中第一个没有文本的表头作为分组标识
        if not all_headers[0]:
            all_headers[0] = "group_indicator"

        # 动态确定医嘱主列的名称 (e.g., '长期医嘱' or '临时医嘱')
        main_order_column_name = all_headers[1]

        headers_to_keep = []
        indices_to_keep = []
        for i, header in enumerate(all_headers):
            if "签名" not in header:
                headers_to_keep.append(header)
                indices_to_keep.append(i)

        data_rows = table.select('tr.rowstyle, tr.alternatingrowstyle')
        if not data_rows:
            return {"status": "error", "error": "解析失败：医嘱单表格中未找到任何数据行。"}

        records = []
        for row in data_rows:
            cells = row.find_all('td')
            if len(cells) != len(all_headers):
                continue

            filtered_cells_text = [cells[i].get_text(strip=True) for i in indices_to_keep]
            record = dict(zip(headers_to_keep, filtered_cells_text))
            records.append(record)

        # 合并关联的医嘱项
        grouped_records = []
        for record in records:
            indicator = record.get("group_indicator")
            if indicator in ['┗', '┃'] and grouped_records:
                current_order_text = record.get(main_order_column_name, "")
                if current_order_text:
                    grouped_records[-1][main_order_column_name] += f" + {current_order_text}"
            else:
                grouped_records.append(record)

        # 从最终结果中移除不必要的标识列
        for record in grouped_records:
            if "group_indicator" in record:
                del record["group_indicator"]

        return {"status": "success", "data": grouped_records}
    except Exception as e:
        return {"status": "error", "data": [], "error": f"解析医嘱单时发生错误: {str(e)}"}


def _extract_form_elements(form_container) -> Dict[str, list]:
    """从表单容器中提取所有表单元素的详细信息。
    """
    form_data = {
        "inputs": [],
        "selects": [],
        "textareas": [],
        "buttons": [],
        "other_elements": []
    }

    # 提取 input 元素
    inputs = form_container.find_all('input')
    for inp in inputs:
        input_data = {
            "name": inp.get('name', ''),
            "type": inp.get('type', 'text'),
            "value": inp.get('value', ''),
            "id": inp.get('id', ''),
            "class": inp.get('class', []),
            "placeholder": inp.get('placeholder', ''),
            "required": inp.has_attr('required'),
            "disabled": inp.has_attr('disabled'),
            "readonly": inp.has_attr('readonly')
        }

        # 对于 checkbox 和 radio，添加 checked 状态
        if input_data["type"] in ["checkbox", "radio"]:
            input_data["checked"] = inp.has_attr('checked')

        form_data["inputs"].append(input_data)

    # 提取 select 元素
    selects = form_container.find_all('select')
    for sel in selects:
        options = []
        for opt in sel.find_all('option'):
            options.append({
                "value": opt.get('value', ''),
                "text": opt.get_text(strip=True),
                "selected": opt.has_attr('selected')
            })

        select_data = {
            "name": sel.get('name', ''),
            "id": sel.get('id', ''),
            "class": sel.get('class', []),
            "multiple": sel.has_attr('multiple'),
            "required": sel.has_attr('required'),
            "disabled": sel.has_attr('disabled'),
            "options": options,
            "selected_value": sel.get('value', '')
        }

        form_data["selects"].append(select_data)

    # 提取 textarea 元素
    textareas = form_container.find_all('textarea')
    for ta in textareas:
        textarea_data = {
            "name": ta.get('name', ''),
            "id": ta.get('id', ''),
            "class": ta.get('class', []),
            "value": ta.get_text(),
            "rows": ta.get('rows', ''),
            "cols": ta.get('cols', ''),
            "placeholder": ta.get('placeholder', ''),
            "required": ta.has_attr('required'),
            "disabled": ta.has_attr('disabled'),
            "readonly": ta.has_attr('readonly')
        }

        form_data["textareas"].append(textarea_data)

    # 提取其他可能的表单相关元素
    other_elements = form_container.find_all(['label', 'fieldset', 'legend'])
    for elem in other_elements:
        element_data = {
            "tag": elem.name,
            "text": elem.get_text(strip=True),
            "for": elem.get('for', ''),  # 主要针对 label
            "id": elem.get('id', ''),
            "class": elem.get('class', [])
        }

        form_data["other_elements"].append(element_data)

    return form_data 