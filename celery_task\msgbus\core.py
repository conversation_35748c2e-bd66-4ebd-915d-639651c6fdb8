"""
消息总线核心模块 - 简化版
定义消息类型、载荷、处理器等核心组件
"""

import time
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional

from .config import ERROR_CODES


class MessageType(Enum):
    """消息类型枚举"""
    VOICE_TTS = "voice_tts"           # TTS语音通知
    VOICE_VOX = "voice_vox"           # VOX录音播放
    WECHAT_TEMPLATE = "wechat_template"  # 微信模板消息


class MessagePriority(Enum):
    """消息优先级"""
    LOW = 8
    NORMAL = 5
    HIGH = 3
    URGENT = 2


@dataclass
class MessagePayload:
    """消息载荷"""
    message_type: MessageType
    target_id: str                              # 目标ID（手机号、openid等）
    content: Dict[str, Any]                     # 消息内容
    priority: MessagePriority = MessagePriority.NORMAL
    metadata: Optional[Dict[str, Any]] = None   # 元数据

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if 'message_id' not in self.metadata:
            self.metadata['message_id'] = str(uuid.uuid4())
        if 'created_at' not in self.metadata:
            self.metadata['created_at'] = datetime.now().isoformat()


@dataclass
class MessageResult:
    """消息发送结果"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None
    duration: Optional[float] = None  # 执行时间(毫秒)
    message_id: Optional[str] = None


class MessageBus:
    """消息总线 - 简化版"""

    def __init__(self):
        self._handlers: Dict[MessageType, Any] = {}

    def register_handler(self, message_type: MessageType, handler):
        """注册消息处理器"""
        self._handlers[message_type] = handler

    def send_message(self, payload: MessagePayload) -> MessageResult:
        """发送消息 - 改进版"""
        start_time = time.time()

        try:
            # 获取处理器
            handler = self._handlers.get(payload.message_type)
            if not handler:
                return self._create_error_result(
                    f"No handler found for message type: {payload.message_type}",
                    ERROR_CODES.get('NO_HANDLER', 'NO_HANDLER'),
                    start_time,
                    payload
                )

            # 验证载荷（如果处理器支持）
            if hasattr(handler, 'validate_payload') and not handler.validate_payload(payload):
                return self._create_error_result(
                    "Invalid message payload",
                    ERROR_CODES.get('INVALID_PAYLOAD', 'INVALID_PAYLOAD'),
                    start_time,
                    payload
                )

            # 发送消息
            result = handler.send(payload)

            # 添加执行时间和消息ID
            result.duration = (time.time() - start_time) * 1000
            result.message_id = payload.metadata.get('message_id')

            return result

        except Exception as e:
            return self._create_error_result(
                f"Message sending failed: {str(e)}",
                ERROR_CODES.get('SEND_ERROR', 'SEND_ERROR'),
                start_time,
                payload
            )

    def _create_error_result(self, message: str, error_code: str,
                           start_time: float, payload: MessagePayload) -> MessageResult:
        """创建错误结果"""
        return MessageResult(
            success=False,
            message=message,
            error_code=error_code,
            duration=(time.time() - start_time) * 1000,
            message_id=payload.metadata.get('message_id')
        )


# 全局消息总线实例
message_bus = MessageBus()
