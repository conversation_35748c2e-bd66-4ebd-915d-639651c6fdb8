import re

from celery_task.record.proxy import fetch
from custom.db.execute import db

#print(fetch(operation="celery_task.record.utils.get_pdf_binary_from_url", args=["http://175.16.8.68/EmrView/EmrTemp/202507081027030525624.pdf"]))
def build_optimized_prompt(advice_content: str) -> str:
    """
    构建优化后的AI提示词

    :param advice_content: 医嘱内容
    :return: 优化后的提示词
    """
    return f"""你是专业医疗随访计划专家。仔细分析医嘱内容，提取所有随访相关信息，生成完整的JSON格式随访计划。
医嘱内容：{advice_content}
分析重点：
1.寻找时间关键词：如"每"、"月"、"年"、"天"、"周"，“半年”等
2.识别具体时间：如"1个月"、"3-4个月"、"半年"、"2年内"、"每年"等
3.判断周期性任务，如出现“每”等
输出JSON格式示例：
[{{"cycle":1,"interval":3,"unit":"月","exact_date":"","content":"每3个月复查CT","end_time":,"end_unit":"","end_date":""}},{{"cycle":0,"interval":1,"unit":"月","exact_date":"","content":"术后1个月复查","end_time":0,"end_unit":"","end_date":""}}]
字段规则：
- cycle：1=定期重复随访，0=单次随访
- interval：时间间隔数值（如3个月写3，半年写6），且可以是小数，如果是“3-4月”就填3.5
- unit：时间单位（天/周/月/年）
- content：具体随访内容和检查项目
- end_time：结束时间，如果没有直接写结束时间，就不用管
- end_unit：结束时间的单位
如果医嘱中没有明确的随访安排，返回：[{{"cycle":0,"interval":0,"unit":"","exact_date":"","content":"","end_time":0,"end_unit":"","end_date":""}}]
请仔细分析医嘱内容，提取所有随访信息，直接返回JSON数组："""


def clean_prompt_text(prompt: str) -> str:
    """
    清理提示词文本，删除多余的换行和空格

    :param prompt: 原始提示词
    :return: 清理后的提示词
    """
    # 删除多余的换行符
    cleaned = re.sub(r'\n+', ' ', prompt)
    # 删除多余的空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    # 删除首尾空格
    return cleaned.strip()


import requests

# 构建优化后的AI提示词
prompt = build_optimized_prompt("1.卧床休息，禁性生活，加强营养，目前孕妇彩超提示盆腹腔包块，性质不明，避免大幅度运动，注意右侧腰腹部疼痛情况，必要时需手术治疗；定期复查。2.自计胎动，注意阴道流血、流液，腹痛、腹胀等异常情况，若有不适，随时就诊；3.建议自备头孢克洛口服治疗2-3天，定期复查血常规、CRP情况，益生菌调节胃肠道菌群；4.定期产检（每2周一次），我科门诊随诊。")

# 清理提示词：删除多余的换行和空格
cleaned_prompt = clean_prompt_text(prompt)
response = requests.post(
    "http://***************:12434/api/generate",
    json={
        "model": 'qwen3:8b-q4_K_M',
        "prompt": """
你是专业医疗随访计划专家。仔细分析医嘱内容，提取所有随访相关信息，生成完整的JSON格式随访计划。 医嘱内容：1.卧床休息，禁性生活，加强营养，目前孕妇彩超提示盆腹腔包块，性质不明，避免大幅度运动，注意右侧腰腹部疼痛情况，必要时需手术治疗；定期复查。2.自计胎动，注意阴道流血、流液，腹痛、腹胀等异常情况，若有不适，随时就诊；3.建议自备头孢克洛口服治疗2-3天，定期复查血常规、CRP情况，益生菌调节胃肠道菌群；4.定期产检（每2周一次），我科门诊随诊。 分析重点： 1.寻找时间关键词：如"每"、"月"、"年"、"天"、"周"，“半年”等 2.识别具体时间：如"1个月"、"3-4个月"、"半年"、"2年内"、"每年"等 3.判断周期性任务，如出现“每”等 输出JSON格式示例： [{"cycle":1,"interval":3,"unit":"月","exact_date":"","content":"每3个月复查CT","end_time":,"end_unit":"","end_date":""},{"cycle":0,"interval":1,"unit":"月","exact_date":"","content":"术后1个月复查","end_time":0,"end_unit":"","end_date":""}] 字段规则： - cycle：1=定期重复随访，0=单次随访 - interval：时间间隔数值（如3个月写3，半年写6），且可以是小数，如果是“3-4月”就填3.5 - unit：时间单位（天/周/月/年） - content：具体随访内容和检查项目 - end_time：结束时间，如果没有直接写结束时间，就不用管 - end_unit：结束时间的单位 如果医嘱中没有明确的随访安排，返回：[{"cycle":0,"interval":0,"unit":"","exact_date":"","content":"","end_time":0,"end_unit":"","end_date":""}] 请仔细分析医嘱内容，提取所有随访信息，直接返回JSON数组：        
        """,
        "stream": False,
        "think": True
    },
    # timeout=60  # 60秒超时
)

if response.status_code == 200:
    response_data = response.json()
    ai_output = response_data.get('response', '').strip()
    print(f"AI响应: {ai_output}")

