"""
消息处理器实现 - 简化版
包含语音通知、微信模板消息等具体处理器
"""

from .config import PHONE_LENGTH, ERROR_CODES
from .core import MessagePayload, MessageResult, MessageType


class VoiceTTSHandler:
    """TTS语音通知处理器"""
    
    def validate_payload(self, payload: MessagePayload) -> bool:
        """验证载荷"""
        if payload.message_type != MessageType.VOICE_TTS:
            return False
        
        content = payload.content
        if not content.get('text'):
            return False
        
        # 验证手机号格式
        phone = payload.target_id
        if not phone or len(phone) != PHONE_LENGTH or not phone.isdigit():
            return False
        
        return True
    
    def send(self, payload: MessagePayload) -> MessageResult:
        """发送TTS语音通知"""
        try:
            # 提取参数
            phone = payload.target_id
            text = payload.content['text']
            play_size = payload.content.get('play_size', 1)

            # 直接调用原有的 Celery 任务
            from celery_task.privacy.bind import notice
            result = notice(
                phone,
                text,
                play_size,
                True  # is_tts=True
            )

            # 统一返回格式
            if isinstance(result, dict):
                return MessageResult(
                    success=result.get('success', False),
                    message=result.get('msg', 'TTS语音通知发送完成'),
                    data=result
                )
            else:
                return MessageResult(
                    success=True,
                    message="TTS语音通知发送成功",
                    data={"result": result}
                )

        except Exception as e:
            return MessageResult(
                success=False,
                message=f"TTS语音通知发送失败: {str(e)}",
                error_code=ERROR_CODES['TTS_SEND_ERROR']
            )


class VoiceVOXHandler:
    """VOX录音播放处理器"""
    
    def validate_payload(self, payload: MessagePayload) -> bool:
        """验证载荷"""
        if payload.message_type != MessageType.VOICE_VOX:
            return False
        
        content = payload.content
        if not content.get('file_name'):
            return False
        
        # 验证手机号格式
        phone = payload.target_id
        if not phone or len(phone) != PHONE_LENGTH or not phone.isdigit():
            return False
        
        return True
    
    def send(self, payload: MessagePayload) -> MessageResult:
        """发送VOX录音播放"""
        try:
            # 提取参数
            phone = payload.target_id
            file_name = payload.content['file_name']
            play_size = payload.content.get('play_size', 1)

            # 直接调用原有的 Celery 任务
            from celery_task.privacy.bind import notice
            result = notice(
                phone,
                file_name,
                play_size,
                False  # is_tts=False
            )

            # 统一返回格式
            if isinstance(result, dict):
                return MessageResult(
                    success=result.get('success', False),
                    message=result.get('msg', 'VOX录音播放完成'),
                    data=result
                )
            else:
                return MessageResult(
                    success=True,
                    message="VOX录音播放成功",
                    data={"result": result}
                )

        except Exception as e:
            return MessageResult(
                success=False,
                message=f"VOX录音播放失败: {str(e)}",
                error_code=ERROR_CODES['VOX_SEND_ERROR']
            )


class WeChatTemplateHandler:
    """微信模板消息处理器"""
    
    def validate_payload(self, payload: MessagePayload) -> bool:
        """验证载荷"""
        if payload.message_type != MessageType.WECHAT_TEMPLATE:
            return False
        
        content = payload.content
        if not content.get('template_type') or not content.get('data'):
            return False
        
        # 验证openid格式
        open_id = payload.target_id
        if not open_id or not open_id.startswith('o'):
            return False
        
        return True
    
    def send(self, payload: MessagePayload) -> MessageResult:
        """发送微信模板消息"""
        try:
            # 提取参数
            open_id = payload.target_id
            template_type = payload.content['template_type']
            data = payload.content['data']
            url = payload.content.get('url')
            miniprogram = payload.content.get('miniprogram')
            client_msg_id = payload.content.get('client_msg_id')

            # 直接调用原有的 Celery 任务
            from celery_task.WeChat.interface import send_template
            result = send_template(
                data,
                open_id,
                template_type,
                url,
                miniprogram,
                client_msg_id
            )

            # 统一返回格式
            if isinstance(result, dict):
                # 微信API成功返回包含msgid
                if result.get('msgid'):
                    return MessageResult(
                        success=True,
                        message="微信模板消息发送成功",
                        data=result
                    )
                else:
                    return MessageResult(
                        success=False,
                        message=result.get('errmsg', '微信模板消息发送失败'),
                        data=result,
                        error_code=str(result.get('errcode', 'UNKNOWN'))
                    )
            else:
                return MessageResult(
                    success=True,
                    message="微信模板消息发送成功",
                    data={"result": result}
                )

        except Exception as e:
            return MessageResult(
                success=False,
                message=f"微信模板消息发送失败: {str(e)}",
                error_code=ERROR_CODES['WECHAT_SEND_ERROR']
            )


# 处理器工厂
class HandlerFactory:
    """处理器工厂"""
    
    _handlers = {
        MessageType.VOICE_TTS: VoiceTTSHandler,
        MessageType.VOICE_VOX: VoiceVOXHandler,
        MessageType.WECHAT_TEMPLATE: WeChatTemplateHandler,
    }
    
    @classmethod
    def create_handler(cls, message_type: MessageType):
        """创建处理器"""
        handler_class = cls._handlers.get(message_type)
        if not handler_class:
            raise ValueError(f"No handler available for message type: {message_type}")
        return handler_class()
    
    @classmethod
    def register_handler(cls, message_type: MessageType, handler_class: type):
        """注册新的处理器类"""
        cls._handlers[message_type] = handler_class
