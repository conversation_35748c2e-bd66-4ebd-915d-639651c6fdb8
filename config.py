#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 数据库配置
DATABASE_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'your_password',  # 请修改为实际的MySQL密码
    'database': 'cancer_first',
    'charset': 'utf8mb4',
    'autocommit': True
}

# 爬虫配置
CRAWLER_CONFIG = {
    'base_url': 'https://code.nhsa.gov.cn/jbzd/public/dataWesterSearch.html',
    'timeout': 30,
    'retry_times': 3,
    'delay_between_requests': 0.1,  # 请求间隔（秒）
    'selenium_wait_timeout': 10,  # Selenium等待超时时间
    'node_click_delay': 0.5,  # 节点点击后等待时间
    'page_load_delay': 2,  # 页面加载等待时间
    'max_tree_depth': 6,  # 最大树遍历深度
}

# 请求头配置
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}
