import pandas
from PySide2.QtCore import Qt

from app.common.config import cfg
from app.common.setting import CHECK_TYPE_COMPARISON
from function.db.execute import db
from function.program.transform_subject import get_department_code, get_half_dept_code


def get_quality_control_entire_ratio(history_db, month):
    """ 质控报表院级数据 """
    return db('RATIO', 'yzzxyy.history_entire',
              {'HISTORY_DB': history_db, 'MONTH': month[2:]}, 'mysql')


def get_quality_control_history_data(column, name, analysis_index, positive_index, time_type, month_report, history_db):
    """ 质控报表历史数据查询 """
    add_condition = ''
    if column != '总览':
        add_condition = f"AND {column} = '{name}'"
    sql = f"""SELECT t.MONTH,
                       SUM(IF(t.{analysis_index} = '{positive_index}', 1, 0))  AS positive_num,
                       SUM(IF(t.{analysis_index} != '{positive_index}', 1, 0)) AS negative_num
                FROM (SELECT DATE_FORMAT({time_type}_隐藏, '%Y-%m') AS MONTH,
                             {analysis_index}
                      FROM yzzxyy.{history_db}
                      WHERE {time_type}_隐藏 BETWEEN '{month_report[0][0].toString(Qt.ISODate)}' and 
                                                    '{month_report[-1][1].toString(Qt.ISODate)}'
                      {add_condition}) AS t
                GROUP BY t.MONTH"""
    return db(None, None, sql, 'mysql')


def get_quality_control_history_data_v(column, name, analysis_index, time_type, month_report, history_db,
                                       table_data_filter, combo_filter_list_1, combo_filter_list_2, algorithm):
    """ 质控报表历史数据纯数值，没有positive的查询 """

    def set_add_condition(types, detail, condition, subject):
        if types == '学科名称':
            subject = True
            code_lists = get_department_code(detail)
            condition += f"AND 科室代码_隐藏 IN {tuple(code_lists)}"
        elif types == '科室名称':
            codes = table_data_filter[table_data_filter['科室名称'] == detail]['科室代码_隐藏'].values[0]
            condition += f"AND 科室代码_隐藏 = '{codes}'"
        elif types != '总览':
            condition += f"AND {types} = '{detail}'"
        return condition, subject

    has_subject_name = False  # 是否查询了学科名称
    add_condition = ''  # sql追加限定条件
    add_condition, has_subject_name = set_add_condition(column, name, add_condition, has_subject_name)
    # 强制加上 上一个窗口过滤框的限定条件
    if combo_filter_list_1[1] not in {'全部', '', '合并'}:
        add_condition, has_subject_name = set_add_condition(combo_filter_list_1[0], combo_filter_list_1[1],
                                                            add_condition, has_subject_name)
    if combo_filter_list_2[1] not in {'全部', '', '合并'}:
        add_condition, has_subject_name = set_add_condition(combo_filter_list_2[0], combo_filter_list_2[1],
                                                            add_condition, has_subject_name)

    sql = ''
    if not algorithm:  # 计数
        sql = f"""SELECT t.MONTH,
                         SUM(t.{analysis_index}) AS num
                    FROM (SELECT DATE_FORMAT({time_type}_隐藏, '%Y-%m') AS MONTH,
                                 {analysis_index}
                          FROM yzzxyy.{history_db}
                          WHERE {time_type}_隐藏 BETWEEN DATE '{month_report[0][0].toString(Qt.ISODate)}' AND 
                                                        DATE '{month_report[-1][1].toString(Qt.ISODate)}'
                          {add_condition}) AS t
                    GROUP BY t.MONTH"""
    else:  # 均值
        if has_subject_name:  # 说明设置了学科名称
            half_dept_code = get_half_dept_code()
            add_temp_half = f"科室代码_隐藏 IN {tuple(half_dept_code)}"
            sql = f"""SELECT DATE_FORMAT({time_type}_隐藏, '%Y-%m')                AS MONTH,
                               ROUND(SUM(CASE WHEN {add_temp_half} THEN {algorithm[0]} * 0.5 ELSE {algorithm[0]} END) /
                               SUM(CASE WHEN {add_temp_half} THEN {algorithm[1]} * 0.5 ELSE {algorithm[1]} END), 2) AS num
                        FROM yzzxyy.{history_db}
                        WHERE {time_type}_隐藏 BETWEEN DATE '{month_report[0][0].toString(Qt.ISODate)}' AND 
                                                      DATE '{month_report[-1][1].toString(Qt.ISODate)}'
                              {add_condition}
                        GROUP BY MONTH"""
        else:
            sql = f"""SELECT DATE_FORMAT({time_type}_隐藏, '%Y-%m')                AS MONTH,
                               ROUND(SUM({algorithm[0]}) / SUM({algorithm[1]}), 2) AS num
                        FROM yzzxyy.{history_db}
                        WHERE {time_type}_隐藏 BETWEEN DATE '{month_report[0][0].toString(Qt.ISODate)}' AND 
                                                      DATE '{month_report[-1][1].toString(Qt.ISODate)}'
                              {add_condition}
                        GROUP BY MONTH"""
    df = db(None, None, sql, 'mysql')
    if df.empty:
        df = pandas.DataFrame(columns=['MONTH', 'num'])
    return df


def get_auto_add_sql(user, auto_add_object_name):
    """ 查询报表下拉列表 """
    sql = f"""SELECT *
                 FROM yzzxyy.info_sql
                 WHERE auto_add_objectName = '{auto_add_object_name}'
                   AND (SELECT department FROM yzzxyy.info_user where id = '{user}')
                     LIKE CONCAT('%', department, '%')
                 ORDER BY ids"""
    return db(None, None, sql, 'mysql')


def quality_control_concat(sql: str, replacements: list, enable_cache: bool = False, cache_db: str = None,
                           time_type: str = None, database_name: str = 'oracle'):
    """ 报表主数据查询 """
    # raise ValueError('测试异常')
    exists_flag = False
    if enable_cache and cache_db:
        # 检查起始和结束月份的文本是不是在对应的总览表中找得到
        # positive为null时，初始查询不调用缓存，因为缓存的都是月度数据，查询的都是具体日期数据
        sql_check = f"""SELECT IF(COUNT(DISTINCT MONTH) = 2, TRUE, FALSE) AS exists_flag
                    FROM yzzxyy.history_entire
                    WHERE HISTORY_DB = '{cache_db}'
                      AND MONTH IN ('{replacements[0][2:7]}', '{replacements[1][2:7]}')"""
        if replacements[0][:7] == replacements[1][:7]:
            sql_check = sql_check.replace("COUNT(DISTINCT MONTH) = 2", "COUNT(DISTINCT MONTH) = 1")
        exists_flag = db(None, None, sql_check, 'mysql').iloc[0, 0] == 1

    if exists_flag:
        # 存在缓存
        sql = f"SELECT * FROM yzzxyy.{cache_db} WHERE {time_type}_隐藏 BETWEEN '{replacements[0]}' AND '{replacements[1]}'"
        df = db(None, None, sql, 'mysql')
    else:
        for ids, data in {f'[[{i}]]': value for i, value in enumerate(replacements, start=0)}.items():  # 构建最终sql语句
            sql = sql.replace(ids, data)
        df = db(None, None, sql, database_name)
    return df


def get_follow_task(username):
    """ 查询回访任务 """
    sql = f"""SELECT a.INPATIENT_NO INPATIENT_NO,
                   a.ID         ARTIFICIAL_ID,
                   a.PLAN_ID    PLAN_ID,
                   a.CONTENT_ID CONTENT_ID,
                   a.STATE      TASK_STATE,
                   a.HOUSE_DOC_CODE	主管医生工号,
                   a.DEDICATED_CODE	回访专人工号,
                   p.COMPLETE   COMPLETE,
                   p.STATE      状态,
                   p.TEL        TEL,
                   c.INFO       INFO,
                   c.COMBO      COMBO
            FROM yzzxyy.follow_task_artificial a
             JOIN yzzxyy.follow_plan p ON p.ID = a.PLAN_ID
             JOIN yzzxyy.follow_content c ON c.ID = a.CONTENT_ID
            where a.STATE < 2
              AND a.DEDICATED_CODE = '{username}' """
    task = db(None, None, sql, 'mysql')
    if task.empty:
        return task
    # 合并相同INPATIENT_NO的task
    status_mapping = {2: '回访任务', 3: '超时任务', 4: '二次超时', 5: '严重超时'}
    grouped_task = task.groupby('INPATIENT_NO').agg({
        'ARTIFICIAL_ID': lambda x: x.tolist(),  # 将ARTIFICIAL_ID列的值转换为列表
        'PLAN_ID': lambda x: x.tolist(),
        'CONTENT_ID': lambda x: x.tolist(),
        'TASK_STATE': lambda x: x.tolist(),
        'COMPLETE': lambda x: x.tolist(),
        '状态': lambda x: status_mapping.get(max(x), '其他'),
        '主管医生工号': lambda x: x.iloc[0],
        '回访专人工号': lambda x: x.iloc[0],
        'TEL': lambda x: x.iloc[0],  # 只取第一个值
        'INFO': lambda x: x.tolist(),
        'COMBO': lambda x: x.tolist()
    }).reset_index()

    sql = f'''select f.INPATIENT_NO                               INPATIENT_NO,
                       f.CARD_NO                                   CARD_NO,
                       f.NAME                                       姓名,
                       DECODE(f.SEX_CODE, 'M', '男', '女')          性别,
                       f.CHARGE_DOC_NAME                            主管医生,
                       f.BALANCE_COST                               总花费,
                       h.CERTIFICATE_NO                             身份证,
                       h.PRESENT_PROVINCE                           住址省,
                       h.PRESENT_CITY                               住址市,
                       h.PRESENT_COUNTY                             住址县,
                       h.PRESENT_OTHER                              住址其他,
                       h.PRESENT_TEL                                现住址电话,
                       h.CONTACT_NAME                               联系人,
                       h.RELATIONSHIP_CODE                          联系人关系,
                       h.CONTACT_TEL                                联系人电话,
                       h.IN_DATE                                    入院时间,
                       HNYZ_ZXYY.FUN_GET_DEPT_NAME(h.IN_DEPT_CODE)  入院科室,
                       h.OUT_DATE                                   出院时间,
                       HNYZ_ZXYY.FUN_GET_DEPT_NAME(h.OUT_DEPT_CODE) 出院科室,
                       h.OUT_DEPT_CODE                              出院科室代码,
                       h.IN_DAYS                                    住院天数,
                       DECODE(h.OUT_TYPE, 1, '医嘱离院', 
                                          2, '医嘱转院', 
                                          3, '医嘱下转', 
                                          4, '非医嘱离院', 
                                          5, '死亡',
                                             '其他')                 离院方式,
                       h.RECEIVING_HOS_NAME                         接收机构,
                       h.RECEIVING_COMMUNITY                        接收社区,
                       h.AGE                                        入院年龄
                from HNYZ_ZXYY.FIN_IPR_INMAININFO f
                         LEFT JOIN NMRWS.NMRWS_MR_HOMEPAGE h ON h.INPATIENT_NO = f.INPATIENT_NO
                where f.INPATIENT_NO in ('{"', '".join(grouped_task['INPATIENT_NO'])}')
    '''
    info = db(None, None, sql, 'oracle')
    merged_df = pandas.merge(grouped_task, info, on='INPATIENT_NO', how='left')
    return merged_df


def get_group_info(username):
    """ 查询医生的自定义分组信息 """
    result = db(['ID', 'GROUP_NAME'], 'yzzxyy.follow_group_config',
                {'USER': username}, 'mysql')
    return result if not result.empty else pandas.DataFrame(columns=['ID', 'GROUP_NAME'])


def get_patient_group(id_card):
    """ 查询回访患者分组信息 """
    result = db(['GROUP_ID ID', "1 TAG"], 'yzzxyy.follow_group_patient',
                {'ID_CARD': id_card}, 'mysql')
    return result if not result.empty else pandas.DataFrame(columns=['ID', 'TAG'])


def update_patient_group(id_card: str, group_id: str, is_insert: bool):
    """ 更新回访患者分组信息 """
    if is_insert:
        sql = f"""INSERT INTO yzzxyy.follow_group_patient (ID_CARD, GROUP_ID)
                    SELECT '{id_card}', {group_id}
                            FROM dual
                            WHERE NOT EXISTS (SELECT 1
                                              FROM yzzxyy.follow_group_patient
                                              WHERE ID_CARD = '{id_card}'
                                                AND GROUP_ID = {group_id})"""
    else:
        sql = f"""DELETE FROM yzzxyy.follow_group_patient 
                    WHERE ID_CARD = '{id_card}' AND GROUP_ID = {group_id}"""
    db(None, None, sql, 'mysql')


def get_doc_info(username: str) -> dict:
    """ 查询医生手机号、姓名、身份证号、可登录科室的ID等信息 """
    tel = db('TEL', 'yzzxyy.info_phone', {'CODE': username}, 'mysql')
    df = db(['EMPL_NAME', 'IDENNO'], 'DAWN.DAWN_ORG_EMPL',
            {'EMPL_ID': username}, 'oracle')

    sql = f"""select dept_id
                    from DAWN.dawn_org_empl
                    where empl_id = '{username}'
                    union
                    select dept_id -- 科室编码,
                    from DAWN.dawn_prm_deptstruct
                    where DEPTSTRUCT_ID in (select deptstruct_id -- 科室结构编码
                                            from DAWN.dawn_prm_dept_user u
                                            where u.user_id = (select USER_ID -- 用户ID
                                                               from DAWN.dawn_prm_user
                                                               where EMPLOYEE_ID = '{username}'))"""
    return {"TEL": tel, "NAME": df['EMPL_NAME'].values[0], "ID": df['IDENNO'].values[0],
            "PERMISSION_DEPT_ID": db(None, None, sql, 'oracle')['DEPT_ID'].tolist()}


def get_homepage_diagnose(inpatient_no: str) -> pandas.DataFrame:  # 获取首页诊断
    """ 获取首页诊断信息 """
    sql = f"""select DECODE(d.DIAGNOSE_TYPE, 1, '门诊诊断', 2, '入院诊断', 3, '出院诊断', 4, '病历诊断', d.DIAGNOSE_TYPE) 诊断类型,
                       d.IS_MAIN                                                                                    主诊断,
                       d.BEFORECODE || d.ICD_NAME || d.AFTERCODE                                                   诊断名称,
                       d.SORT_NO                                                                                     顺序号
                from NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE d
                         join NMRWS.NMRWS_MR_HOMEPAGE h on h.HOMEPAGE_ID = d.HOMEPAGE_ID
                where h.INPATIENT_NO = '{inpatient_no}'
                  and DIAGNOSE_CODE_TYPE = '1'
                order by d.DIAGNOSE_TYPE, d.SORT_NO"""
    return db(None, None, sql, 'oracle')


def get_follow_history(dept_code: list, id_card=None, patient_name=None, date_s=None, date_e=None, types=None):
    """ 获取回访历史记录 """
    conditions_h = []
    conditions_a = []
    if id_card:
        conditions_h.append(f" h.ID_CARD = '{id_card}' ")
        conditions_a.append(f" p.ID_CARD = '{id_card}' ")
    if patient_name:
        conditions_h.append(f" h.PATIENT_NAME = '{patient_name}' ")
        conditions_a.append(f" p.NAME = '{patient_name}' ")
    if date_s and date_e:
        conditions_h.append(f""" h.SMS_TIME BETWEEN STR_TO_DATE('{date_s} 00:00:00', '%Y-%m-%d %H:%i:%s') AND 
                                                    STR_TO_DATE('{date_e} 23:59:59', '%Y-%m-%d %H:%i:%s') """)
        conditions_a.append(f""" a.COMPLETE_TIME BETWEEN STR_TO_DATE('{date_s} 00:00:00', '%Y-%m-%d %H:%i:%s') AND 
                                                         STR_TO_DATE('{date_e} 23:59:59', '%Y-%m-%d %H:%i:%s') """)
    if types == '方案提醒':
        conditions_h.append(" 1 = 1 ")
        conditions_a.append(" 1 != 1 ")
    elif types == '专员升级':
        conditions_h.append(" 1 != 1 ")
        conditions_a.append(" a.STATE = 2 ")
    elif types == '人工取消':
        conditions_h.append(" 1 != 1 ")
        conditions_a.append(" a.STATE = 3 ")
    elif types == '完成回访':
        conditions_h.append(" 1 != 1 ")
        conditions_a.append(" a.STATE = 4 ")

    condition_h_str = ' AND '.join(conditions_h)
    condition_a_str = ' AND '.join(conditions_a)
    if condition_h_str:
        condition_h_str = "AND " + condition_h_str
    if condition_a_str:
        condition_a_str = "AND " + condition_a_str

    sql = f"""select DISTINCT *
        from (SELECT h.PATIENT_NAME                           患者姓名,
                     h.ID_CARD                                身份证号,
                     h.SMS_TIME                               回访时间,
                     '系统'                                   回访人,
                     '方案提醒'                               回访类型,
                     CONCAT('短信：', h.CONTENT, ' - ', h.TEL) 反馈
              FROM yzzxyy.follow_psms_history h
              WHERE h.DEPT_CODE IN ('{"', '".join(dept_code)}') {condition_h_str}
              UNION
              SELECT p.NAME           患者姓名,
                     p.ID_CARD        身份证号,
                     a.COMPLETE_TIME  回访时间,
                     a.DEDICATED_NAME 回访人,
                     CASE a.STATE
                         WHEN 2 THEN '专员升级'
                         WHEN 3 THEN '人工取消'
                         WHEN 4 THEN '完成回访'
                         END          回访类型,
                     CONCAT('人工：', IFNULL(p.COMPLETE, ''), ' - ', IFNULL(a.COMBO1, ''), ' - ', IFNULL(a.COMBO2, ''), ' - ', 
                            IFNULL(a.ADDS, ''), ' - ', IFNULL(a.REMARKS, ''), '[', IFNULL(a.F_TEL, ''), ']') AS   反馈
              FROM yzzxyy.follow_task_artificial a
                       JOIN yzzxyy.follow_plan p ON p.ID = a.PLAN_ID
              WHERE a.DEPT_CODE IN ('{"', '".join(dept_code)}') {condition_a_str}
                AND a.STATE > 1
            UNION
            SELECT p.NAME                                患者姓名,
                p.ID_CARD                             身份证号,
                fr.START_TIME                         回访时间,
                a.DEDICATED_NAME                      回访人,
                '语音通话'                            回访类型,
                CONCAT(CASE fr.CALL_RESULT
                           WHEN 1 THEN '成功'
                           WHEN 2 THEN '失败'
                           ELSE '未知'
                           END,
                       ' - ', fr.DURATION_TIME, '秒') 反馈
         FROM yzzxyy.follow_task_artificial a
             JOIN yzzxyy.follow_plan p ON p.ID = a.PLAN_ID
             JOIN yzzxyy.follow_call fc ON FIND_IN_SET(
                                                   fc.id,
                                                   REPLACE(a.CALL_ID, '|', ',')) > 0
             JOIN yzzxyy.follow_record fr ON fc.CALL_ID = fr.CALL_ID
         WHERE a.DEPT_CODE IN ('{"', '".join(dept_code)}') {condition_a_str}
           AND a.CALL_ID IS NOT NULL) x
        ORDER BY x.回访时间 DESC
        LIMIT {cfg.FollowHistoryRow.value}"""

    return db(None, None, sql, 'mysql')


def get_element(inpatient_no: str) -> pandas.DataFrame:  # 获取文书元素信息
    sql = f"""select ELEMENT_NAME, VALUE
                from HIT_MDC.MDC_RCD_IN_RECORD_ITEM
                where ELEMENT_ID in ('56', '203233', '2048395', '2048396', '369', '2054154')
                  and INPATIENT_NO = '{inpatient_no}'
                order by CREATE_TIME desc"""
    return db(None, None, sql, 'oracle')


def get_follow_search(data: dict, code: str):
    """ 获取病程追踪记录 """
    condition = []
    if data.get('Name', None):
        condition.append(f" h.NAME = '{data['Name']}' ")
    if data.get('Inpatient', None):
        condition.append(f" h.INPATIENT_NO = '{data['Inpatient']}' ")
    if data.get('IDCard', None):
        condition.append(f" h.CERTIFICATE_NO = '{data['IDCard']}' ")
    if data.get('InTime', None):
        condition.append(f""" h.IN_DATE BETWEEN TO_DATE('{data['InTime'][0]} 00:00:00', 'yyyy-mm-dd hh24:mi:ss') 
                                             AND TO_DATE('{data['InTime'][1]} 23:59:59', 'yyyy-mm-dd hh24:mi:ss') """)
    if data.get('OutTime', None):
        condition.append(f""" h.OUT_DATE BETWEEN TO_DATE('{data['OutTime'][0]} 00:00:00', 'yyyy-mm-dd hh24:mi:ss') 
                                              AND TO_DATE('{data['OutTime'][1]} 23:59:59', 'yyyy-mm-dd hh24:mi:ss') """)
    if data.get('InDiag', None):
        condition.append(f""" EXISTS (SELECT 1
                                          FROM NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE d
                                          WHERE d.HOMEPAGE_ID = h.HOMEPAGE_ID
                                            AND d.DIAGNOSE_CODE_TYPE = 1
                                            AND d.DIAGNOSE_TYPE = 2
                                            AND (d.BEFORECODE LIKE '%{data['InDiag']}%' 
                                                OR d.ICD_NAME LIKE '%{data['InDiag']}%' 
                                                OR d.AFTERCODE LIKE '%{data['InDiag']}%')) """)
    if data.get('OutDiag', None):
        condition.append(f""" EXISTS (SELECT 1
                                          FROM NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE d
                                          WHERE d.HOMEPAGE_ID = HOMEPAGE_ID
                                            AND d.DIAGNOSE_CODE_TYPE = 1
                                            AND d.DIAGNOSE_TYPE = 3
                                            AND (d.BEFORECODE LIKE '%{data['OutDiag']}%' 
                                                OR d.ICD_NAME LIKE '%{data['OutDiag']}%' 
                                                OR d.AFTERCODE LIKE '%{data['OutDiag']}%')) """)
    for items in data.get('Inspect', []):
        # items为每个页面类的条件列表，item为单个条件详情
        sql_list = []
        for item in items:
            sql = ''
            if item['item0_content'] == '检验':
                sql = " EXISTS (SELECT 1 FROM HNYZ_ZXYY.LIS_RESULT r WHERE r.PATIENTID = h.MR_NO "
                if item['item1_content'] == '医嘱名' and item['item2_content'] == '模糊匹配':
                    sql += f""" AND r.HISITEMCODE IN (SELECT TERM_ID FROM HIS_FSHV1.MET_ORDT_UNDRUGTERM
                                                            WHERE TERM_CLASS_NAME = '检验' 
                                                            AND TERM_NAME LIKE '%{item['item3_content']}%') """
                elif item['item1_content'] == '医嘱名' and item['item2_content'] == '精准匹配':
                    sql += f""" AND r.HISITEMCODE = '{item['item3_code']}' """
                # [大于、小于、匹配][全部、升高、降低][全部,描述, 结论]、ECombo、Edit
                elif item['item1_content'] == '项目名' and item['item3_content'] == '全部':
                    sql += f""" AND r.ITEMCODE = '{item['item2_code']}' """
                elif item['item1_content'] == '项目名' and item['item3_content'] == '升高':
                    sql += f""" AND r.ITEMCODE = '{item['item2_code']}' AND r.FLAG = '高' """
                elif item['item1_content'] == '项目名' and item['item3_content'] == '降低':
                    sql += f""" AND r.ITEMCODE = '{item['item2_code']}' AND r.FLAG = '低' """
                elif item['item1_content'] == '项目名' and item['item3_content'] == '匹配':
                    sql += f""" AND r.ITEMCODE = '{item['item2_code']}' AND r.RESULT LIKE '%{item['item4_content']}%' """
                elif item['item1_content'] == '项目名' and item['item3_content'] == '大于':
                    sql += f""" AND r.ITEMCODE = '{item['item2_code']}' 
                                    AND (CASE WHEN REGEXP_LIKE(r.RESULT, '^[0-9.]+$') THEN TO_NUMBER(r.RESULT) ELSE -9999 END)
                                        > {float(item['item4_content'])}) """
                elif item['item1_content'] == '项目名' and item['item3_content'] == '小于':
                    sql += f""" AND r.ITEMCODE = '{item['item2_code']}' 
                                    AND (CASE WHEN REGEXP_LIKE(r.RESULT, '^[0-9.]+$') THEN TO_NUMBER(r.RESULT) ELSE 9999 END)
                                        < {float(item['item4_content'])}) """
                sql += ' ) '

            elif item['item0_content'] == '检查':
                sql = f""" EXISTS (SELECT 1
                                      FROM HISINTERFACE.TH_CHECK_RECORD r
                                      WHERE r.TREATMENT_CODE = h.INPATIENT_NO
                                        AND r.CHECK_TYPE in {CHECK_TYPE_COMPARISON[item['item1_content']]} """
                if item['item2_content']:
                    sql += f"AND r.CHECK_NAME like '%{item['item2_content']}%' "
                if item['item3_content'] == '描述':
                    sql += f"AND r.CHECK_VIEW like '%{item['item4_content']}%' "
                elif item['item3_content'] == '结论':
                    sql += f"AND r.CHECK_RESULT like '%{item['item4_content']}%' "
                sql += ' ) '
            elif item['item0_content'] == '心电图':
                sql = f""" EXISTS (SELECT 1
                                      FROM BYXDJK.TH_CHECK_RECORD r
                                      WHERE r.TREATMENT_CODE = h.INPATIENT_NO
                                        AND r.CHECK_TYPE = '{item['item1_content']}' """
                if item['item2_content']:
                    sql += f"AND r.CHECK_NAME like '%{item['item2_content']}%' "
                if item['item3_content'] == '描述':
                    sql += f"AND r.CHECK_VIEW like '%{item['item4_content']}%' "
                elif item['item3_content'] == '结论':
                    sql += f"AND r.CHECK_RESULT like '%{item['item4_content']}%' "
                sql += ' ) '
            if sql:
                sql_list.append(sql)
        if sql_list:
            condition.append(f" ({' OR '.join(sql_list)}) ")

    condition.append(f""" (h.DEPT_DIRECTOR_CODE = '{code}' OR h.PROFESSIONAL_DOC_CODE = '{code}'
                                    OR h.ATTENDING_DOC_CODE = '{code}' OR h.INHOS_DOC_CODE_CODE = '{code}') 
                                    FETCH FIRST {cfg.FollowSearchRow.value} ROWS ONLY """)
    result = db(['INPATIENT_NO 住院号', 'NAME 姓名', "TO_CHAR(OUT_DATE, 'YYYY-MM-DD') 出院日期",
                 "HNYZ_ZXYY.FUN_GET_EMPL_NAME(INHOS_DOC_CODE_CODE) 住院医生", "CERTIFICATE_NO 身份证号_隐藏"],
                'NMRWS.NMRWS_MR_HOMEPAGE h', 'AND'.join(condition), 'oracle')

    # 获取分组
    sql = f"""SELECT c.GROUP_NAME 分组, p.ID_CARD 身份证号_隐藏
                    FROM yzzxyy.follow_group_config c
                             JOIN yzzxyy.follow_group_patient p ON p.GROUP_ID = c.ID
                    WHERE c.USER = '{code}'
                      AND p.ID_CARD in ('{"', '".join(result['身份证号_隐藏'].unique())}') """
    group = db(None, None, sql, 'mysql')
    if group.empty:
        group = pandas.DataFrame(columns=['分组', '身份证号_隐藏'])

    # 获取预警
    sql = f"""SELECT ID_CARD 身份证号_隐藏, CAST(count(1) AS CHAR) 预警
                        FROM yzzxyy.follow_warning
                        WHERE USER = '25078'
                          AND ID_CARD in ('{"', '".join(result['身份证号_隐藏'].unique())}')
                        group by ID_CARD """
    warning = db(None, None, sql, 'mysql')
    if warning.empty:
        warning = pandas.DataFrame(columns=['身份证号_隐藏', '预警'])

    # 信息合并
    # group 按照 '身份证号_隐藏' 列进行分组，并对每个分组内的数据进行合并为一个字符串，用 '|' 分隔。
    grouped = group.groupby('身份证号_隐藏').agg(lambda x: '|'.join(x))
    # 将上一步得到的分组数据集 grouped 与另一个数据集 warning 按照 '身份证号_隐藏' 列进行外连接合并。
    merged = grouped.merge(warning, on='身份证号_隐藏', how='outer')
    # 对合并后的数据集 merged 的 '分组' 列进行处理，如果 '预警' 列不为空，则在 '分组' 前加上 "【警{预警}】"，否则保持原样。
    merged['分组'] = merged.apply(
        lambda row: f"【警{row['预警']}】{row['分组']}" if not pandas.isnull(row['预警']) else row['分组'], axis=1)
    merged = merged.drop('预警', axis=1)

    result = pandas.merge(result, merged, on='身份证号_隐藏', how='left').fillna('')
    if data.get('Group', None):
        result = result[result['分组'].str.contains(data['Group'])]
    return result


def get_group_by_idcard(user: str, id_cards: pandas.Series):
    """ 依据身份证号获取分组信息 """
    sql = f"""SELECT c.GROUP_NAME 分组, p.ID_CARD 身份证号_隐藏
                        FROM yzzxyy.follow_group_config c
                                 JOIN yzzxyy.follow_group_patient p ON p.GROUP_ID = c.ID
                        WHERE c.USER = '{user}'
                          AND p.ID_CARD in ('{"', '".join(id_cards.unique())}') """
    return db(None, None, sql, 'mysql')


def get_conditions_by_idcard(user: str, id_cards: list):
    """ 依据身份证号获取预警条件 """
    return db(['CONDITIONS', 'ID_CARD'], 'yzzxyy.follow_warning',
              f"""ID_CARD in ('{"', '".join(id_cards)}') and USER = '{user}' """,
              'mysql')


def set_condition(callback, user, id_card):
    """ 设置预警条件 """
    if callback['condition']:
        if callback.get('id', None) and callback['id'].isdigit():
            sql = f"""UPDATE yzzxyy.follow_warning
                        SET CONDITIONS  = '{str(callback['condition']).replace("'", '"')}'
                        WHERE ID = {str(callback['id'])}"""
        elif callback.get('id', None):
            sql = f"""UPDATE yzzxyy.follow_warning
                        SET CONDITIONS  = '{str(callback['condition']).replace("'", '"')}'
                        WHERE CONDITIONS = '{callback['id']}'
                            AND USER = '{user}'
                            AND ID_CARD = '{id_card}'"""
        else:
            sql = f"""INSERT INTO yzzxyy.follow_warning (USER, ID_CARD, CONDITIONS)
                        VALUES ('{user}', '{id_card}',
                                '{str(callback['condition']).replace("'", '"')}')"""
        db(None, None, sql, 'mysql')


def delete_warning(user, id_card, warning_time):
    """ 删除预警 """
    sql = f"""DELETE
                FROM yzzxyy.follow_warning
                WHERE USER = '{user}' 
                    AND ID_CARD = '{id_card}' 
                    AND TIME = '{warning_time.strftime('%Y-%m-%d %H:%M:%S')}' """
    db(None, None, sql, 'mysql')


def get_name(username: str):
    return db('name', 'yzzxyy.info_user', {'id': username}, 'mysql')


def get_idcard(username: str):
    return db('id_card', 'yzzxyy.info_user', {'id': username}, 'mysql')


def binding_wx(username):
    """ 用户名关联的微信OpenID """
    return db('wx_id', 'yzzxyy.info_user', {'id': username}, 'mysql')
