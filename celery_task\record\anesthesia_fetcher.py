import requests
import json
import re
from typing import Optional, Dict, Union, List
from urllib.parse import urljoin, quote, urlparse, parse_qs
from bs4 import BeautifulSoup


def fetch_anesthesia_data(
    url: str,
    cookies: Optional[Dict[str, str]] = None,
    timeout: int = 20,
    **_: Dict,
) -> Dict[str, Union[str, List[Dict]]]:
    """
    抓取手麻系统相关页面，通过模拟AJAX请求获取PDF文档列表。
    会进一步解析每个文档的查看页面，以获取最终的PDF文件直接链接。
    """
    # 从URL中解析PatientID
    try:
        # 解析是为了把url中的参数拿出来
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        # 因为是返回一个参数列表
        patient_id_list = query_params.get('PatientID')
        
        if not patient_id_list or not patient_id_list[0]:
            return {"status": "error", "error": "无法从URL中提取PatientID。"}
        patient_id = patient_id_list[0]

    except Exception:
        return {"status": "error", "error": "解析URL以获取PatientID时出错。"}


    headers = {
        "User-Agent": (
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
            "AppleWebKit/537.36 (KHTML, like Gecko) "
            "Chrome/********* Safari/537.36"
        ),
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "X-Requested-With": "XMLHttpRequest",
        "Referer": url,
    }

    # 从页面JS逻辑中，我们知道API端点和所需的payload
    ajax_url = urljoin(url, "/EmrView/Emr/GetEmrList")
    
    payload = {
        "MrClass": "麻醉,重症",  # 从页面HTML中观察到的默认值
        "PatientID": patient_id,
        "VisitID": "",
        "StrStartDate": "",
        "StrEndDate": "",
    }

    try:
        resp = requests.post(
            ajax_url,
            headers=headers,
            data=payload,
            cookies=cookies,
            timeout=timeout,
            verify=False, # 假定内部系统可能使用自签名证书
        )
        resp.raise_for_status()
        
        response_data = resp.json()

        if not response_data.get("success"):
            return {
                "status": "error",
                "error": f"API返回失败: {response_data.get('Msg', '未知错误')}"
            }

        # 页面JS使用了 eval()，但requests库可能已经将其解析为Python对象。
        # 我们需要同时处理字符串和列表两种情况。
        res_value = response_data.get("resValue")
        if not res_value:
            return {"status": "success", "data": []} # 成功但没有文件
        
        document_list = []
        if isinstance(res_value, list):
            document_list = res_value
        elif isinstance(res_value, str):
            try:
                document_list = json.loads(res_value)
            except json.JSONDecodeError:
                return {"status": "error", "error": "解析API返回的resValue字符串失败，不是有效的JSON。"}
        else:
            # 如果resValue是其他类型，我们也无法处理
            return {"status": "error", "error": f"API返回的resValue格式无法识别: {type(res_value)}"}

        extracted_data = []
        for doc in document_list:
            file_path = doc.get("PATH")
            file_name = doc.get("FileNAME")
            if not file_path or not file_name:
                continue
            
            # 构建PDF查看器页面的URL
            viewer_url = urljoin(url, f"/EmrView/View?FilePath={quote(file_path)}")
            final_pdf_url = viewer_url  # 默认为查看器URL, 以防解析失败

            try:
                # 访问查看器页面以获取真实PDF链接
                viewer_resp = requests.get(
                    viewer_url,
                    cookies=cookies,
                    timeout=timeout,
                    verify=False,
                    headers=headers,
                )
                viewer_resp.raise_for_status()

                # 解析HTML，并在脚本中寻找真实的PDF路径
                soup = BeautifulSoup(viewer_resp.text, 'html.parser')
                scripts = soup.find_all('script')
                
                real_file_path = None
                # 典型的目标路径格式: /EmrView/View/PdfJSView?FilePath=2024...pdf
                pattern = re.compile(r"FilePath=([^&'\"?]+\.pdf)")

                for script in scripts:
                    if script.string:
                        match = pattern.search(script.string)
                        if match:
                            real_file_path = match.group(1)
                            break
                
                if real_file_path:
                    # 构建最终的PDF链接
                    final_pdf_url = urljoin(url, f"/EmrView/EmrTemp/{real_file_path}")

            except (requests.RequestException, Exception):
                # 如果获取或解析查看器页面失败，我们将静默处理，并保留原始的查看器URL
                pass
            
            extracted_data.append({
                "name": file_name,
                "url": final_pdf_url,
                "raw_path": file_path
            })
            
        return {"status": "success", "data": extracted_data}

    except requests.RequestException as e:
        return {
            "status": "error",
            "error": f"请求API失败：{str(e)}"
        }
    except Exception as e:
        return {
            "status": "error",
            "error": f"处理过程中发生未知错误：{str(e)}"
        } 