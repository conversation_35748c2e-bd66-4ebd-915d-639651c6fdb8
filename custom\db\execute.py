from typing import Union

import pandas as pd
from pandas import DataFrame

from custom.db.mysql import UsingMysql, engine
from custom.db.oracle import UsingOracle
from custom.db.sqlite import UsingSQLite


def db(target_names: Union[list, str, None],
       table_name: Union[str, None],
       condition: Union[dict, str, None],
       db_type: str,
       force_query: bool = False) -> Union[pd.DataFrame, str]:
    """
    target_names为str时输出结果首个str，否则输出全部dict；
    target_names和table_name为None时，condition接收完整SQL

    :param target_names: 查询目标 ['regular'] 或 'regular'【异步查询写了callback_data时不支持str传入】
    :param table_name: 表名 'info_sql'
    :param condition: 限定条件字典 {"id": "drug"} 或 原始 ‘sql’
    :param db_type: 数据库类型。'mysql', 'oracle'
    :param force_query: 强制定义为查询
    :return: dataframe{'auto_add_contentList': ['测试1', '测试2'], 'id': ['222', '333']} 或 '测试1'
    """

    def __query(dbs):
        try:
            if sql.strip().upper().startswith('SELECT') or force_query:
                header, content = dbs.fetch_all(sql)
                df = pd.DataFrame(content, columns=header)
                if isinstance(target_names, str):
                    return df[target_names][0] if not df.empty else ''
                return df
            else:
                # 执行非查询语句
                affected_rows = dbs.execute(sql)
                return f"{affected_rows}"
        except Exception as e:
            print(sql)
            raise ValueError(e)

    target_names_str = ", ".join(target_names) if isinstance(target_names, list) else target_names  # 构建查询目标
    if table_name and condition:  # table_name和condition均不为None
        query_string = " AND ".join([f"{key} = '{value}'" for key, value in condition.items()]) \
            if isinstance(condition, dict) else condition  # 构建限定条件
        sql = "SELECT {} FROM {} WHERE {}".format(target_names_str, table_name, query_string)  # 拼装完整SQL
    elif table_name:  # table_name不为None，condition为None
        sql = "SELECT {} FROM {}".format(target_names_str, table_name)  # 拼装完整SQL
    else:
        sql = condition

    if db_type == 'mysql':
        with UsingMysql() as dbm:
            return __query(dbm)
    elif db_type == 'oracle':
        with UsingOracle() as dbo:
            return __query(dbo)
    elif db_type == 'DRG':
        with UsingSQLite('DRG') as dbo:
            return __query(dbo)
    return ''


def df_to_sql(data: DataFrame, db_name):
    data.to_sql(db_name, con=engine, if_exists='append', chunksize=10000, index=False)


def db_exist(key: str, value: list, table: str, db_type: str):
    """
    db_exist('医嘱开始时间', ['2024-10-01', '2024-10-31'], 'yzzxyy.test01', 'mysql')
    """
    sql = f"SELECT EXISTS (SELECT 1 FROM {table} WHERE {key} BETWEEN '{value[0]}' AND '{value[1]}') AS exists_flag"
    result = db(None, None, sql, db_type)
    return result.iloc[0, 0] == 1


if __name__ == "__main__":
    phone = db('TEL', 'DAWN.DAWN_ORG_EMPL', {'empl_id': '25078'}, 'oracle')
    print(phone)
