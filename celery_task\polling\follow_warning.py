import pandas
import json

import custom.settins
from celery_task.celery import app
from custom.db.execute import db
from datetime import datetime


# 随访个性化预警
@app.task
def start():
    # 数据库中查询上次查询时间 + 写入本次查询时间
    start_time = db('value', 'yzzxyy.info_db',
                    {'index_information': 'follow_warning_time'}, 'mysql')
    end_time = str(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

    sql = f'''UPDATE yzzxyy.info_db SET value = '{end_time}' WHERE index_information = 'follow_warning_time' '''
    db(None, None, sql, 'mysql')

    # 查询预警的所有IDCard(查重后)
    id_total = db(['DISTINCT ID_CARD'], 'yzzxyy.follow_warning',
                  None, 'mysql')
    # 查询LIS、PACS、心电图时间范围内变动的数据和his术语
    sql = f"""select t.BARCODE, t.IDENNO, r.ITEMCODE, r.HISITEMCODE, r.RESULT, r.FLAG
                from HNYZ_ZXYY.LIS_TEST_REG t
                         JOIN HNYZ_ZXYY.LIS_RESULT r ON r.BARCODE = t.BARCODE
                where t.IDENNO in ('{"', '".join(id_total['ID_CARD'].unique())}')
                  and t.RESULTDATE between to_date('{start_time}', 'yyyy-mm-dd hh24:mi:ss') 
                                       and to_date('{end_time}', 'yyyy-mm-dd hh24:mi:ss')"""
    lis = db(None, None, sql, 'oracle')
    sql = f"""select DISTINCT p.IDENNO, r.CHECK_NAME, r.CHECK_TYPE, r.CHECK_VIEW, r.CHECK_RESULT
                from HISINTERFACE.TH_CHECK_RECORD r
                         JOIN HNYZ_ZXYY.COM_PATIENTINFO p ON p.CARD_NO = r.PATIENT_ID
                where p.IDENNO in ('{"', '".join(id_total['ID_CARD'].unique())}')
                  and r.REPORT_DATE between to_date('{start_time}', 'yyyy-mm-dd hh24:mi:ss') 
                                       and to_date('{end_time}', 'yyyy-mm-dd hh24:mi:ss')"""
    pacs = db(None, None, sql, 'oracle')
    sql = f"""select DISTINCT p.IDENNO, r.CHECK_NAME, r.CHECK_TYPE, r.CHECK_VIEW, r.CHECK_RESULT
                from BYXDJK.TH_CHECK_RECORD r
                         JOIN HNYZ_ZXYY.COM_PATIENTINFO p ON p.CARD_NO = r.PATIENT_ID
                where p.IDENNO in ('{"', '".join(id_total['ID_CARD'].unique())}')
                  and r.REPORT_DATE between to_date('{start_time}', 'yyyy-mm-dd hh24:mi:ss') 
                                       and to_date('{end_time}', 'yyyy-mm-dd hh24:mi:ss')"""
    electro = db(None, None, sql, 'oracle')
    his_item = db(['TERM_ID ITEM_CODE', 'TERM_NAME ITEM_NAME'], 'HIS_FSHV1.MET_ORDT_UNDRUGTERM',
                  {'TERM_CLASS_NAME': '检验'}, 'oracle')
    # 筛选出哪些人有数据变动
    id_change = pandas.concat([lis['IDENNO'], pacs['IDENNO'], electro['IDENNO']]).unique()

    # 所有预警内容
    warning_total = db(['CONDITIONS', 'ID_CARD', 'USER', 'TIME'], 'yzzxyy.follow_warning',
                       None, 'mysql')
    trigger = []
    for idcard in id_change:  # 身份证
        # 对应身份证号的所有预警内容
        for _, row in warning_total[warning_total['ID_CARD'] == idcard].iterrows():  # condition 为 AND 的大组
            condition_bool = True
            for i, parallel in enumerate(json.loads(row['CONDITIONS'])):  # parallel 为 OR 的小组
                parallel_bool = False  # 是否匹配到
                for item in parallel:  # item 为具体的小内容了
                    if parallel_bool:
                        break
                    if item['item0_content'] == '检验':
                        # 身份证过滤
                        content = lis[lis['IDENNO'] == idcard]
                        if content.empty:  # 如果指定时间段都没有患者lis结果，就跳过这个小项目
                            continue
                        if item['item1_content'] == '医嘱名' and item['item2_content'] == '模糊匹配':
                            parallel_bool = not pandas.merge(his_item[his_item['ITEM_NAME'].str.contains('输血')],
                                                             content, left_on='ITEM_CODE', right_on='HISITEMCODE',
                                                             how='inner').empty
                        elif item['item1_content'] == '医嘱名' and item['item2_content'] == '精准匹配':
                            parallel_bool = not content[content['HISITEMCODE'] == item['item3_code']].empty
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '全部':
                            parallel_bool = not content[content['ITEMCODE'] == item['item2_code']].empty
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '升高':
                            parallel_bool = not content[(content['ITEMCODE'] == item['item2_code']) &
                                                        (content['FLAG'] == '高')].empty
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '降低':
                            parallel_bool = not content[(content['ITEMCODE'] == item['item2_code']) &
                                                        (content['FLAG'] == '低')].empty
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '匹配':
                            parallel_bool = not content[(content['ITEMCODE'] == item['item2_code']) &
                                                        (content['RESULT'].str.contains(item['item4_content']))].empty
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '大于':
                            numeric = content[pandas.to_numeric(content['RESULT'], errors='coerce').notnull()]
                            try:
                                parallel_bool = not numeric[(numeric['ITEMCODE'] == item['item2_code']) &
                                                            (numeric['RESULT'].astype(float) > float(
                                                                item['item4_content']))].empty
                            except ValueError:
                                continue
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '小于':
                            numeric = content[pandas.to_numeric(content['RESULT'], errors='coerce').notnull()]
                            try:
                                parallel_bool = not numeric[(numeric['ITEMCODE'] == item['item2_code']) &
                                                            (numeric['RESULT'].astype(float) < float(
                                                                item['item4_content']))].empty
                            except ValueError:
                                continue
                        elif item['item1_content'] == '项目名' and item['item3_content'] == '全部':
                            parallel_bool = not content[content['ITEMCODE'] == item['item2_code']].empty
                    elif item['item0_content'] == '检查':
                        category = eval(custom.settins.check_type_comparison[item['item1_content']])
                        if isinstance(category, str):
                            category = (category,)

                        # 身份证及类型过滤
                        content = pacs[(pacs['IDENNO'] == idcard) & (pacs['CHECK_TYPE'].isin(category))]
                        if item['item2_content']:
                            content = content[content['CHECK_NAME'].str.contains(item['item2_content'])]
                        if content.empty:
                            continue
                        if item['item3_content'] == '描述':
                            parallel_bool = not content[content['CHECK_VIEW'].str.contains(item['item4_content'])].empty
                        elif item['item3_content'] == '结论':
                            parallel_bool = not content[
                                content['CHECK_RESULT'].str.contains(item['item4_content'])].empty
                        elif item['item3_content'] == '全部':
                            parallel_bool = True
                    elif item['item0_content'] == '心电图':
                        # 身份证及类型过滤
                        content = electro[(electro['IDENNO'] == idcard) &
                                          (electro['CHECK_TYPE'] == item['item1_content'])]
                        if content.empty:
                            continue
                        if item['item3_content'] == '描述':
                            parallel_bool = not content[content['CHECK_VIEW'].str.contains(item['item4_content'])].empty
                        elif item['item3_content'] == '结论':
                            parallel_bool = not content[
                                content['CHECK_RESULT'].str.contains(item['item4_content'])].empty
                        elif item['item3_content'] == '全部':
                            parallel_bool = True
                # OR 的小组检查结果
                if not parallel_bool:
                    condition_bool = False
                    break
            # AND 的大组检查结果
            if condition_bool:
                # 预警方案被触发
                trigger.append({'ID_CARD': row['ID_CARD'], 'USER': row['USER'], 'TIME': row['TIME']})

    # 把预警触发的结果写入数据库
    if not trigger:
        return
    # 提取身份证对应的姓名
    id_card = list(set([t['ID_CARD'] for t in trigger]))
    name_total = db(['DISTINCT IDENNO', 'NAME'], 'HNYZ_ZXYY.COM_PATIENTINFO',
                    f"IDENNO in ({str(id_card)[1:-1]})", 'oracle')
    add = []
    for item in trigger:
        add.append(f"( {item['USER']}, {item['ID_CARD']}, '{item['TIME']}' )")
        try:
            name = name_total[name_total['IDENNO'] == item['ID_CARD']]['NAME'].values[0]
        except KeyError:
            name = '未知'
        # todo
        # res = send_sms.apply_async(args=['follow_warning', item['USER']],
        #                            kwargs={'patient_name': name, 'time': item['TIME'].strftime('%Y-%m-%d %H:%M')},
        #                            priority=4
        #                            )
    if add:
        sql = f'''INSERT INTO yzzxyy.follow_warning_history (USER, ID_CARD, CONDITIONS_TIME)
                    VALUES {', '.join(add)} '''
        db(None, None, sql, 'mysql')
