# 第一阶段：构建环境
FROM docker.1ms.run/python:3.11.9-slim-bookworm as builder
ENV TZ=Asia/Shanghai
WORKDIR /usr/src/app

# 配置APT源
RUN rm -f /etc/apt/sources.list /etc/apt/sources.list.d/*
COPY sources.list /etc/apt/sources.list
ENV DEBIAN_FRONTEND=noninteractive

# 安装cx_Oracle编译所需的库
RUN apt-get update &&  \
    apt-get install -y --no-install-recommends  \
    gcc \
    python3-dev \
    libaio1  \
    && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 创建虚拟环境，路径为/opt/venv
RUN /usr/local/bin/python3.11 -m venv --copies /opt/venv
# 设置 PATH 环境变量（export PATH="/opt/venv/bin:$PATH"）
ENV PATH="/opt/venv/bin:$PATH"

# 拷贝依赖文件
COPY requirements.txt .
# 无缓存安装依赖
RUN pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip install --compile --no-cache-dir -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

#第二阶段：生产镜像
FROM docker.1ms.run/python:3.11.9-slim-bookworm

ARG USERNAME=celery
ARG UID=1000
ARG GID=1000

RUN rm -f /etc/apt/sources.list /etc/apt/sources.list.d/*
COPY sources.list /etc/apt/sources.list
RUN apt-get update && \
    apt-get install -y --no-install-recommends  \
    gosu  \
    libpython3.11 \
    libpcre3 \
    zlib1g \
    procps \
    supervisor \
    unzip \
    libaio1 \
    && apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 从构建阶段复制环境
COPY --from=builder --chown=${UID}:${GID} /opt/venv /opt/venv

ENV TZ=Asia/Shanghai

#（需挂载/app/celery，挂载/app/record，挂载/var/log/supervisor）
RUN ln -sf /opt/venv/bin/python3.11 /opt/venv/bin/python && \
    ln -sf /opt/venv/bin/python3.11 /opt/venv/bin/python3 && \
    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime &&  \
    echo $TZ > /etc/timezone
ENV PATH="/opt/venv/bin:$PATH"
RUN echo 'export PATH="/opt/venv/bin:$PATH"' >> /etc/profile.d/venv.sh && \
    chown ${UID}:${GID} /etc/profile.d/venv.sh

RUN mkdir -p /app/celery && \
    mkdir -p /app/record && \
    chown -R ${UID}:${GID} /app/celery /app/record

# 配置supervisor进程管理工具
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
RUN chown ${UID}:${GID} /etc/supervisor/conf.d/supervisord.conf && \
    mkdir -p /etc/supervisor/conf.d && \
    chown -R ${UID}:${GID} /etc/supervisor/conf.d

# 启动脚本
COPY docker-entrypoint.sh /docker-entrypoint.sh
RUN chmod +x /docker-entrypoint.sh && \
    chown ${UID}:${GID} /docker-entrypoint.sh

# 容器运行配置
WORKDIR /app/celery

ENTRYPOINT ["/./docker-entrypoint.sh"]
CMD ["celery"]