import ast
import json
import requests

from typing import <PERSON><PERSON>

from celery_task.celery import app
from celery_task.privacy.common import encrypt_3des, create_retry_session
from celery_task.privacy.crawler import retry_session
from custom.db.execute import db
from custom.db.mysql import UsingMysql
from custom.db.redis import redis_client

with UsingMysql() as um:
    privacy = ast.literal_eval(
        um.fetch_decrypt("select value from info_db where index_information = 'privacy_dx' ",
                         None, 'value'))

TIMEOUT = (5, 50)  # 连接超时3.05s，读取超时27s
SSL_VERIFY = True  # 生产环境必须验证SSL证书
CA_BUNDLE = True  # CA证书路径 '/path/to/cert.pem'（或设置为True使用系统默认）

COMMON_PAYLOAD = {
    "deptCode": "0001", "isdept": 0, "sipPhoneOrder": 1, "recordFlag": 1, "city": 1, "country": 1, "international": 0,
    "transferNumber2": None, "transferNumber3": None, "transferType": 1, "extStatus": 1, "distrubType": 0,
    "ttsContent": "", "extType": 0, "ringingStatus": 0, "recordText": 0}


def post_session(api_url, params) -> dict:
    session = create_retry_session()
    try:
        response = session.post(api_url,
                                params=params,
                                verify=CA_BUNDLE if SSL_VERIFY else False,  # SSL证书验证
                                timeout=TIMEOUT)
        response.raise_for_status()  # 自动触发HTTP错误异常

        try:
            json_response = response.json()
            msg = json_response.get('msg', '') or json_response.get('status', json_response).get('message', '')
            if json_response.get('status', json_response).get('code', '') == '0010':
                return {'success': True, 'data': json_response.get('data', {}), 'msg': msg}
            else:
                return {'success': False, 'data': {}, 'msg': msg}
        except json.JSONDecodeError:
            return {'success': False, 'data': {}, 'msg': '响应包含无效的JSON格式'}

    except requests.exceptions.SSLError as e:
        return {'success': False, 'data': {}, 'msg': 'SSL证书验证失败'}
    except requests.exceptions.ConnectionError as e:
        return {'success': False, 'data': {}, 'msg': f"连接失败: {str(e)}"}
    except requests.exceptions.Timeout as e:
        return {'success': False, 'data': {}, 'msg': f"请求超时: {str(e)}"}
    except requests.exceptions.HTTPError as e:
        return {'success': False, 'data': {}, 'msg': f"HTTP错误: {str(e)}"}
    except requests.exceptions.RequestException as e:
        return {'success': False, 'data': {}, 'msg': f"请求异常: {str(e)}"}
    except Exception as e:
        return {'success': False, 'data': {}, 'msg': f"未知异常: {str(e)}"}
    finally:
        session.close()


def _get_token() -> Tuple[bool, str]:
    """获取api访问令牌
    :return {'data': {}, 'msg': '非法IP访问', 'success': False}"""
    payload = {
        "appkey": privacy['appkey'],  # 应用标识
        "appsecret": privacy['appsecret'],  # 应用密钥
        "entId": privacy['entId']  # 企业ID
    }
    api_url = f"{privacy['base_url']}/pbx-api/token"
    params = {"entId": privacy['entId'], "requestData": encrypt_3des(json.dumps(payload))}
    result = post_session(api_url, params)

    token = result.get('data', {}).get('token', '')
    if not result.get('success', False) or not token:
        return False, result.get('msg', '未知')

    if not redis_client.set('privacy_bind_token', token, ex=50 * 60):
        return False, 'redis设置失败'
    return True, token


def _get_bind_payload(phone) -> Tuple[bool, dict]:
    """ 检查电话，是否绑定或发起绑定
    return: 是否为已绑定的老号码 | 用来绑定的payload """
    is_exist = db(None, None,
                  f"""SELECT USER_ID,
                                       BINDING_TIME >= (CURRENT_TIMESTAMP - INTERVAL 1 HOUR) AS VALID,
                                       BINDING_PHONE
                                FROM yzzxyy.follow_bind WHERE MOBILE_PHONE= '{phone}'""",
                  'mysql')
    if not is_exist.empty:
        data = is_exist.iloc[0]
        return False, {"userId": data['USER_ID'], "valid": data['VALID'], "bindingPhone": data['BINDING_PHONE']}

    # 新号码
    idle = db(None, None,
              f"""SELECT USER_ID, BINDING_PHONE, EXT_NO, USER_NAME, USER_TYPE
                            FROM yzzxyy.follow_bind
                            ORDER BY CAPTURE, BINDING_TIME LIMIT 1""",
              'mysql')
    if not idle.empty:
        idle = idle.iloc[0]
        payload_update = {"userId": idle['USER_ID'],
                          "bindingPhone": idle['BINDING_PHONE'],
                          "transferNumber1": idle['BINDING_PHONE'],
                          "extNo": idle['EXT_NO'],
                          "userName": idle['USER_NAME'],
                          "userType": str(idle['USER_TYPE']),
                          "mobilePhone": phone}
        return True, COMMON_PAYLOAD | payload_update
    raise ValueError('号码池为空')


def _get_session_result(call_session, params):
    # redis有token
    retries = 0
    token = redis_client.get('privacy_bind_token')
    if token:
        suc, msg = call_session(token, params)
        if suc:
            return {'success': True, 'msg': msg}
        if not msg.startswith('请求token'):
            return {'success': False, 'msg': msg}
    # redis没有token或token失效
    while retries < 3:
        suc, token = _get_token()
        retries += 1
        if suc:
            break
    else:
        return {'success': False, 'msg': msg}
    suc, msg = call_session(token, params)
    return {'success': True, 'msg': msg} if suc else {'success': False, 'msg': msg}


@app.task
def call(doctor_phone, patient_phone) -> dict:
    """开始发起双呼
    :return """
    try:
        success, msg = modify(doctor_phone)  # 绑定
        if not success:
            return {'success': False, 'msg': msg}
        payload = {
            "called1": doctor_phone,  # 被叫号码1(需要对应分机的短号或者绑定手机号)
            "called2": patient_phone,  # 被叫号码2
            # "pbxCall": 'Y',  # 是否以总机外呼：显示统一号码 Y:是  N:否;不传默认是
            # "called1PhoneType": 3,  # 未指定 默认呼叫分机绑定的手机号；1:分机绑定的ip话机，2:分机绑定的手机号，3：分机绑定的直线号码
        }
        api_url = f"{privacy['base_url']}/pbx-api/api/call/mutual"
        params = {"serialId": "",
                  "timestamp": "",
                  "requestData": encrypt_3des(json.dumps(payload))}

        def call_session(token_i, params_i):
            params_i['token'] = token_i
            result_i = post_session(api_url, params_i)  # 阻塞位置（接不接通都是{'data': {}, 'msg': '接收成功', 'success': True}）
            if result_i.get('success', False):
                # 添加成功发起通话的记录
                db(None, None,
                   f"""INSERT INTO yzzxyy.follow_call (CALL_1, CALL_X, CALL_2,TIME) VALUES
                                ('{doctor_phone}', '{msg}', '{patient_phone}',CURRENT_TIMESTAMP) """, 'mysql')
                ids = db(None, None,
                         f"""SELECT ID FROM yzzxyy.follow_call 
                                         WHERE CALL_1  = '{doctor_phone}' 
                                            AND CALL_X = '{msg}' 
                                            AND CALL_2 = '{patient_phone}' 
                                            ORDER BY TIME DESC LIMIT 1""", 'mysql').iloc[0]['ID']
                result_i['id'] = str(ids)
                return True, result_i
            return False, result_i.get('msg', '未知')

        return _get_session_result(call_session, params)
    except Exception as e:
        return {'success': False, 'msg': f"程序出错: {str(e)}"}


@app.task
def notice(called, content, play_size=1, is_tts=True) -> dict:
    """ 开始语音通知 """
    try:
        payload = {"serialId": "",
                   "called": called,
                   "notifyType": "tts" if is_tts else "vox"}
        if is_tts:
            payload["ttsContent"] = content * play_size
        else:
            payload["voxFileName"] = content
            payload["playSize"] = play_size
        api_url = f"{privacy['base_url']}/pbx-api/api/call/voice_notice"
        params = {"msgid": "",
                  "timestamp": "",
                  "requestData": encrypt_3des(json.dumps(payload))}

        def call_session(token_i, params_i):
            params_i['token'] = token_i
            result_i = post_session(api_url, params_i)  # 阻塞位置（接不接通都是{'data': {}, 'msg': '接收成功', 'success': True}）
            if result_i.get('success', False):
                return True, result_i
            return False, result_i.get('msg', '未知')

        return _get_session_result(call_session, params)
    except Exception as e:
        return {'success': False, 'msg': f"程序出错: {str(e)}"}


def modify(doctor_phone) -> Tuple[bool, str]:
    """ 修改医生号码 返回 是否修改成功 | 绑定的中间号码 或 错误提示 """
    try:
        new_bind, payload = _get_bind_payload(doctor_phone)
    except Exception as e:  # 响应号码池为空的情况
        return False, f"{str(e)}"
    if not new_bind and payload.get('valid', 0) == 0:  # 已绑定的老号码，且 最近绑定超1小时，重置计数
        db(None, None,
           f"""UPDATE yzzxyy.follow_bind 
                        SET BINDING_TIME = CURRENT_TIMESTAMP, CAPTURE = 1
                        WHERE USER_ID = '{payload['userId']}' """, 'mysql')
        return True, payload['bindingPhone']
    elif not new_bind:  # 已绑定的老号码，且 最近绑定未超1小时，计数+1 （通话记录还未获取到）
        db(None, None,
           f"""UPDATE yzzxyy.follow_bind 
                        SET BINDING_TIME = CURRENT_TIMESTAMP, CAPTURE = CAPTURE + 1
                        WHERE USER_ID = '{payload['userId']}' """, 'mysql')
        return True, payload['bindingPhone']

    # 新号码
    post_kwargs = {
        'url': 'https://www.hn-yzj.cn/cloud/ext/em/newEdit',
        'payload': payload,
        'method': 'post_json',
        'headers': {'Content-Type': 'application/json',
                    'Sec-Fetch-Site': 'same-origin',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Accept': 'application/json, text/plain, */*',
                    'Referer': 'https://www.hn-yzj.cn/cloud/home/<USER>',
                    'Origin': 'https://www.hn-yzj.cn'}}
    success, login_result = retry_session(post_kwargs)
    if not success:
        raise ValueError(login_result)

    if login_result.get('msg', {}).get('status', '') == 'success':
        # 新号码，更新绑定状态和时间
        db(None, None,
           f"""UPDATE yzzxyy.follow_bind 
                        SET BINDING_TIME = CURRENT_TIMESTAMP, CAPTURE = 1, MOBILE_PHONE = '{doctor_phone}'
                        WHERE USER_ID = '{post_kwargs['payload'].get('userId', '')}' """, 'mysql')
        return True, payload['bindingPhone']
    return False, str(login_result.get('msg', {}))


if __name__ == '__main__':
    print(1)
    # test = asyncio.run(Privacy.bind_privacy_async(doctor_phone, patient_phone, loss))
    # return {'patient_phone': test.get('extension', ''),  # 患者号码
    #         'privacy_phone': test.get('secret_no', ''),  # 隐私号码
    #         'subs_id': test.get('subs_id', '')  # 绑定ID，用于通话记录查询
    #         }
