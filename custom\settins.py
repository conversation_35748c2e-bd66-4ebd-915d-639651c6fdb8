# 自定义基础设置

title = "永医预警系统"
version = "v1.1.0"  # 程序版本号
website = "https://hnsyzszxyy.com/index.aspx"  # 官方网址，目前没用，放在更新程序里面的那个按钮，隐藏了。
update = "http://175.16.7.95/fusion/static/update/update.json"

USE = 'bPe7O7rPX6VXOj/Nh3dhLw=='  # 基础user
PW = '1yRTMO5fTMhhL9gGXaLhTA=='  # 基础pwd
K = 'hnyzzxyy0salmon0'  # 基础key

column_title_format = {
    'bold': True,
    'align': 'center',
    'bg_color': '#D9D9D9',
    'border': 1
}
cell_format = {'border': 1}

operation_record = '操作记录回溯'  # 用于历史记录时追加的尾部提示词

"""
不良事件查询的sql，必须要提供短信平台要求的数据别名：'记录内容_隐藏'、’床号_隐藏‘、’工号_隐藏‘
不良事件的历史sql，除以上内容外，还需要提供‘序号_隐藏’
"""

notification_time = '08:00'  # 普通通知时间
start_time_of_manual_follow_up = 12  # 人工随访开始时间
key_patients_waiting_time_for_execution = 7  # 重点患者执行等待时间
effective_registration_time_before_reminding_key_patients = 7  # 重点患者提醒前有效挂号时间
timeout_reminder = 2  # 人工随访超时提醒
serious_timeout_reminder = 2  # 人工随访严重超时提醒
director_reminder = 2  # 主任提醒

normal_follow_up_interval = 7  # 普通患者首次随访间隔天数
effective_time_ratio = 0.5  # 多少时间范围内的挂号算有效
warning_again_time_ratio = 0.5  # 超时多久还没挂号，派发随访任务
maximum_timeout_days = 7  # 最长超时天数，和上面配合，最长不超过7天。
doctor_timeout_day1 = 3  # 医生超时天数
doctor_timeout_day2 = 4  # 医生严重超时天数
doctor_timeout_day3 = 7  # 升级主任天数
number_of_cycles = 3  # 对于周期性随访任务，跟踪随访多少次

check_type_comparison = {'CT': "('CT')",
                         'MRI': "('MR')",
                         'X线': "('DR')",
                         '彩超': "('US')",
                         '内镜': "('ES')",
                         '手术标本': "('组织学')",
                         '细胞标本': "('细胞学', '体检', '检验显微镜')",
                         '透视': "('RF')",
                         'ECT': "('ECT')",
                         'DSA': "('DSA')",
                         '钼靶': "('MG')"}

if __name__ == "__main__":
    basic_column = {'inpatient_no': '住院号',
                    'recode_time': '记录时间',
                    'show_column': '记录内容',
                    'code_column': '工号_隐藏',
                    'bed_column': '床号',
                    'name_column': '姓名',
                    }  # 固定的索引

    from custom.function.methods_general import *

    # 加密
    # key = b'hnyzzxyy0salmon0'
    key = b'Whe-@.1rpEE7ri8V'
    cipher = AES.new(key, AES.MODE_ECB)
    # appId = "your_app_id"  # 替换为实际appId
    # token = "your_token"  # 替换为实际token
    # {'appkey': '796ad63bbb6b448fb117a185aa0d0a39', 'appsecret': 'cca21a3b317344e4b942466e61272119', 'entId': '1750064861271018307', 'secret_key': '183011650110499994876335', 'base_url': 'http://*************:9082'}
    plaintext = b"redis://:redis_e52jr7@***************:11000"
    # 确保明文长度是块大小的倍数
    plaintext = plaintext.ljust(len(plaintext) + (16 - len(plaintext) % 16), b'\0')
    enc = cipher.encrypt(plaintext)
    print(base64.b64encode(enc).decode('utf-8'))  # 打印加密后的结果

    print(decrypt('Whe-@.1rpEE7ri8V',
                  'jrUvMlYyXfs1HzGZH62H3WmU0wkBGf4BlBWc6T3jnSoNSSfzTV2GQFvhHf4nAb7S'))
#
#
