from celery_task.celery import app
from celery_task.polling.method import is_same_homepage, drgs_from_no, drgs_from_df, update_history
from custom.db.execute import db
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor


@app.task
def start():
    num = db(None, None,
             f"SELECT INPATIENT_NO FROM NMRWS.NMRWS_MR_HOMEPAGE WHERE OUT_DATE >= DATE'2025-05-01' AND OUT_DATE < DATE'2025-06-01' ",
             'oracle')
    inpatient_nos = num['INPATIENT_NO'].tolist()
    # 创建线程池 (根据数据库连接池大小调整max_workers)
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(process_inpatient_no, no) for no in inpatient_nos]
        success_count = 0
        progress = tqdm(total=len(futures), desc="Processing")
        for future in futures:
            result = future.result()  # 这里会阻塞直到任务完成
            success_count += int(result)
            progress.update(1)
            progress.set_postfix(success=success_count, failed=progress.n - success_count)
        progress.close()


def start_db():
    num = db(None, None,
             f"SELECT A48, A49 FROM my_table WHERE CMI = 0",
             'HomePage')
    # 将两列数据转换为嵌套列表结构：[[A48, A49], [A48, A49], ...]
    inpatient_nos = num[['A48', 'A49']].apply(lambda row: [row['A48'], row['A49']], axis=1).tolist()

    # 创建线程池 (根据数据库连接池大小调整max_workers)
    with ThreadPoolExecutor(max_workers=20) as executor:
        futures = [executor.submit(process_inpatient_no_db, no) for no in inpatient_nos]
        success_count = 0
        progress = tqdm(total=len(futures), desc="Processing")
        for future in futures:
            result = future.result()  # 这里会阻塞直到任务完成
            success_count += int(result)
            progress.update(1)
            progress.set_postfix(success=success_count, failed=progress.n - success_count)
        progress.close()


def process_inpatient_no_db(inpatient_no):
    """处理单个住院号的函数（线程安全版本）"""
    try:
        drgs = drgs_from_df(inpatient_no)
        db(None, None,
           f"""UPDATE my_table
                           SET CMI = {'NULL' if not drgs.get('CMI', '') else drgs['CMI'].get('cmi', 'NULL')}
                           WHERE A48 = '{inpatient_no[0]}' AND A49 = '{inpatient_no[1]}' """,
           'HomePage')
        return True
    except Exception as e:
        print(f"Error processing {inpatient_no}: {str(e)}")
        return False


def process_inpatient_no(inpatient_no):
    """处理单个住院号的函数（线程安全版本）"""
    try:
        same = is_same_homepage(inpatient_no)
        drgs_d = drgs_from_no(inpatient_no, is_doctor=True, full=True)
        drgs_m = drgs_d if same else drgs_from_no(inpatient_no, is_doctor=False, full=True)
        cmi_d = drgs_d.get('weight', {}).get('cmi', None) if drgs_d.get('weight') else None
        cmi_m = drgs_m.get('weight', {}).get('cmi', None) if drgs_m.get('weight') else None
        # 判断四级手术、条件四级、微创手术
        condition = f"""ICD IN ('{"', '".join([drgs_m.get('main_oper_code', '')] + drgs_m.get('other_oper_code', []))}')"""
        level4 = db('1', 'CODE_LEVEL4', condition, 'DRG') == 1
        level4c = db('1', 'CODE_LEVEL4C', condition, 'DRG') == 1
        mic = db('1', 'CODE_MIC', condition, 'DRG') == 1
        update_history(inpatient_no, drgs_d, cmi_d, drgs_m, cmi_m, level4, level4c, mic)
        return True
    except Exception as e:
        print(f"{inpatient_no} 处理失败: {str(e)}")
        return False

# start()
