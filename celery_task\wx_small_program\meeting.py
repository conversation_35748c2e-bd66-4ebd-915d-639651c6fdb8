# 会议相关
import pandas

from celery_task.celery import app
from custom.db.execute import db


@app.task
def get_meeting_worker(name: str):
    sql = f"""
        SELECT m.m_name
        FROM meeting.meetings m
        JOIN meeting.participants p ON m.m_id = p.m_id
        WHERE p.name = '{name}'
    """
    df = db(None, None, sql, 'mysql')

    if df.empty:
        return []

    return df['m_name'].tolist()


@app.task
def get_meeting_info_worker(name: str, meeting_name: str):
    sql = f"""
        SELECT me.m_name,pa.`name`,pa.seat_code,me.start_time,me.duration,ro.room_name
        FROM meeting.participants AS pa
        JOIN meeting.meetings me ON me.m_id = pa.m_id
        JOIN meeting.meeting_room ro ON me.m_room = ro.room_id
        WHERE pa.name = '{name}'AND me.m_name='{meeting_name}'
    """
    df = db(None, None, sql, 'mysql')

    if df.empty:
        return []

    if 'start_time' in df.columns and pandas.api.types.is_datetime64_any_dtype(df['start_time']):
        df['start_time'] = df['start_time'].dt.strftime('%Y-%m-%d %H:%M:%S')

    for col in df.select_dtypes(include=['timedelta']).columns:
        df[col] = df[col].astype(str)

    return df.to_dict(orient='records')
