"""
消息总线模块 - 简化版
统一管理语音通知、录音通知、微信模板消息等多种消息类型
"""

from .core import MessageType, message_bus
from .handlers import HandlerFactory

# 初始化消息总线 - 注册处理器
def _init_message_bus():
    """初始化消息总线，注册所有处理器"""
    try:
        # 注册 TTS 语音处理器
        message_bus.register_handler(
            MessageType.VOICE_TTS,
            HandlerFactory.create_handler(MessageType.VOICE_TTS)
        )

        # 注册 VOX 录音处理器
        message_bus.register_handler(
            MessageType.VOICE_VOX,
            HandlerFactory.create_handler(MessageType.VOICE_VOX)
        )

        # 注册微信模板消息处理器
        message_bus.register_handler(
            MessageType.WECHAT_TEMPLATE,
            HandlerFactory.create_handler(MessageType.WECHAT_TEMPLATE)
        )

    except Exception as e:
        print(f"[MSGBUS] 初始化失败: {e}")

# 执行初始化
_init_message_bus()

# 导入 API（放在初始化之后）
from .api import msgbus_api

__all__ = ['msgbus_api']