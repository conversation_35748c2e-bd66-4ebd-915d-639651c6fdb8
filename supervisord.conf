[supervisord]
nodaemon=true
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[program:celery-worker]
command=celery -A celery_task worker --loglevel=warning
directory=/app/celery
autostart=true
autorestart=true
; user=celery
environment=UID="1000",GID="1000"

[program:celery-beat]
command=celery -A celery_task beat --loglevel=warning
directory=/app/celery
autostart=true
autorestart=true
; user=celery
environment=UID="1000",GID="1000"

; [program:nginx]
; command=nginx -g "daemon off;"
; autostart=true
; autorestart=true
; user=root
;
; [program:uwsgi]
; command=uwsgi --ini /app/uwsgi.ini
; autostart=true
; autorestart=true
; user=celery
