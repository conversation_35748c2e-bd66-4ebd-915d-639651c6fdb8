# 消息总线系统

## 概述

消息总线系统统一管理语音通知、录音播放、微信模板消息等多种消息类型，提供简洁的API接口和异步处理能力。

## 核心特性

- **统一接口**: 所有消息类型使用统一的API
- **异步处理**: 基于Celery的异步任务处理
- **优先级控制**: 支持消息优先级设置，与Celery队列优先级集成
- **错误处理**: 完善的错误处理机制
- **向后兼容**: 保持与原有API的兼容性

## 快速开始

### 基本用法

```python
from celery_task.msgbus import msgbus_api

# TTS语音通知
result = msgbus_api.send_voice_tts(
    phone="13800138000",
    text="您有新的随访任务需要处理",
    priority="NORMAL"
)

# VOX录音播放
result = msgbus_api.send_voice_vox(
    phone="13800138000",
    file_name="notification.wav",
    priority="HIGH"
)

# 微信模板消息
result = msgbus_api.send_wechat_template(
    open_id="oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    template_type="generate",
    data={"thing33": {"value": "随访任务"}},
    priority="URGENT"
)

# 微信模板消息 - 带小程序跳转
result = msgbus_api.send_wechat_template(
    open_id="oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    template_type="generate",
    data={"thing33": {"value": "随访任务"}},
    miniprogram={
        "appid": "wxadf2deda3469b806",
        "pagepath": "pages/tasks/index?id=123"
    }
)

# 批量发送消息
notifications = [
    {"type": "voice_tts", "phone": "13800138000", "text": "您有新任务"},
    {"type": "wechat_template", "open_id": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
     "template_type": "generate", "data": {"thing33": {"value": "随访任务"}}}
]
results = msgbus_api.send_batch_messages(notifications)
```

### 优先级说明

| 优先级 | 值 | 说明 | 使用场景 |
|--------|----|----- |----------|
| `URGENT` | 1 | 紧急优先级 | 系统故障、严重超时 |
| `HIGH` | 3 | 高优先级 | 重要通知、任务超时 |
| `NORMAL` | 5 | 普通优先级（默认） | 日常任务、一般提醒 |
| `LOW` | 8 | 低优先级 | 统计报告、非紧急信息 |

## 详细使用方法

### 基本用法

```python
from celery_task.msgbus import msgbus_api

# TTS语音通知 - 基本用法
result = msgbus_api.send_voice_tts(
    phone="13800138000",
    text="您有新的随访任务需要处理，请及时登录系统查看。"
)

# TTS语音通知 - 完整参数
result = msgbus_api.send_voice_tts(
    phone="13800138000",
    text="紧急通知：您有3项随访任务已超时，请立即处理！",
    play_size=2,           # 播放2次
    priority="HIGH",       # 高优先级
    async_mode=True        # 异步发送（默认）
)

# VOX录音播放
result = msgbus_api.send_voice_vox(
    phone="13800138000",
    file_name="urgent_notification.wav",
    play_size=1,
    priority="URGENT"
)

# 微信模板消息 - 随访任务生成通知
result = msgbus_api.send_wechat_template(
    open_id="oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    template_type="generate",
    data={
        "thing33": {"value": "随访任务"},
        "thing4": {"value": "计划任务"},
        "number19": {"value": "5"}
    },
    url="https://hospital.com/tasks",  # 点击跳转链接
    priority="NORMAL"
)

# 微信模板消息 - 任务超时通知
result = msgbus_api.send_wechat_template(
    open_id="oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    template_type="timeout",
    data={
        "thing3": {"value": "随访任务"},
        "thing6": {"value": "任务超时"},
        "character_string29": {"value": "3"}
    },
    priority="HIGH"
)
```

### 批量发送

```python
# 批量发送多种类型的消息
notifications = [
    {
        "type": "voice_tts",
        "phone": "13800138000",
        "text": "您有新的随访任务需要处理",
        "priority": "NORMAL"
    },
    {
        "type": "voice_vox",
        "phone": "13800138001",
        "file_name": "urgent_notification.wav",
        "play_size": 2,
        "priority": "HIGH"
    },
    {
        "type": "wechat_template",
        "open_id": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
        "template_type": "generate",
        "data": {
            "thing33": {"value": "随访任务"},
            "thing4": {"value": "计划任务"},
            "number19": {"value": "5"}
        },
        "priority": "NORMAL"
    }
]

# 执行批量发送
results = msgbus_api.send_batch_messages(notifications)

# 统计发送结果
success_count = sum(1 for r in results if r.get('success', False))
print(f"批量发送完成: {success_count}/{len(results)} 成功")

# 处理失败的消息
for i, result in enumerate(results):
    if not result.get('success', False):
        print(f"消息 {i+1} 发送失败: {result.get('message', '未知错误')}")
```

### 微信小程序跳转

```python
# 微信模板消息 - 带小程序跳转
result = msgbus_api.send_wechat_template(
    open_id="oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    template_type="generate",
    data={
        "thing33": {"value": "随访任务"},
        "thing4": {"value": "计划任务"},
        "number19": {"value": "5"}
    },
    # 小程序跳转配置
    miniprogram={
        "appid": "wxadf2deda3469b806",           # 小程序AppID
        "pagepath": "pages/tasks/index?id=123"   # 跳转页面路径
    },
    priority="NORMAL"
)

# 微信模板消息 - 同时支持网页和小程序跳转
result = msgbus_api.send_wechat_template(
    open_id="oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
    template_type="timeout",
    data={
        "thing3": {"value": "随访任务"},
        "thing6": {"value": "任务超时"},
        "character_string29": {"value": "3"}
    },
    url="https://hospital.com/tasks/urgent",     # 网页跳转链接（备用）
    miniprogram={
        "appid": "wxadf2deda3469b806",
        "pagepath": "pages/urgent/index?type=timeout"
    },
    priority="HIGH"
)

# 小程序跳转参数说明
miniprogram_config = {
    "appid": "小程序的AppID",                    # 必填
    "pagepath": "pages/index/index?param=value"  # 可选，跳转的小程序页面路径
}

# 注意事项：
# 1. 如果同时设置了url和miniprogram，优先跳转小程序
# 2. 如果用户没有安装小程序，会跳转到url指定的网页
# 3. pagepath参数可以包含查询参数，用于传递数据到小程序页面
```

## 核心组件

### MessagePayload 消息载荷

```python
@dataclass
class MessagePayload:
    """消息载荷"""
    message_type: MessageType              # 消息类型
    target_id: str                         # 目标ID（手机号、openid等）
    content: Dict[str, Any]                # 消息内容
    priority: MessagePriority = NORMAL     # 消息优先级
    metadata: Optional[Dict[str, Any]]     # 元数据（自动生成message_id等）
```

### MessagePriority 优先级

```python
class MessagePriority(Enum):
    """消息优先级（数值越小优先级越高，符合Celery规则）"""
    URGENT = 1    # 紧急优先级 - 最高优先级
    HIGH = 3      # 高优先级
    NORMAL = 5    # 普通优先级 - 默认级别
    LOW = 8       # 低优先级 - 最低优先级
```

### 优先级实现机制

- **Celery集成**: 优先级直接映射到Celery任务优先级
- **队列调度**: 高优先级消息优先执行
- **自动转换**: 字符串优先级自动转换为枚举值

### 同步 vs 异步发送

```python
# 异步发送（默认，推荐用于生产环境）
result = msgbus_api.send_voice_tts(
    phone="13800138000",
    text="异步发送的消息",
    async_mode=True  # 默认值
)

# 同步发送（用于测试或需要立即获取结果的场景）
result = msgbus_api.send_voice_tts(
    phone="13800138000",
    text="同步发送的消息",
    async_mode=False
)

# 异步发送的优势：
# 1. 不阻塞主线程
# 2. 更好的并发性能
# 3. 自动重试机制

# 同步发送的优势：
# 1. 立即获取发送结果
# 2. 便于调试和测试
# 3. 简单的错误处理
```

## 返回格式

消息总线系统根据不同的发送模式和执行状态，提供多种返回格式。

### 基础返回结构

所有返回都遵循统一的基础结构：

```python
{
    "success": True/False,         # 是否成功
    "message": "结果描述",         # 结果消息
    "data": {...},                 # 详细数据
    "error_code": "ERROR_CODE",    # 错误代码(失败时)
    "duration": 1234.5,            # 执行耗时(毫秒)
    "message_id": "uuid"           # 消息ID
}
```

### 同步模式返回 (async_mode=False)

同步模式下，方法会等待消息发送完成后返回实际执行结果：

#### 同步成功返回

```python
# TTS语音通知成功
{
    "success": True,
    "message": "TTS语音通知发送成功",
    "data": {
        "success": True,
        "msg": "接收成功",
        "serialId": "20241201123456789"
    },
    "duration": 1250.5,
    "message_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
}

# VOX录音播放成功
{
    "success": True,
    "message": "VOX录音播放成功",
    "data": {
        "success": True,
        "msg": "播放完成",
        "serialId": "20241201123456790"
    },
    "duration": 2100.3,
    "message_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012"
}

# 微信模板消息成功
{
    "success": True,
    "message": "微信模板消息发送成功",
    "data": {
        "msgid": 123456789,
        "errcode": 0,
        "errmsg": "ok"
    },
    "duration": 856.2,
    "message_id": "c3d4e5f6-g7h8-9012-cdef-345678901234"
}
```

#### 同步失败返回

```python
# 参数验证失败
{
    "success": False,
    "message": "无效的手机号格式: invalid_phone",
    "error_code": "INVALID_PAYLOAD",
    "duration": 2.1,
    "message_id": "d4e5f6g7-h8i9-0123-def0-456789012345"
}

# TTS语音发送失败
{
    "success": False,
    "message": "TTS语音通知发送失败: 连接超时",
    "error_code": "TTS_SEND_ERROR",
    "duration": 30000.0,
    "message_id": "e5f6g7h8-i9j0-1234-ef01-56789012345a"
}

# 微信模板消息失败
{
    "success": False,
    "message": "invalid openid",
    "data": {
        "errcode": 40003,
        "errmsg": "invalid openid"
    },
    "error_code": "40003",
    "duration": 1200.5,
    "message_id": "f6g7h8i9-j0k1-2345-f012-6789012345ab"
}
```

### 异步模式返回 (async_mode=True)

异步模式下，方法立即返回任务提交状态，不等待实际执行：

#### 异步任务提交成功

```python
# TTS语音通知任务提交
{
    "success": True,
    "message": "TTS语音通知任务已提交",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "submitted",
        "phone": "13800138000",
        "text": "您有新的随访任务需要处理"
    },
    "error_code": None,
    "duration": 0,
    "message_id": None
}

# 微信模板消息任务提交
{
    "success": True,
    "message": "微信模板消息任务已提交",
    "data": {
        "task_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
        "status": "submitted",
        "open_id": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
        "template_type": "generate"
    },
    "error_code": None,
    "duration": 0,
    "message_id": None
}
```

### 异步任务状态查询返回

使用 `msgbus_api.get_task_status(task_id)` 查询异步任务状态：

#### 任务执行中

```python
{
    "success": True,
    "message": "任务执行中",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "pending",
        "state": "PENDING"
    },
    "error_code": None,
    "duration": None,
    "message_id": None
}
```

#### 任务执行成功

```python
{
    "success": True,
    "message": "任务执行成功",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "completed",
        "result": {
            "success": True,
            "message": "TTS语音通知发送成功",
            "data": {
                "success": True,
                "msg": "接收成功",
                "serialId": "20241201123456789"
            },
            "duration": 1250.5,
            "message_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
        }
    },
    "error_code": None,
    "duration": None,
    "message_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890"
}
```

#### 任务执行失败

```python
{
    "success": False,
    "message": "任务执行失败: 连接超时",
    "data": {
        "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
        "status": "failed",
        "error": "连接超时"
    },
    "error_code": "TASK_FAILED",
    "duration": None,
    "message_id": None
}
```

#### 任务状态查询失败

```python
{
    "success": False,
    "message": "查询任务状态失败: 任务不存在",
    "data": {
        "task_id": "invalid-task-id",
        "status": "unknown"
    },
    "error_code": "QUERY_FAILED",
    "duration": None,
    "message_id": None
}
```

### 批量发送返回

批量发送返回一个结果数组，每个元素对应一条消息的发送结果：

#### 批量发送成功示例

```python
# 批量发送3条消息的返回结果
[
    {
        "success": True,
        "message": "TTS语音通知任务已提交",
        "data": {
            "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
            "status": "submitted",
            "phone": "13800138000",
            "text": "您有新任务"
        },
        "error_code": None,
        "duration": 0,
        "message_id": None
    },
    {
        "success": True,
        "message": "VOX录音播放任务已提交",
        "data": {
            "task_id": "b2c3d4e5-f6g7-8901-bcde-f23456789012",
            "status": "submitted",
            "phone": "13800138001",
            "file_name": "urgent_notification.wav"
        },
        "error_code": None,
        "duration": 0,
        "message_id": None
    },
    {
        "success": True,
        "message": "微信模板消息任务已提交",
        "data": {
            "task_id": "c3d4e5f6-g7h8-9012-cdef-345678901234",
            "status": "submitted",
            "open_id": "oXXXXXXXXXXXXXXXXXXXXXXXXXXX",
            "template_type": "generate"
        },
        "error_code": None,
        "duration": 0,
        "message_id": None
    }
]
```

#### 批量发送部分失败示例

```python
# 批量发送3条消息，其中1条失败
[
    {
        "success": True,
        "message": "TTS语音通知任务已提交",
        "data": {
            "task_id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
            "status": "submitted",
            "phone": "13800138000",
            "text": "您有新任务"
        },
        "error_code": None,
        "duration": 0,
        "message_id": None
    },
    {
        "success": False,
        "message": "不支持的消息类型: invalid_type",
        "error_code": "UNSUPPORTED_TYPE"
    },
    {
        "success": False,
        "message": "发送消息失败: 无效的手机号格式",
        "error_code": "SEND_ERROR"
    }
]
```

### 错误代码完整说明

| 错误代码 | 说明 | 触发场景 | 处理建议 |
|---------|------|---------|---------|
| `NO_HANDLER` | 找不到对应的消息处理器 | 消息类型不存在或未注册 | 检查消息类型是否正确 |
| `INVALID_PAYLOAD` | 消息载荷验证失败 | 参数格式错误或缺少必需字段 | 检查参数格式和内容 |
| `SEND_ERROR` | 通用发送错误 | 消息发送过程中的一般性错误 | 查看详细错误信息 |
| `TASK_ERROR` | 任务执行异常 | Celery任务执行时发生异常 | 查看任务日志和错误详情 |
| `TTS_SEND_ERROR` | TTS语音发送失败 | TTS服务调用失败 | 检查网络连接和TTS服务状态 |
| `VOX_SEND_ERROR` | VOX录音播放失败 | VOX服务调用失败 | 检查音频文件是否存在和服务状态 |
| `WECHAT_SEND_ERROR` | 微信消息发送失败 | 微信API调用失败 | 检查openid、模板配置和微信服务状态 |
| `UNSUPPORTED_TYPE` | 不支持的消息类型 | 批量发送中包含不支持的消息类型 | 检查消息类型参数 |
| `TASK_FAILED` | 异步任务执行失败 | 异步任务最终执行失败 | 查看任务执行日志 |
| `QUERY_FAILED` | 任务状态查询失败 | 查询不存在的任务或系统错误 | 检查task_id是否正确 |
| `40003` | 微信API错误：无效的openid | openid格式错误或用户未关注 | 检查openid格式和用户关注状态 |
| `40037` | 微信API错误：无效的模板ID | 模板ID不存在或已被删除 | 检查模板配置 |
| `43004` | 微信API错误：需要用户同意 | 用户拒绝接收模板消息 | 引导用户开启消息接收 |
| `47001` | 微信API错误：数据格式错误 | 模板数据格式不符合要求 | 检查模板数据格式 |

### 返回格式使用建议

#### 1. 判断操作是否成功

```python
result = msgbus_api.send_voice_tts(phone="13800138000", text="测试消息")

if result['success']:
    print("发送成功")
    if result.get('data', {}).get('task_id'):
        # 异步模式：任务已提交
        print(f"任务ID: {result['data']['task_id']}")
    else:
        # 同步模式：已完成发送
        print(f"消息ID: {result.get('message_id')}")
else:
    print(f"发送失败: {result['message']}")
    print(f"错误代码: {result.get('error_code')}")
```

#### 2. 处理异步任务

```python
# 提交异步任务
result = msgbus_api.send_voice_tts(phone="13800138000", text="测试消息", async_mode=True)
if result['success']:
    task_id = result['data']['task_id']

    # 查询任务状态
    import time
    while True:
        status = msgbus_api.get_task_status(task_id)
        if status['data']['status'] == 'completed':
            print("任务执行成功")
            break
        elif status['data']['status'] == 'failed':
            print(f"任务执行失败: {status['message']}")
            break
        else:
            print("任务执行中...")
            time.sleep(1)
```

#### 3. 处理批量发送结果

```python
results = msgbus_api.send_batch_messages(notifications)

# 统计结果
success_count = sum(1 for r in results if r.get('success', False))
total_count = len(results)
print(f"批量发送完成: {success_count}/{total_count} 成功")

# 处理失败的消息
for i, result in enumerate(results):
    if not result.get('success', False):
        print(f"消息 {i+1} 发送失败:")
        print(f"  错误: {result.get('message', '未知错误')}")
        print(f"  错误代码: {result.get('error_code', 'UNKNOWN')}")
```

#### 4. 错误处理最佳实践

```python
def safe_send_notification(phone: str, text: str) -> bool:
    """安全发送通知，包含完整错误处理"""
    try:
        result = msgbus_api.send_voice_tts(phone=phone, text=text, async_mode=False)

        if result['success']:
            print(f"通知发送成功，消息ID: {result.get('message_id')}")
            return True
        else:
            error_code = result.get('error_code')
            if error_code == 'INVALID_PAYLOAD':
                print(f"参数错误: {result['message']}")
            elif error_code == 'TTS_SEND_ERROR':
                print(f"TTS服务错误: {result['message']}")
            else:
                print(f"发送失败: {result['message']} (错误代码: {error_code})")
            return False

    except Exception as e:
        print(f"发送异常: {str(e)}")
        return False

# 使用示例
success = safe_send_notification("13800138000", "测试通知消息")
if not success:
    print("通知发送失败，请检查日志")
```

### 字段说明

| 字段名 | 类型 | 说明 | 出现场景 |
|--------|------|------|---------|
| `success` | boolean | 操作是否成功 | 所有返回 |
| `message` | string | 结果描述信息 | 所有返回 |
| `data` | object/null | 详细数据，结构因场景而异 | 大部分返回 |
| `error_code` | string/null | 错误代码，成功时为null | 失败时必有 |
| `duration` | number/null | 执行耗时(毫秒)，异步提交时为0 | 同步返回和任务完成时 |
| `message_id` | string/null | 消息唯一标识，异步提交时为null | 同步返回和任务完成时 |
| `task_id` | string | 异步任务ID | 异步模式提交时 |
| `status` | string | 任务状态：submitted/pending/completed/failed | 异步相关返回 |

## 配置说明

### 优先级设置

```python
# 可用的优先级级别（按Celery规则，数值越小优先级越高）
"URGENT"  # 紧急优先级 (1) - 最高优先级，立即处理
"HIGH"    # 高优先级 (3) - 重要通知，优先处理
"NORMAL"  # 普通优先级 (5) - 默认级别，正常处理
"LOW"     # 低优先级 (8) - 非紧急通知，延后处理

# 使用示例
msgbus_api.send_voice_tts(
    phone="13800138000",
    text="紧急通知内容",
    priority="URGENT"  # 设置为紧急优先级，将优先于其他消息处理
)

# 优先级的实际效果
# URGENT: 立即处理，插队到队列前端
# HIGH: 优先处理，在普通消息之前
# NORMAL: 正常处理，按FIFO顺序
# LOW: 延后处理，在其他消息之后
```

### 微信模板类型

```python
# 支持的模板类型
"generate"      # 随访任务生成通知
"timeout"       # 随访任务超时提醒
"timeout_chief" # 主任级严重超时通知
"unbind"        # 账号解绑通知

# 各模板类型对应的数据格式
templates = {
    "generate": {
        "thing33": {"value": "任务类型"},
        "thing4": {"value": "任务描述"},
        "number19": {"value": "任务数量"}
    },
    "timeout": {
        "thing3": {"value": "任务类型"},
        "thing6": {"value": "超时级别"},
        "character_string29": {"value": "任务数量"}
    },
    "timeout_chief": {
        "thing8": {"value": "通知类型"},
        "thing5": {"value": "医生姓名"},
        "character_string7": {"value": "任务数量"}
    },
    "unbind": {
        "thing1": {"value": "患者姓名"},
        "thing3": {"value": "解绑原因"},
        "time2": {"value": "解绑时间"}
    }
}
```

### 批量发送配置

```python
# 批量发送支持的消息类型
SUPPORTED_BATCH_TYPES = [
    "voice_tts",        # TTS语音通知
    "voice_vox",        # VOX录音播放
    "wechat_template"   # 微信模板消息
]

# 批量发送消息格式
batch_message_format = {
    "voice_tts": {
        "type": "voice_tts",
        "phone": "手机号",
        "text": "语音内容",
        "play_size": 1,           # 可选，播放次数
        "priority": "NORMAL"      # 可选，优先级
    },
    "voice_vox": {
        "type": "voice_vox",
        "phone": "手机号",
        "file_name": "音频文件名",
        "play_size": 1,           # 可选，播放次数
        "priority": "NORMAL"      # 可选，优先级
    },
    "wechat_template": {
        "type": "wechat_template",
        "open_id": "微信OpenID",
        "template_type": "模板类型",
        "data": {},               # 模板数据
        "url": "跳转链接",        # 可选
        "miniprogram": {},        # 可选，小程序跳转
        "priority": "NORMAL"      # 可选，优先级
    }
}
```

### 微信小程序跳转配置

```python
# 小程序配置
MINIPROGRAM_CONFIG = {
    "appid": "wxadf2deda3469b806",  # 小程序AppID
    "default_page": "pages/index/index"  # 默认跳转页面
}

# 小程序跳转参数格式
miniprogram_params = {
    "appid": "小程序AppID",                    # 必填，小程序的AppID
    "pagepath": "pages/path/index?param=value" # 可选，跳转页面路径及参数
}

# 常用页面路径示例
page_paths = {
    "任务列表": "pages/tasks/index",
    "任务详情": "pages/tasks/detail?id={task_id}",
    "紧急任务": "pages/urgent/index?type=timeout",
    "个人中心": "pages/profile/index",
    "通知中心": "pages/notifications/index"
}

# 跳转优先级说明
# 1. 如果设置了miniprogram参数，优先跳转小程序
# 2. 如果用户未安装小程序或跳转失败，使用url参数跳转网页
# 3. 建议同时设置url和miniprogram，提供更好的用户体验
```

## 扩展开发

### 添加新消息类型

消息总线系统支持扩展新的消息类型。以下是完整的添加流程：

#### 1. 定义消息类型

在 `core.py` 中的 `MessageType` 枚举中添加新类型：

```python
class MessageType(Enum):
    """消息类型枚举"""
    VOICE_TTS = "voice_tts"           # TTS语音通知
    VOICE_VOX = "voice_vox"           # VOX录音播放
    WECHAT_TEMPLATE = "wechat_template"  # 微信模板消息
    SMS = "sms"                       # 新增：短信消息
```

#### 2. 实现消息处理器

在 `handlers.py` 中创建新的处理器类：

```python
class SMSHandler:
    """短信消息处理器"""

    def validate_payload(self, payload: MessagePayload) -> bool:
        """验证载荷"""
        if payload.message_type != MessageType.SMS:
            return False

        content = payload.content
        # 验证必需字段
        if not content.get('text'):
            return False

        # 验证手机号格式
        phone = payload.target_id
        if not phone or len(phone) != 11 or not phone.isdigit():
            return False

        return True

    def send(self, payload: MessagePayload) -> MessageResult:
        """发送短信消息"""
        try:
            # 提取参数
            phone = payload.target_id
            text = payload.content['text']
            template_id = payload.content.get('template_id')

            # 调用短信服务（示例）
            from celery_task.sms.send import send_sms
            result = send_sms.apply_async(
                args=[phone, text, template_id],
                countdown=0
            ).get()

            # 统一返回格式
            return MessageResult(
                success=result.get('success', False),
                message=result.get('message', '短信发送完成'),
                data=result
            )

        except Exception as e:
            return MessageResult(
                success=False,
                message=f"短信发送失败: {str(e)}",
                error_code=ERROR_CODES['SMS_SEND_ERROR']
            )
```

#### 3. 注册处理器

在 `handlers.py` 的 `HandlerFactory` 中注册新处理器：

```python
class HandlerFactory:
    """处理器工厂"""

    _handlers = {
        MessageType.VOICE_TTS: VoiceTTSHandler,
        MessageType.VOICE_VOX: VoiceVOXHandler,
        MessageType.WECHAT_TEMPLATE: WeChatTemplateHandler,
        MessageType.SMS: SMSHandler,  # 新增
    }
```

#### 4. 添加错误代码

在 `config.py` 中添加相应的错误代码：

```python
ERROR_CODES = {
    'NO_HANDLER': 'NO_HANDLER',
    'INVALID_PAYLOAD': 'INVALID_PAYLOAD',
    'SEND_ERROR': 'SEND_ERROR',
    'TASK_ERROR': 'TASK_ERROR',
    'TTS_SEND_ERROR': 'TTS_SEND_ERROR',
    'VOX_SEND_ERROR': 'VOX_SEND_ERROR',
    'WECHAT_SEND_ERROR': 'WECHAT_SEND_ERROR',
    'SMS_SEND_ERROR': 'SMS_SEND_ERROR',  # 新增
    'UNSUPPORTED_TYPE': 'UNSUPPORTED_TYPE'
}
```

#### 5. 创建异步任务

在 `tasks.py` 中添加异步任务函数：

```python
@app.task
def send_sms_async(phone: str, text: str, template_id: Optional[str] = None,
                   priority: str = DEFAULT_PRIORITY, **kwargs) -> Dict[str, Any]:
    """异步发送短信消息"""
    payload = MessagePayload(
        message_type=MessageType.SMS,
        target_id=phone,
        content={
            "text": text,
            "template_id": template_id
        },
        priority=MessagePriority[priority.upper()],
        **kwargs
    )

    # 直接调用消息总线
    result = message_bus.send_message(payload)

    return {
        "success": result.success,
        "message": result.message,
        "data": result.data,
        "error_code": result.error_code,
        "duration": result.duration,
        "message_id": result.message_id
    }
```

#### 6. 添加API接口

在 `api.py` 中添加便捷的API方法：

```python
def send_sms(self, phone: str, text: str, template_id: Optional[str] = None,
             priority: str = DEFAULT_PRIORITY, async_mode: bool = True) -> Dict[str, Any]:
    """发送短信消息

    Args:
        phone: 手机号
        text: 短信内容
        template_id: 模板ID（可选）
        priority: 优先级 LOW/NORMAL/HIGH/URGENT
        async_mode: 是否异步发送，默认True

    Returns:
        发送结果字典
    """
    return self._send_message(
        message_type=MessageType.SMS,
        target_id=phone,
        content={"text": text, "template_id": template_id},
        priority=priority,
        async_mode=async_mode,
        async_task_func=lambda: send_sms_async.delay(phone, text, template_id, priority),
        message_type_name="短信消息"
    )
```

#### 7. 在 `__init__.py` 中注册处理器

在 `__init__.py` 的初始化函数中添加新处理器的注册：

```python
def _init_message_bus():
    """初始化消息总线，注册所有处理器"""
    try:
        # 注册 TTS 语音处理器
        message_bus.register_handler(
            MessageType.VOICE_TTS,
            HandlerFactory.create_handler(MessageType.VOICE_TTS)
        )

        # 注册 VOX 录音处理器
        message_bus.register_handler(
            MessageType.VOICE_VOX,
            HandlerFactory.create_handler(MessageType.VOICE_VOX)
        )

        # 注册微信模板消息处理器
        message_bus.register_handler(
            MessageType.WECHAT_TEMPLATE,
            HandlerFactory.create_handler(MessageType.WECHAT_TEMPLATE)
        )

        # 注册短信消息处理器（新增）
        message_bus.register_handler(
            MessageType.SMS,
            HandlerFactory.create_handler(MessageType.SMS)
        )

    except Exception as e:
        print(f"[MSGBUS] 初始化失败: {e}")
```

#### 8. 使用新消息类型

```python
from celery_task.msgbus import msgbus_api

# 发送短信消息
result = msgbus_api.send_sms(
    phone="13800138000",
    text="您有新的系统通知，请及时查看",
    template_id="SMS_001",
    priority="HIGH"
)

if result['success']:
    print("短信发送成功")
else:
    print(f"短信发送失败: {result['message']}")
```

### 扩展最佳实践

1. **处理器命名**: 使用 `{MessageType}Handler` 格式，如 `SMSHandler`
2. **错误代码**: 使用 `{MESSAGE_TYPE}_SEND_ERROR` 格式，如 `SMS_SEND_ERROR`
3. **参数验证**: 在 `validate_payload` 中进行完整验证，包括必需字段和格式检查
4. **统一返回**: 使用 `MessageResult` 统一返回格式
5. **异常处理**: 捕获所有可能的异常并返回错误信息
6. **优先级支持**: 在异步任务中正确使用 `MessagePriority[priority.upper()]`
7. **代码复用**: 在API层使用 `_send_message` 方法统一处理逻辑

### 扩展检查清单

添加新消息类型时，请确保完成以下步骤：

- [ ] 在 `core.py` 的 `MessageType` 枚举中添加新类型
- [ ] 在 `handlers.py` 中实现新的处理器类
- [ ] 在 `HandlerFactory` 中注册新处理器
- [ ] 在 `config.py` 中添加相应的错误代码
- [ ] 在 `tasks.py` 中创建异步任务函数
- [ ] 在 `api.py` 中添加便捷的API方法
- [ ] 在 `__init__.py` 中注册新处理器
- [ ] 在 `api.py` 的批量发送方法中添加新消息类型支持

### 项目架构说明

消息总线系统采用简化的架构设计：

- **核心模块** (`core.py`): 定义消息类型、载荷、结果等基础数据结构
- **处理器模块** (`handlers.py`): 实现各种消息类型的具体处理逻辑
- **任务模块** (`tasks.py`): 提供基于Celery的异步任务支持
- **API模块** (`api.py`): 提供统一的消息发送接口
- **配置模块** (`config.py`): 集中管理配置参数和错误代码
- **初始化模块** (`__init__.py`): 负责系统初始化和处理器注册

这种设计确保了系统的可扩展性和维护性，新增消息类型只需要按照标准流程添加相应的组件即可。
