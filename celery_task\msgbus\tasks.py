"""
消息总线Celery任务 - 简化版
提供异步消息发送功能
"""

from typing import Dict, Any, Optional

from celery_task.celery import app
from .config import DEFAULT_PRIORITY
from .core import MessagePayload, MessageType, MessagePriority, message_bus


@app.task
def send_voice_tts_async(phone: str, text: str, play_size: int = 1,
                        priority: str = DEFAULT_PRIORITY, **kwargs) -> Dict[str, Any]:
    """异步发送TTS语音通知"""
    payload = MessagePayload(
        message_type=MessageType.VOICE_TTS,
        target_id=phone,
        content={
            "text": text,
            "play_size": play_size
        },
        priority=MessagePriority[priority.upper()],
        **kwargs
    )

    # 直接调用消息总线
    result = message_bus.send_message(payload)

    return {
        "success": result.success,
        "message": result.message,
        "data": result.data,
        "error_code": result.error_code,
        "duration": result.duration,
        "message_id": result.message_id
    }


@app.task
def send_voice_vox_async(phone: str, file_name: str, play_size: int = 1,
                        priority: str = DEFAULT_PRIORITY, **kwargs) -> Dict[str, Any]:
    """异步发送VOX录音播放"""
    # 直接创建并发送，避免不必要的转换
    payload = MessagePayload(
        message_type=MessageType.VOICE_VOX,
        target_id=phone,
        content={
            "file_name": file_name,
            "play_size": play_size
        },
        priority=MessagePriority[priority.upper()],
        **kwargs
    )

    # 直接调用消息总线，无需转换
    result = message_bus.send_message(payload)

    return {
        "success": result.success,
        "message": result.message,
        "data": result.data,
        "error_code": result.error_code,
        "duration": result.duration,
        "message_id": result.message_id
    }


@app.task
def send_wechat_template_async(open_id: str, template_type: str, data: Dict[str, Any],
                              url: Optional[str] = None, miniprogram: Optional[Dict[str, str]] = None,
                              client_msg_id: Optional[str] = None, priority: str = DEFAULT_PRIORITY,
                              **kwargs) -> Dict[str, Any]:
    """异步发送微信模板消息"""
    # 直接创建并发送，避免不必要的转换
    payload = MessagePayload(
        message_type=MessageType.WECHAT_TEMPLATE,
        target_id=open_id,
        content={
            "template_type": template_type,
            "data": data,
            "url": url,
            "miniprogram": miniprogram,
            "client_msg_id": client_msg_id
        },
        priority=MessagePriority[priority.upper()],
        **kwargs
    )

    # 直接调用消息总线，无需转换
    result = message_bus.send_message(payload)

    return {
        "success": result.success,
        "message": result.message,
        "data": result.data,
        "error_code": result.error_code,
        "duration": result.duration,
        "message_id": result.message_id
    }
    # return send_message_async.delay(payload.to_dict()).get()


# 消息总线初始化在 __init__.py 中完成
