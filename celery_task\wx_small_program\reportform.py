from celery_task.celery import app
from custom.db.execute import db
import json
from collections import defaultdict


def paged_query(sql_base, page, page_size, db_type):
    """分页查询，返回分页数据 + 总数"""
    if page <= 0 or page_size <= 0:
        raise ValueError("page and page_size must be positive integers.")

    offset = (page - 1) * page_size

    # 统计总数
    count_sql = f"SELECT COUNT(*) AS total_count FROM ({sql_base}) alias"

    # 分页
    if db_type == 'mysql':
        paged_sql = f"{sql_base} LIMIT {offset}, {page_size}"
    elif db_type == 'oracle':
        paged_sql = f"{sql_base} OFFSET {offset} ROWS FETCH NEXT {page_size} ROWS ONLY"
    else:
        raise ValueError("Unsupported database type")

    # 执行查询（假设 db 是你的数据库操作函数）
    count_df = db(None, None, count_sql, db_type)
    total_count = int(count_df.iloc[0][count_df.columns[0]])

    total_pages = (total_count + page_size - 1) // page_size

    data_df = db(None, None, paged_sql, db_type)
    records = data_df.to_dict(orient='records')

    return {
        "page": page,
        "page_size": page_size,
        "total_count": total_count,
        "total_pages": total_pages,
        "records": records,
    }


def paged_query_with_dept_filter(sql_base, page, page_size, db_type, allowed_depts):
    """
    分页查询 + 科室权限过滤（过滤“科室名称”列）
    :param sql_base: 查询语句
    :param page: 当前页码
    :param page_size: 每页条数
    :param db_type: 数据库类型
    :param allowed_depts: 允许的科室名称列表（如 ['放射科', '超声科']）
    :return: 分页后的字典结果
    """
    if page <= 0 or page_size <= 0:
        raise ValueError("page and page_size must be positive integers.")

    # 执行 SQL，得到完整 DataFrame
    df = db(None, None, sql_base, db_type)

    # 确保列名中包含“科室名称”
    dept_column = None
    for col in df.columns:
        if col.strip() == '科室名称':  # 支持空格或大小写问题
            dept_column = col
            break
    if not dept_column:
        raise ValueError("SQL 返回结果中未找到 '科室名称' 列，无法进行权限过滤")

    # 按科室名称过滤
    df_filtered = df[df[dept_column].isin(allowed_depts)].drop_duplicates()

    # 计算分页
    total_count = len(df_filtered)
    total_pages = (total_count + page_size - 1) // page_size
    start_index = (page - 1) * page_size
    end_index = start_index + page_size
    page_data = df_filtered.iloc[start_index:end_index]
    records = page_data.to_dict(orient='records')

    return {
        "page": page,
        "page_size": page_size,
        "total_count": total_count,
        "total_pages": total_pages,
        "records": records,
    }


@app.task
def get_report_form_data_by_classification():
    """ 先按 auto_add_objectName 分类，再按 department（质控报表）分类，给出报表结构 """
    sql = """
          SELECT id, auto_add_objectName, auto_add_contentName, department
          FROM yzzxyy.info_sql
          """
    dataframe = db(None, None, sql, 'mysql')

    # 只保留 department == "未分类报表" 的行
    dataframe = dataframe[dataframe['department'] == '未分类报表']

    # 把 "未分类报表" 改成 "统计报表"
    dataframe['department'] = '统计报表'
    result = defaultdict(lambda: defaultdict(list))
    # 生成报表目录结构
    for _, row in dataframe.iterrows():
        object_name = row['auto_add_objectName']
        department = row['department']

        result[object_name][department].append({
            "id": row["id"],
            "auto_add_contentName": row["auto_add_contentName"],
            "department": department
        })

    # 转成普通 dict，然后转成 JSON 字符串
    final_result = dict(result)
    json_str = json.dumps(final_result, ensure_ascii=False, indent=2)
    return json_str


@app.task
def get_specific_report_form(id, begin_date, end_date, page, page_size, unionid):
    """ 获取指定的报表 """
    if id is None or id == '':
        return None

    sql = f"""
        SELECT t.sql_content, t.database_name, t.filter_column
        FROM yzzxyy.info_sql t
        WHERE id = '{id}'  
    """

    dataform = db(None, None, sql, 'mysql')
    true_sql = dataform.iloc[0]['sql_content']
    db_type = dataform.iloc[0]['database_name']
    filter_column = dataform.iloc[0]['filter_column']

    # 把 true_sql 中的 [[0]] 替换成 begin_date，[[1]] 替换成 end_date
    if begin_date is not None and end_date is not None:
        true_sql = true_sql.replace('[[0]]', begin_date)
        true_sql = true_sql.replace('[[1]]', end_date)

    department = db('department', 'yzzxyy.info_sql', {'id': id}, 'mysql')

    result = None
    if department == '未分类报表':
        # 查询当前用户有权限的科室名称
        department_name_list = _get_current_user_dept_name_list(unionid)
        # 进行权限校验，过滤掉无权限的科室数据
        result = paged_query_with_dept_filter(true_sql, page, page_size, db_type, department_name_list)
    else:
        result = paged_query(true_sql, page, page_size, db_type)

    result["filter_column"] = filter_column
    return result


def _get_current_user_dept_name_list(unionid: str):
    """
    通过 unionid 查出工号，再查出权限。
    :param unionid: unionid
    :return: ['零-神经内科', '零-神经内科二区', '零-神经内科一区', '零-神经内科重症监护室']
    """
    """ 依据用户名（工号）获取权限科室名称 """
    df = db(['d_director', 'id'], 'yzzxyy.info_user', {"unionid": unionid}, 'mysql')

    if df.empty:
        return []

    director = df.iloc[0]['d_director']
    username = df.iloc[0]['id']
    director = [] if not director else director.split('|')

    sql = f"""SELECT HNYZ_ZXYY.FUN_GET_DEPT_NAME(d.DEPT_CODE) AS DEPT_NAME, d.DEPT_CODE AS DEPT_ID
                  FROM HNYZ_ZXYY.COM_PRIV_USER p
                           JOIN HNYZ_ZXYY.COM_DEPARTMENT d ON d.DEPT_CODE = p.DEPT_CODE
                  WHERE p.USER_CODE = '{username}'
                    AND d.DEPT_TYPE = 'I'
                  UNION
                  SELECT HNYZ_ZXYY.FUN_GET_DEPT_NAME(DEPT_ID) AS DEPT_NAME, DEPT_ID
                  FROM DAWN.DAWN_ORG_EMPL
                  WHERE EMPL_ID = '{username}' OR DEPT_ID in ('{"', '".join(director)}') """
    df = db(None, None, sql, 'oracle')

    if df.empty:
        return []

    return df['DEPT_NAME'].tolist()
