# 自定义小方法

import base64

from Crypto.Cipher import AES  # pip install pycryptodome
import regex as re  # 内置的re库不支持 不定长度的 零宽负向后行断言


def decrypt(key, enc):  # 解密
    decipher = AES.new(key.encode('utf-8'), AES.MODE_ECB)
    dec = decipher.decrypt(base64.b64decode(enc))
    return dec.strip(b'\0').decode('utf-8')


def remove_first_last_mark(content: str):  # 移除文本首位的符号
    cleaned_content = re.sub(r'(^[；;。.，,])|([；;。.，,]$)', '', content)
    return cleaned_content


def get_hours(times: str, unit: str) -> float:  # 依据时间和单位返回小时数
    if unit == '小时':
        result = float(times)
    elif unit == '天':
        result = float(times) * 24
    elif unit == '周':
        result = float(times) * 24 * 7
    elif unit == '月':
        result = float(times) * 24 * 30
    elif unit == '年':
        result = float(times) * 24 * 365
    else:
        result = 0.0
    return int(round(result))  # 返回小时数，并四舍五入取整
