from datetime import datetime

from dateutil.relativedelta import relativedelta
from celery_task.celery import app
from custom.db.execute import db, db_exist, df_to_sql


@app.task
def start():
    """ 生成质控报表的历史记录 """
    # 获取需要抓取的数据库信息
    db_info = db(['sql_content', 'time_type', 'history_db', 'analysis_index', 'positive_index', 'database_name'],
                 'yzzxyy.info_sql',
                 "history_db IS NOT NULL AND history_db != ''",
                 'mysql')
    for _, row in db_info.iterrows():
        # 检查半年数据完整性
        for month in range(6, 0, -1):
            target_month = _get_time(month)
            if not db_exist(f"{row['time_type']}_隐藏", [target_month[0], target_month[1]],
                            f"yzzxyy.{row['history_db']}", 'mysql'):
                set_new_data(row, target_month)


def set_new_data(row, target_month):
    # # 删除老数据(这段好像无效)
    # sql = f"""DELETE FROM yzzxyy.{row['history_db']}
    #             WHERE {row['time_type']}_隐藏 BETWEEN DATE '{target_month[0]}' AND DATE '{target_month[1]}'"""
    # db(None, None, sql, 'mysql')
    # 抓取新数据
    sql = row['sql_content'].replace('[[0]]', target_month[0]).replace('[[1]]', target_month[1])
    result = db(None, None, sql, row['database_name'])
    df_to_sql(result, f"{row['history_db']}")
    if row['positive_index']:
        # 输出总览
        sql = f"""DELETE FROM yzzxyy.history_entire 
                    WHERE HISTORY_DB = '{row['history_db']}' AND MONTH = '{target_month[0][2:7]}'"""
        db(None, None, sql, 'mysql')

        entire_month = target_month[0][2:7]
        entire_db = row['history_db']

        entire_ratio = result[row['analysis_index']].value_counts(normalize=True).get(row['positive_index'],
                                                                                      0)
        entire_ratio = format(entire_ratio * 100, '.1f')
        sql = (f"INSERT INTO yzzxyy.history_entire (HISTORY_DB, MONTH, RATIO) "
               f"VALUES ('{entire_db}', '{entire_month}', {entire_ratio})")
        db(None, None, sql, 'mysql')
    print(f"{row['history_db']} - {target_month[0][:7]} 抓取完成")


def _get_time(months) -> list:
    """ 获取指定月份前的第一天和最后一天 """
    first_day_of_target_month = (datetime.now() - relativedelta(months=months)).replace(day=1)
    str_first_day_of_target_month = first_day_of_target_month.strftime('%Y-%m-%d')
    str_last_day_of_target_month = (first_day_of_target_month + relativedelta(months=1, days=-1)).strftime('%Y-%m-%d')
    return [str_first_day_of_target_month, str_last_day_of_target_month]
