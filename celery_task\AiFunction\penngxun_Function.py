import json
import pandas as pd
import requests
import re
import threading
import queue
from pandas import DataFrame
from datetime import datetime
from typing import List, Dict

from celery_task.celery import app
from custom.db.execute import db

# 创建线程安全的队列
batch_queue = queue.Queue()
# 创建线程锁
db_lock = threading.Lock()

def check_first_treatment():
    """
    主任务函数：提取所有患者的首次治疗时间并更新数据库
    处理流程：
        1. 查询所有Earliest_Time为空的记录
        2. 使用多线程提取首次治疗时间
        3. 每批完成后立即更新数据库
    """
    try:
        # 步骤1: 查询所有Earliest_Time为空的记录
        sql = """
        SELECT ID, INPATIENT_NO, ITEM_NAME, type
        FROM ai.test_patient_treatments
        WHERE Earliest_Time IS NULL
        """

        # 执行查询
        records_df = db(None, None, sql, 'mysql')  # 假设使用MySQL连接

        if records_df.empty:
            print("未找到Earliest_Time为空的记录")
            return

        print(f"找到{len(records_df)}条需要处理的记录")

        batch_size = 20  # 每批20条记录
        total_rows = len(records_df)

        # 准备批处理数据并放入队列
        for start in range(0, total_rows, batch_size):
            end = min(start + batch_size, total_rows)
            batch_df = records_df.iloc[start:end]
            batch_queue.put(batch_df)

        # 创建并启动工作线程
        num_workers = 10  # 10个工作线程
        threads = []
        for i in range(num_workers):
            t = threading.Thread(target=worker_thread, args=(i + 1,))
            t.daemon = True  # 设置为守护线程
            t.start()
            threads.append(t)

        # 等待所有批处理完成
        batch_queue.join()
        print("所有批次处理完成")

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise

def worker_thread(worker_id):
    """
    工作线程函数：处理队列中的批次数据
    :param worker_id: 工作线程ID
    """
    print(f"工作线程 {worker_id} 启动")
    while True:
        try:
            batch_df = batch_queue.get()
            if batch_df is None:
                break

            print(f"工作线程 {worker_id} 处理 {len(batch_df)} 条记录")

            # 调用AI函数处理当前批次
            batch_result = extract_first_treatment_time(batch_df)

            if batch_result:
                # 使用线程锁确保数据库更新安全
                with db_lock:
                    update_earliest_time(batch_result)
                    print(f"工作线程 {worker_id} 更新了 {len(batch_result)} 条记录")

            # 标记任务完成
            batch_queue.task_done()

        except Exception as e:
            print(f"工作线程 {worker_id} 处理批次时出错: {str(e)}")
            batch_queue.task_done()

def extract_first_treatment_time(records_df: DataFrame) -> List[Dict]:
    """
    使用AI提取首次治疗时间
    :param records_df: 包含患者治疗记录的DataFrame
    :return: 首次治疗时间列表 [{"id": 123, "earliest_time": "2024-01-01 12:00:00"}, ...]
    """
    # 构建AI提示词
    prompt = """
    请根据以下定义判断患者接受的肿瘤治疗项目：
    首次治疗指针对肿瘤开展的手术、放疗、化疗、靶向治疗、内分泌治疗、免疫治疗等，
    不包括为明确诊断或病情而采取的穿刺、活检、检查等诊疗措施。

    任务要求：
    1. 分析每个患者的治疗项目列表（ITEM_NAME字段）
    2. 识别出属于肿瘤治疗的项目（手术、放疗、化疗、靶向治疗、内分泌治疗、免疫治疗等）
    3. 从这些肿瘤治疗项目中找出最早的时间
    4. 如果没有符合条件的治疗项目，返回'1970-01-01 00:00:00'

    治疗项目格式：项目名称(时间)，多个项目用逗号分隔

    输出要求：
    1. 每个患者返回一个最早治疗时间
    2. 使用JSON格式返回
    3. 字段说明:
        id: 记录ID（整数）
        earliest_time: 最早治疗时间（字符串格式：yyyy-MM-dd HH:mm:ss）

    示例输出格式：
    [{"id": 1, "earliest_time": "2024-07-03 15:00:00"}, ...]

    患者治疗记录列表：
    """

    for _, row in records_df.iterrows():
        # 使用ID作为唯一标识
        prompt += f"\n记录ID:{row['ID']}): 癌症类型:{row['type']} 治疗项目:{row['ITEM_NAME']}"

    prompt += """
    请直接给出提取结果：
    """

    # 调用AI服务
    try:
        response = requests.post(
            "http://***************:12434/api/generate",
            json={
                "model": 'qwen3:8b-q4_K_M',
                "prompt": prompt,
                "stream": False,
            }
        )
        response.raise_for_status()
    except requests.exceptions.RequestException as e:
        print(f"AI服务调用失败: {str(e)}")
        # 返回空结果避免阻塞后续处理
        return []

    # 处理AI响应
    ai_output = response.json().get("response", "").strip()
    clean_response = re.sub(r'<think>.*?</think>', '', ai_output, flags=re.DOTALL).strip()
    print(f"AI返回结果: {clean_response[:500]}...")  # 截断长输出
    return parse_ai_response(clean_response)

def parse_ai_response(response_text: str) -> List[Dict]:
    """
    解析AI返回的JSON格式响应
    :param response_text: AI返回的文本
    :return: 结构化患者数据列表
    """
    try:
        # 尝试直接解析为JSON
        data = json.loads(response_text)
        if isinstance(data, list):
            # 验证数据格式
            valid_data = []
            for item in data:
                if "id" in item and "earliest_time" in item:
                    # 验证时间格式
                    try:
                        datetime.strptime(item["earliest_time"], "%Y-%m-%d %H:%M:%S")
                        valid_data.append({
                            "id": int(item["id"]),
                            "earliest_time": item["earliest_time"]
                        })
                    except ValueError:
                        # 无效时间格式，使用默认值
                        valid_data.append({
                            "id": int(item["id"]),
                            "earliest_time": "1970-01-01 00:00:00"
                        })
            return valid_data
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {str(e)}")

    # 如果直接解析失败，尝试手动提取数据
    patients = []
    pattern = r'\{\s*"id"\s*:\s*(\d+)\s*,\s*"earliest_time"\s*:\s*"([\d\-: ]+)"\s*\}'
    matches = re.findall(pattern, response_text)

    for id, time_str in matches:
        try:
            # 验证时间格式
            datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
            patients.append({
                "id": int(id.strip()),
                "earliest_time": time_str
            })
        except ValueError:
            # 无效时间格式，使用默认值
            patients.append({
                "id": int(id.strip()),
                "earliest_time": "1970-01-01 00:00:00"
            })

    return patients

def update_earliest_time(data: List[Dict]):
    """
    更新首次治疗时间到数据库
    :param data: 更新数据列表 [{"id": 123, "earliest_time": "2024-01-01 12:00:00"}, ...]
    """
    for item in data:
        # 确保ID是整数
        item_id = int(item['id'])
        earliest_time = item['earliest_time']

        update_sql = f"""
        UPDATE ai.test_patient_treatments
        SET Earliest_Time = '{earliest_time}'
        WHERE ID = {item_id}
        """
        try:
            result = db(None, None, update_sql, 'mysql')
            if result:
                print(f"成功更新记录 ID={item_id} 的首次治疗时间为 {earliest_time}")
            else:
                print(f"更新记录 ID={item_id} 失败: 数据库操作未返回结果")
        except Exception as e:
            print(f"更新记录 ID={item_id} 失败: {str(e)}")

# 定时任务入口
@app.task
def start_first_treatment_extraction():
    check_first_treatment()

if __name__ == "__main__":
    # 测试执行
    start_first_treatment_extraction()