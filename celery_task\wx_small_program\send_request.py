from celery_task.celery import app
from celery_task.wx_small_program.wxsm_config import APPID, APP_SECRET, GRANT_TYPE
import requests

@app.task
def get_information(code: str):
    """
    获取微信小程序的 OpenID 等信息
    :param code: code
    :return:
    """
    # 带参数的GET请求
    params = {'appid': APPID,
              'secret': APP_SECRET,
              'js_code': code,
              'grant_type': GRANT_TYPE
    }

    response = requests.get('https://api.weixin.qq.com/sns/jscode2session', params=params)
    wechat_data = response.json()

    if 'errcode' in wechat_data and wechat_data['errcode'] is not None:
        return {
            'code': 500,
            'data': wechat_data['errcode'],
            'message': wechat_data.get('errmsg', '，微信接口返回错误')
        }

    res = {
        'code': 200,
        'data': {
            "openid": wechat_data.get('openid'),
            "session_key": wechat_data.get('session_key'),
            "unionid": wechat_data.get('unionid'),
            "errcode": wechat_data.get('errcode'),
            "errmsg": wechat_data.get('errcode')
        },
        'message': 'success'
    }

    return res