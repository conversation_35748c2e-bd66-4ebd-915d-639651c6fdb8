import asyncio

from celery_task.ali.ali_sms import SMS
from custom.db.execute import db
import regex as re
from celery_task.celery import app


@app.task
def send_sms(template_name: str, code: str, **kwargs):
    """
    send_sms('in vivo implants', phone, bed_no=bed_no, patient_name=patient_name,
             patient_no=inpatient_no, time=record_time, type="药物", software_name='OA')
    """
    if not re.match(r"^\d{11}$", code):  # 判断code是工号还是手机号码
        code = query_phone(code)
    if not code:
        return False
    elif template_name == 'in vivo implants':
        template_param = {"bed_no": kwargs.get('bed_no', ''),
                          "patient_name": kwargs.get('patient_name', ''),
                          "patient_no": kwargs.get('patient_no', '')[-8:],
                          "time": kwargs.get('time', '')[:10],
                          "type": kwargs.get('type', ''),
                          "software_name": kwargs.get('software_name', '')}

        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永医预警',
                               'SMS_469065912')).get('code') == 'OK'
    elif template_name == 'GCP':
        # 规则校验
        # phone_number校验
        phone_number = kwargs.get('phone_number', '')
        if not re.compile(r"^\d+|-\d+$").match(phone_number):
            phone_number = '00000'
        # doctor_name校验
        doctor_name = kwargs.get('doctor_name', '')
        doctor_name = '未指定' if not doctor_name else keep_chinese(doctor_name)

        template_param = {"type": kwargs.get('type', ''),
                          "patient_name": kwargs.get('patient_name', ''),
                          "phone_number": phone_number,
                          "registration_time": kwargs.get('registration_time', ''),
                          "department": kwargs.get('department', ''),
                          "doctor_name": doctor_name}
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永医预警',
                               'SMS_473485200')).get('code') == 'OK'
    elif template_name == 'follow_patient':
        '''【永州市中心医院】${patient_name_and_appellation}您好：感谢您选择永州市中心医院就诊。
            ${doctor_name}医生提醒您，按${out_date}医嘱，建议您出院${content}。祝您健康！'''
        patient_name_and_appellation = kwargs.get('patient_name', '') + kwargs.get('appellation', '')
        doctor_name = kwargs.get('doctor_name', '')
        out_date = kwargs.get('out_date', '')
        content = kwargs.get('content', '')
        template_param = {"patient_name_and_appellation": patient_name_and_appellation, "doctor_name": doctor_name,
                          "out_date": out_date, "content": content}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_474915599')).get('code') == 'OK'
    elif template_name == 'follow_doctor':
        '''【永州市中心医院】${doctor_name}您好：
            患者${patient_name_and_inpatient_no}未按时完成随访计划，请及时回访，谢谢！'''
        doctor_name = kwargs.get('doctor_name', '')
        patient_name_and_inpatient_no = f"{kwargs.get('patient_name', '')}【{kwargs.get('inpatient_no', '')}】"
        template_param = {"doctor_name": doctor_name, "patient_name_and_inpatient_no": patient_name_and_inpatient_no}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_474880572')).get('code') == 'OK'
    elif template_name == 'follow_doctor2':
        '''【永州市中心医院】${doctor_name}您好：
            患者${patient_name_and_inpatient_no}超时未回访，请及时回访，谢谢！'''
        doctor_name = kwargs.get('doctor_name', '')
        patient_name_and_inpatient_no = f"{kwargs.get('patient_name', '')}【{kwargs.get('inpatient_no', '')}】"
        template_param = {"doctor_name": doctor_name, "patient_name_and_inpatient_no": patient_name_and_inpatient_no}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_474925555')).get('code') == 'OK'
    elif template_name == 'follow_doctor3':
        '''【永州市中心医院】${doctor_name}您好：
            患者${patient_name_and_inpatient_no}的升级回访任务超时，请及时回访，谢谢！'''
        doctor_name = kwargs.get('doctor_name', '')
        patient_name = kwargs.get('patient_name', '')
        inpatient_no = kwargs.get('inpatient_no', '')
        template_param = {"doctor_name": doctor_name, "patient_name": patient_name, "inpatient_no": inpatient_no}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_474825606')).get('code') == 'OK'
    elif template_name == 'follow_doctor4':
        '''【永州市中心医院】${doctor_name}您好：
            ${house_doctor_name}的患者${patient_name}回访任务严重超时，请督促及时回访，谢谢！'''
        house_doctor_name = kwargs.get('house_doctor_name', '')
        doctor_name = kwargs.get('doctor_name', '')
        patient_name = kwargs.get('patient_name', '')
        template_param = {"doctor_name": doctor_name, "house_doctor_name": house_doctor_name,
                          "patient_name": patient_name}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_475000509')).get('code') == 'OK'
    elif template_name == 'follow_warning':
        '''【永州市中心医院】您好：
            患者${patient_name}已触发您于${time}设置的个性化预警，具体详情请在随访页面中查看，谢谢！'''
        patient_name = kwargs.get('patient_name', '')
        time = kwargs.get('time', '')
        template_param = {"patient_name": patient_name, "time": time}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_474810633')).get('code') == 'OK'
    elif template_name == 'follow_update':
        '''【永州市中心医院】${doctor_name}您好：
            患者${patient_name_and_inpatient_no}的回访任务已被专员回访升级，请您及时主动回访，谢谢！'''
        doctor_name = kwargs.get('doctor_name', '')
        patient_name = kwargs.get('patient_name', '')
        inpatient_no = kwargs.get('inpatient_no', '')
        template_param = {"doctor_name": doctor_name, "patient_name": patient_name, "inpatient_no": inpatient_no}
        # print(template_param)
        return asyncio.run(
            SMS.send_sms_async({'phone_numbers': code, 'template_param': str(template_param)}, '永州市中心医院',
                               'SMS_474825607')).get('code') == 'OK'


def query_phone(code):
    return db('TEL', 'yzzxyy.info_phone', {'CODE': code}, 'mysql')


def keep_chinese(text):
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    chinese_chars = chinese_pattern.findall(text)
    result = ''.join(chinese_chars)
    return result


if __name__ == '__main__':
    # print(query_phone('25078'))
    print(send_sms('GCP', code='15074620622', patient_name='小曼', phone_number='15074620622',
                   registration_time='2024-09-15 20:47', department='急诊内科', doctor_name=None))
