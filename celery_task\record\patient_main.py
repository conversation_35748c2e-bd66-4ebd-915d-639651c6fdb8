from __future__ import annotations

import base64
import logging
from typing import Any, Dict, List, Optional, Union
from urllib.parse import urljoin

import requests
from bs4 import BeautifulSoup

# 常量配置
BASE_HOST = "http://***********:8089/"
MAIN_PAGE = urljoin(BASE_HOST, "PatientMainForm.aspx?hisInpatientID={}")

DEFAULT_HEADERS: Dict[str, str] = {
    "User-Agent": (
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) "
        "AppleWebKit/537.36 (KHTML, like Gecko) "
        "Chrome/********* Safari/537.36"
    ),
    "Accept": (
        "text/html,application/xhtml+xml,application/xml;"
        "q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,"
        "application/signed-exchange;v=b3;q=0.7"
    ),
    "Accept-Language": "zh-CN,zh;q=0.9",
}

# 超时设定（连接, 读取）
TIMEOUT = (5, 20)

# _logger = logging.getLogger(__name__)


# 公共方法
def _build_url(inpatient_no: str) -> str:
    """根据住院号生成主页面 URL。"""
    encoded = base64.b64encode(inpatient_no.encode("utf-8")).decode()
    return MAIN_PAGE.format(encoded)


def _get_html(url: str, cookies: Optional[Dict[str, str]] = None) -> str:
    """发送 GET 请求并返回 HTML 文本。"""
    resp = requests.get(
        url,
        headers=DEFAULT_HEADERS,
        cookies=cookies,
        timeout=TIMEOUT,
        verify=False,  # 内网 HTTP，可关闭 SSL 校验
    )
    resp.raise_for_status()

    # 自动设置正确编码
    try:
        resp.encoding = resp.apparent_encoding or resp.encoding
    except Exception:
        pass

    return resp.text


# 对外核心函数
def fetch_child_links(
        inpatient_no: str | None = None,
        cookies: Optional[Dict[str, str]] = None,
        timeout: int = 10,
        return_structure: str = "hierarchical",  # 新增参数：返回结构类型 "hierarchical" 或 "flat"
        dedupe_by_name: bool = False,  # 保留原有参数以保持兼容性
        **kwargs: Dict,
) -> Union[List[Dict[str, str]], List[Dict[str, Any]]]:
    # 如果外层通过 proxy 传入的 kwargs 中含有 "query"，则尝试从中提取参数
    if inpatient_no is None:
        query_dict: Dict[str, str] = kwargs.get("query", {}) if isinstance(
            kwargs.get("query", {}), dict) else {}
        inpatient_no = query_dict.get("inpatient_no")

    if not inpatient_no:
        raise ValueError("缺少参数 inpatient_no")

    global TIMEOUT
    # 若调用方传入单一 int 形式的 timeout，则覆盖读取超时保持默认连接超时
    if timeout:
        TIMEOUT = (TIMEOUT[0], timeout)

    # 生成主页面 URL
    print(f"[DEBUG] 收到的 inpatient_no: {inpatient_no}")
    print(f"[DEBUG] kwargs: {kwargs}")
    target_url = _build_url(inpatient_no)
    print(f"[DEBUG] 生成的 target_url: {target_url}")
    # _logger.info("Fetch patient main page: %s", target_url)

    # 获取 HTML
    html_text = _get_html(target_url, cookies=cookies)

    # 解析 HTML
    soup = BeautifulSoup(html_text, "html.parser")

    if return_structure == "hierarchical":
        return _extract_hierarchical_structure(soup)
    else:
        return _extract_flat_structure(soup, dedupe_by_name)


def _extract_hierarchical_structure(soup: BeautifulSoup) -> List[Dict[str, Any]]:
    """根据'fetched_page.html'的真实HTML结构提取层级化导航,包括当前和既往病历。"""
    hierarchical_results = []

    # 1. 查找 title="病历导航" 的主容器
    nav_container = soup.find("div", title="病历导航")
    if not nav_container:
        raise ValueError("未能找到 `title='病历导航'` 的主容器。HTML结构可能已变更。")

    # 2. 在导航容器内查找主 accordion
    main_accordion = nav_container.find("div", class_="easyui-accordion")
    if not main_accordion:
        raise ValueError("在导航容器内未能找到主 accordion。")

    # 3. 遍历主 accordion 下的顶级 div ("当前病历" 和 "既往病历")
    top_level_divs = main_accordion.find_all("div", title=True, recursive=False)

    for section_div in top_level_divs:
        section_title = section_div.get("title", "").strip()

        if section_title == "当前病历":
            # --- 解析 "当前病历" ---
            current_records_data: Dict[str, Any] = {"section": section_title, "categories": []}
            inner_accordion = section_div.find("div", class_="easyui-accordion")
            if not inner_accordion:
                continue

            # 分类就是内层 accordion 的直接子 div
            category_divs = inner_accordion.find_all("div", title=True, recursive=False)
            for cat_div in category_divs:
                category_name = cat_div.get("title", "").strip()
                if not category_name:
                    continue

                links = []
                anchors = cat_div.find_all("a", href=True)
                for a in anchors:
                    href = a["href"].strip()
                    text = (a.get_text(strip=True) or a.get("title", "")).strip()

                    if not href or href.startswith("#") or href.lower().startswith("javascript"):
                        continue

                    abs_url = href if href.lower().startswith("http") else urljoin(BASE_HOST, href)
                    links.append({"name": text or abs_url, "url": abs_url})

                if links:
                    current_records_data["categories"].append({
                        "category": category_name,
                        "links": links
                    })

            if current_records_data["categories"]:
                hierarchical_results.append(current_records_data)

        elif section_title == "既往病历":
            # --- 解析 "既往病历" ---
            past_records_data: Dict[str, Any] = {"section": section_title, "visits": []}
            tree = section_div.find("ul", class_="easyui-tree")
            if not tree:
                continue

            # 每个直接 li 都是一个就诊记录
            past_visits = tree.find_all("li", recursive=False)
            for visit_li in past_visits:
                # 分类名是 li 下的第一个 span
                category_span = visit_li.find("span", recursive=False)
                category_name = category_span.get_text(strip=True) if category_span else "未知既往记录"

                links = []
                # 链接在 li > ul > li > a
                record_list_ul = visit_li.find("ul")
                if record_list_ul:
                    record_items = record_list_ul.find_all("a", href=True)
                    for a in record_items:
                        href = a["href"].strip()
                        text = (a.get_text(strip=True) or a.get("title", "")).strip()

                        if not href or href.startswith("#") or href.lower().startswith("javascript"):
                            continue

                        abs_url = href if href.lower().startswith("http") else urljoin(BASE_HOST, href)
                        links.append({"name": text or abs_url, "url": abs_url})

                if links:
                    past_records_data["visits"].append({
                        "visit": category_name,
                        "links": links
                    })

            if past_records_data["visits"]:
                hierarchical_results.append(past_records_data)

    if not hierarchical_results:
        raise ValueError("未能在页面中提取到任何导航结构。")

    return hierarchical_results


def _extract_flat_structure(soup: BeautifulSoup, dedupe_by_name: bool = False) -> List[Dict[str, str]]:
    """提取扁平化的链接结构（保持原有行为）"""
    anchors = soup.find_all("a", href=True)

    results: List[Dict[str, str]] = []
    seen_urls = set()  # URL去重集合
    seen_names = set()  # 名称去重集合

    for a in anchors:
        href: str = a["href"].strip()
        text: str = (a.get_text(strip=True) or a.get("title", "")).strip()
        if not href or href.startswith("#"):
            continue

        # 处理 javascript:void(0) 等无效链接
        if href.lower().startswith("javascript"):
            continue

        # 将相对路径补全为绝对 URL
        abs_url = href if href.lower().startswith("http") else urljoin(BASE_HOST, href)

        # 选择去重策略
        if dedupe_by_name:
            # 按名称去重
            if text and text in seen_names:
                continue
            if text:
                seen_names.add(text)
        else:
            # 按URL去重（原有逻辑）
            if abs_url in seen_urls:
                continue
            seen_urls.add(abs_url)

        results.append({"name": text or abs_url, "url": abs_url})

    # 若未能提取到任何链接，抛出异常供外层捕获
    if not results:
        raise ValueError("未能在页面中提取到任何子链接，可能页面结构已变更或未登录。")

    return results
