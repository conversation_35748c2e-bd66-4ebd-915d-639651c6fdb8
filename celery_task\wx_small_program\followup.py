from celery_task.wx_small_program.followup_common import make_json_safe_html, get_basic_information, \
    get_discharge_information, get_admission_information, transform_db_data_enhanced, normalize_to_list
from celery_task.celery import app
from custom.db.execute import db
import json
from datetime import datetime



@app.task
def get_follow_up_list(unionid: str):
    """ 根据 unionid 获取 待随访列表 """
    sql = f"""SELECT a.INPATIENT_NO INPATIENT_NO,
                   a.ID         ARTIFICIAL_ID,
                   a.PLAN_ID    PLAN_ID,
                   a.CONTENT_ID CONTENT_ID,
                   a.STATE      TASK_STATE,
                   a.HOUSE_DOC_CODE HOUSE_DOC_CODE,
                   a.DEDICATED_CODE DEDICATED_CODE,
                   p.COMPLETE   COMPLETE,
                   p.STATE      STATE,
                   p.NAME       NAME,
                   p.TEL        TEL,
                   c.INFO       INFO,
                   c.COMBO      COMBO
                FROM yzzxyy.follow_task_artificial a
                JOIN yzzxyy.follow_plan p ON p.ID = a.PLAN_ID
                JOIN yzzxyy.info_user i ON i.id = a.DEDICATED_CODE
                JOIN yzzxyy.follow_content c ON c.ID = a.CONTENT_ID
                where a.STATE < 2
                AND i.unionid = '{unionid}'
        """
    dataform = db(None, None, sql, 'mysql')
    return dataform.to_json(orient='records', force_ascii=False)


@app.task
def get_initiate_followup_data(inpatient_no: str):
    """
    根据住院号 inpatient_no 获取某个病人的发起回访所需要的数据
    随访计划、患者基本信息、入院信息、出院信息
    """
    # sql0 根据住院号获取病人基本信息
    sql0 = f'''select f.INPATIENT_NO                               INPATIENT_NO,
                     f.CARD_NO                                    CARD_NO,
                     f.NAME                                       姓名,
                     DECODE(f.SEX_CODE, 'M', '男', '女')          性别,
                     f.CHARGE_DOC_NAME                            主管医生,
                     f.BALANCE_COST                               总花费,
                     h.CERTIFICATE_NO                             身份证,
                     h.PRESENT_PROVINCE                           住址省,
                     h.PRESENT_CITY                               住址市,
                     h.PRESENT_COUNTY                             住址县,
                     h.PRESENT_OTHER                              住址其他,
                     h.PRESENT_TEL                                现住址电话,
                     h.CONTACT_NAME                               联系人,
                     h.RELATIONSHIP_CODE                          联系人关系,
                     h.CONTACT_TEL                                联系人电话,
                     h.IN_DATE                                    入院时间,
                     HNYZ_ZXYY.FUN_GET_DEPT_NAME(h.IN_DEPT_CODE)  入院科室,
                     h.OUT_DATE                                   出院时间,
                     HNYZ_ZXYY.FUN_GET_DEPT_NAME(h.OUT_DEPT_CODE) 出院科室,
                     h.OUT_DEPT_CODE                              出院科室代码,
                     h.IN_DAYS                                    住院天数,
                     DECODE(h.OUT_TYPE, 1, '医嘱离院',
                            2, '医嘱转院',
                            3, '医嘱下转',
                            4, '非医嘱离院',
                            5, '死亡',
                            '其他')                               离院方式,
                     h.RECEIVING_HOS_NAME                         接收机构,
                     h.RECEIVING_COMMUNITY                        接收社区,
                     h.AGE                                        入院年龄
              from HNYZ_ZXYY.FIN_IPR_INMAININFO f
                       LEFT JOIN NMRWS.NMRWS_MR_HOMEPAGE h ON h.INPATIENT_NO = f.INPATIENT_NO
              where f.INPATIENT_NO = '{inpatient_no}'
           '''
    data_basic = db(None, None, sql0, 'oracle').iloc[0]

    # sql1 根据住院号获取病人诊断信息
    sql1 = f"""select DECODE(d.DIAGNOSE_TYPE, 1, '门诊诊断', 2, '入院诊断', 3, '出院诊断', 4, '病历诊断', d.DIAGNOSE_TYPE) 诊断类型,
                          d.IS_MAIN                                                                                    主诊断,
                          d.BEFORECODE || d.ICD_NAME || d.AFTERCODE                                                   诊断名称,
                          d.SORT_NO                                                                                     顺序号
                   from NMRWS.NMRWS_MR_HOMEPAGE_DIAGNOSE d
                            join NMRWS.NMRWS_MR_HOMEPAGE h on h.HOMEPAGE_ID = d.HOMEPAGE_ID
                   where h.INPATIENT_NO = '{inpatient_no}'
                     and DIAGNOSE_CODE_TYPE = '1'
                   order by d.DIAGNOSE_TYPE, d.SORT_NO"""
    homepage_dataform = db(None, None, sql1, 'oracle')

    # sql2 根据住院号获取病人元素信息
    sql2 = f"""select ELEMENT_NAME, VALUE
                   from HIT_MDC.MDC_RCD_IN_RECORD_ITEM
                   where ELEMENT_ID in ('56', '203233', '2048395', '2048396', '369', '2054154')
                     and INPATIENT_NO = '{inpatient_no}'
                   order by CREATE_TIME desc"""
    element_dataform = db(None, None, sql2, 'oracle')

    # sql3 根据住院号获取随访字段
    sql3 = f"""
        SELECT COMPLETE AS 随访计划 FROM
        yzzxyy.follow_plan
        WHERE INPATIENT_NO = '{inpatient_no}'
    """
    follow_plan_dataform = db(None, None, sql3, 'mysql').iloc[0]

    # 随访计划
    follow_plan = follow_plan_dataform['随访计划']
    # 患者基本信息
    patient_basic_info = get_basic_information(data_basic)
    # 入院记录
    admission_information = get_admission_information(homepage_dataform, element_dataform)
    # 出院记录
    discharge_information = get_discharge_information(data_basic, homepage_dataform, element_dataform)

    final_result = json.dumps({
        'followPlan': follow_plan,
        'patientBasicInfo': make_json_safe_html(patient_basic_info),
        'admissionInformation': make_json_safe_html(admission_information),
        'dischargeInformation': make_json_safe_html(discharge_information)
    }, ensure_ascii=False)

    return final_result


@app.task
def get_doctor_patient_phone(artificial_id: str):
    """
    根据随访人工任务 artificial_id 获取随访计划对应的医生和患者的手机号
    """
    sql = f"""
        SELECT fp.TEL AS patientTel, ip.TEL AS doctorTel
        FROM yzzxyy.follow_task_artificial fta
        JOIN yzzxyy.follow_plan fp ON fp.ID = fta.PLAN_ID
        JOIN yzzxyy.info_phone ip ON fta.DEDICATED_CODE = ip.`CODE`
        WHERE fta.ID = '{artificial_id}'
    """

    df = db(None, None, sql, 'mysql')
    records = df.to_dict(orient='records')

    for item in records:
        item['patientTel'] = normalize_to_list(item.get('patientTel'))
        item['doctorTel'] = normalize_to_list(item.get('doctorTel'))

    return records


@app.task
def upgrade_follow_up(artificial_id: str, combo1: str, combo2: str, adds: str, remarks: str):
    """
    回访升级 POST
    """
    # （0未回访1未回访的升级单2已升级本单作废3已取消4已完成）
    try:
        artificial_id = int(artificial_id)
        # 1.修改 plan_id 对应的记录的 state 为2
        sql0 = f"""
               UPDATE yzzxyy.follow_task_artificial fta
               SET fta.STATE = 2
               WHERE fta.ID = {artificial_id}
        """

        db(None, None, sql0, 'mysql')
        # 2.生成新的升级单
        sql = f"""INSERT INTO yzzxyy.follow_task_artificial
        (INPATIENT_NO, HOUSE_DOC_CODE, HOUSE_DOC_NAME,
         DEDICATED_CODE, DEDICATED_NAME,
         PLAN_ID, CONTENT_ID, DEPT_CODE, STATE, COMBO1, COMBO2, REMARK)
            SELECT INPATIENT_NO, HOUSE_DOC_CODE, HOUSE_DOC_NAME,
                   HOUSE_DOC_CODE, HOUSE_DOC_NAME,
                   PLAN_ID, CONTENT_ID, DEPT_CODE, 1,
                   '{combo1}', '{combo2}', '{remarks}'
            FROM yzzxyy.follow_task_artificial
            WHERE ID = {artificial_id} AND STATE = 2
        """
        df = db(None, None, sql, 'mysql')
    except Exception as e:
        return json.dumps({
            'code': 500,
            'message': '升级回访失败',
            'data': str(e)
        }, ensure_ascii=False)

    return json.dumps({
        'code': 200,
        'message': '升级回访成功',
        'data': ''
    }, ensure_ascii=False)


@app.task
def cancel_follow_up(artificial_id: str, combo1: str, combo2: str, adds: str, remarks: str):
    """
        回访取消 POST
    """
    # （0未回访1未回访的升级单2已升级本单作废3已取消4已完成）
    try:
        artificial_id = int(artificial_id)
        sql = f"""UPDATE yzzxyy.follow_task_artificial
                    SET COMBO1  = '{combo1}',
                        COMBO2  = '{combo2}',
                        REMARKS = '{remarks}',
                        STATE = 3,
                        COMPLETE_TIME = '{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}'
                    WHERE ID = {artificial_id}"""

        db(None, None, sql, 'mysql')

    except Exception as e:
        return json.dumps({
            'code': 500,
            'message': '取消回访失败',
            'data': str(e)
        }, ensure_ascii=False)

    return json.dumps({
        'code': 200,
        'message': '取消回访成功',
        'data': ''
    }, ensure_ascii=False)


@app.task
def confirm_follow_up(artificial_id: str, combo1: str, combo2: str, adds: str, remarks: str):
    """
        回访确认 POST
    """
    # （0未回访1未回访的升级单2已升级本单作废3已取消4已完成）
    try:
        artificial_id = int(artificial_id)
        sql = f"""UPDATE yzzxyy.follow_task_artificial
                        SET COMBO1  = '{combo1}',
                            COMBO2  = '{combo2}',
                            REMARKS = '{remarks}',
                            STATE = 4,
                            COMPLETE_TIME = '{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}'
                        WHERE ID = {artificial_id}"""

        db(None, None, sql, 'mysql')

    except Exception as e:
        return json.dumps({
            'code': 500,
            'message': '确认回访失败',
            'data': str(e)
        }, ensure_ascii=False)

    return json.dumps({
        'code': 200,
        'message': '确认回访成功',
        'data': ''
    }, ensure_ascii=False)


@app.task
def get_combo_info(id, artificial_id):
    """
    根据 id，获取对应的 Combo 选项框的内容数据
    id: 1 取消 2 升级 3 确认 4 死亡
    :param id:
    :return:
    """
    sql = f"""
        SELECT INFO AS INFO,
			   COMBO AS COMBO
		FROM yzzxyy.follow_content
		WHERE ID = {id}
    """
    df = db(None, None, sql, 'mysql')
    info = df.iloc[0]['INFO']
    combo = df.iloc[0]['COMBO']
    combo = transform_db_data_enhanced(combo)

    # id 为3，需要查询出 “按随访方案要求：【<<MERGE>>】。系统未检测到患者完成回访方案。请明确患者是否完成上述随访方案。”中的 【<<MERGE>>】 信息
    if id == 3:
        sql1 = f"""
            SELECT fp.COMPLETE
            FROM yzzxyy.follow_task_artificial fta
            JOIN yzzxyy.follow_plan fp ON fp.ID = fta.PLAN_ID
            WHERE fta.ID = {artificial_id}
        """
        inner_df = db(None, None, sql1, 'mysql')
        merge_info = inner_df.iloc[0]['COMPLETE']

        if not merge_info or str(merge_info).strip() == '':
            info = info[info.find('。') + 1:]
        else:
            info = info.replace('【<<MERGE>>】', merge_info)

    return {
        "info": info,
        "combo": combo
    }
