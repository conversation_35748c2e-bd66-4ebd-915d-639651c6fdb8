from celery import Celery
from datetime import timedelta

from celery.schedules import crontab

from custom.db.mysql import UsingMysql

with UsingMysql() as um:
    privacy = um.fetch_decrypt("select value from info_db where index_information = 'redis' ",
                               None, 'value')

config = {
    'broker_url': privacy + '/0',
    'result_backend': privacy + '/1',
    # 'task_compression': 'gzip',  # 开启任务gzip压缩
    'result_compression': 'gzip',  # 开启结果gzip压缩
    'worker_prefetch_multiplier': 1,  # worker每次从队列中预取任务的数量
    'task_acks_late': True,  # 开启消息确认
    'worker_max_tasks_per_child': 100,  # 每个worker最多执行100个任务后销毁
    'worker_max_memory_per_child': 1024 * 1024 * 100,  # 每个worker最多使用100M内存后销毁
    'task_queue_max_priority': 10,  # 任务队列最大优先级
    'task_default_priority': 5,  # 任务默认优先级，priority数值越小，优先级越高。

    'task_acks_on_failure_or_timeout': False,  # 消息由中间件发送给worker时失败或超时后仍然发生确认消息
    'task_reject_on_worker_lost': True,  # worker在任务执行失败后不会发送确认消息
    'result_backend_always_retry': True,  # 尝试重试backend的异常
    'result_backend_max_retries': 10,  # backend的异常重试次数
    'result_backend_thread_safe': True,  # 所有线程共享一个backend连接
    'result_expires': timedelta(hours=1),  # 结果过期时间。时间到后Redis的backend会删除。如果不设置，默认1天删除

    'broker_connection_retry_on_startup': True,  # 启动时重试连接

    # 时区配置
    # 强烈建议启用 UTC，这是 Celery 最稳定和推荐的模式。
    # 当 enable_utc=False 时，eta 和 countdown 等调度功能可能会出现不可预测的行为。
    'enable_utc': True,
    'timezone': 'Asia/Shanghai',  # 时区仍设置为本地时区，用于 Celery Beat 的 crontab 等
    'include': ['celery_task.polling.follow_timeout',
                'celery_task.polling.follow_generate',
                'celery_task.polling.follow_warning',
                'celery_task.polling.follow_sms',
                'celery_task.polling.cmi',
                'celery_task.polling.message_bus',  # 消息分发任务
                # 'celery_task.ali.sms_template',
                # 'celery_task.ali.privacy',
                'celery_task.privacy.bind',
                'celery_task.privacy.crawler',
                'celery_task.WeChat.interface',
                'celery_task.wx_small_program.followup',
                'celery_task.wx_small_program.checkreport',
                'celery_task.wx_small_program.reportform',
                'celery_task.wx_small_program.send_request',
                'celery_task.wx_small_program.medicine',
                'celery_task.wx_small_program.test_request',
                'celery_task.wx_small_program.messageBus',
                'celery_task.wx_small_program.user_info',
                'celery_task.wx_small_program.meeting',
                'celery_task.record.proxy',
                'celery_task.msgbus.tasks',  # 消息总线任务
                'celery_task.AiFunction.patient_tumor_diagnosis',
                'celery_task.AiFunction.sujunqiao_Function',
                ],
}

app = Celery('celery_task')
app.config_from_object(config)

app.conf.beat_schedule = {
    # 随访到期 - 每小时
    'follow_timeout-every-1-hours': {
        'task': 'celery_task.polling.follow_timeout.start',
        'schedule': timedelta(hours=1)
    },
    # AI判断确诊肿瘤患者病历号 - 每两个小时
    'patient_tumor_diagnosis-every-2-hours': {
        'task': 'celery_task.polling.patient_tumor_diagnosis.py.start_tumor_check',
        'schedule': timedelta(hours=2)
    },
    # 检索新出院病人以生成随访 - 每小时（01.01已检查）
    'follow_generate-every-1-day': {
        'task': 'celery_task.polling.follow_generate.start',
        'schedule': timedelta(hours=1),
    },
    # # 每月15日运行报表数据固定
    # 'quality_control_report-monthly': {
    #     'task': 'celery_task.polling.quality_control_report.start',
    #     'schedule': crontab(day_of_month='15', hour='0', minute='0'),
    # },
    # 每天8：30运行随访任务文本和语音提醒
    'quality_control_report-daily': {
        'task': 'celery_task.polling.follow_sms.start',
        'schedule': crontab(hour='8', minute='30'),
    },
    # 每5分钟访问一次通话记录及录音列表
    'privacy_timeout-every-5-minutes': {
        'task': 'celery_task.privacy.crawler.log',
        'schedule': timedelta(minutes=5)
    },
    # 每4分钟检查刷新一次微信的token
    'vx_token_timeout-every-4-minutes': {
        'task': 'celery_task.WeChat.interface.token',
        'schedule': timedelta(minutes=4)
    },
}

# celery -A celery_task worker -l info -P eventlet
# celery -A celery_task worker -l warning

# celery -A celery_task beat -l warning
