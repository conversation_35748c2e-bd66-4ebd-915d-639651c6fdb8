# 涉及小程序的个人信息
import pandas
import requests

from celery_task.wx_small_program.wxsm_config import SERVICE_GRANT_TYPE, SERVICE_APPID, SERVICE_SECRET
from celery_task.wx_small_program.wxsm_utils import get_dept_name
from custom.db.execute import db
from celery_task.celery import app


@app.task
def get_doctor_info_worker(unionid: str):
    # 能用的 openid
    # openid = "o3Ae27VMjCBWFfQpDT4D_xjPtVQs"
    # 工号 work_id
    work_id = db("id", "yzzxyy.info_user", {"unionid": unionid}, "mysql")
    # 查询 sql 语句
    sql = f"""
        SELECT EMPL_NAME                                                                           姓名,
        HNYZ_ZXYY.FUN_GET_DEPT_NAME(DEPT_ID)                                                       科室,
        TEL                                                                                        电话,
        DECODE(EMPL_TYPE, 'D', '医生', 'O', '行政', 'N', '护士', 'P', '药学', 'T', '医技', '其他') 职级,
        EMPL_ID AS                                                                                 工号
        FROM DAWN.DAWN_ORG_EMPL
        WHERE EMPL_ID = '{work_id}'
    """
    df = db(None, None, sql, 'oracle')
    return df.to_dict(orient='records')


@app.task
def is_this_user_a_doctor(unionid: str):
    """
    判断该用户是否是合法用户，检查其是否有工号
    :return 合法：False；不合法：True
    """
    return db("id", "yzzxyy.info_user", {"unionid": unionid}, "mysql") == ''


@app.task
def is_this_user_has_p_quality_control(unionid: str):
    """
    判断该用户是否具有质控报表权限
    :return 有：True；无：False
    """
    res = None
    if db("p_qualityControl", "yzzxyy.info_user", {"unionid": unionid}, "mysql") == 1:
        res = True
    else:
        res = False
    return res


@app.task
def get_union_id_from_open_id_worker():
    """
    遍历 info_user，如果该用户已有 openid，那么，向微信发送请求，得到 unionid，再
    存入 info_user 表中
    :return:
    """
    appid = SERVICE_APPID
    secret = SERVICE_SECRET

    sql = """
          SELECT id, wx_id
          FROM yzzxyy.info_user
          """
    df = db(None, None, sql, 'mysql')
    df = df.dropna(subset=['wx_id'])

    # 发送请求1，为了拿到 access_token
    params = {
        'grant_type': SERVICE_GRANT_TYPE,
        'appid': appid,
        'secret': secret
    }
    response = requests.get('https://api.weixin.qq.com/cgi-bin/token', params=params)
    wechat_data = response.json()
    access_token = wechat_data['access_token']

    # 遍历 df 行
    for _, row in df.iterrows():
        openid = row['wx_id']
        user_id = row['id']  # 确保 df 里有这一列

        # 请求 unionid
        user_info_params = {
            'access_token': access_token,
            'openid': openid,
            'lang': 'zh_CN'
        }

        resp = requests.get('https://api.weixin.qq.com/cgi-bin/user/info', params=user_info_params).json()

        unionid = resp.get('unionid')
        if not unionid:
            print(f"[跳过] 用户 {user_id} 获取 unionid 失败：{resp}")
            continue

        # 构造 SQL 更新
        sql_update = f"""
            UPDATE yzzxyy.info_user
            SET unionid = '{unionid}'
            WHERE id = '{user_id}' and wx_id != NULL
        """
        db(None, None, sql_update, 'mysql')


@app.task
def get_doctor_permission_dept_name_list_worker(unionid: str):
    """
    获取该用户有权限的科室名称列表
    :return:
    """
    # 1.获取工号
    # 工号 work_id
    work_id = db("id", "yzzxyy.info_user", {"unionid": unionid}, "mysql")
    if work_id == '':
        return []
    # 2.获取权限科室名称
    res_df = get_dept_name(work_id)
    if res_df.empty:
        return []
    dept_name_list = res_df['DEPT_NAME'].tolist()
    filtered_list = [name for name in dept_name_list if '老' not in name and '废弃' not in name]
    # 3.返回
    return filtered_list

