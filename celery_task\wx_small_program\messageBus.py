import json
from datetime import datetime, timedelta

from celery_task.celery import app
from custom.db.execute import db


# 消息总线

def _seconds_diff_from_now(time_str: str, time_format: str = "%Y-%m-%d %H:%M:%S"):
    """
    计算指定时间字符串与当前时间的相差秒数（返回整数）

    :param time_str: 时间字符串（如 "2025-07-02 18:55:00"）
    :param time_format: 时间格式（默认 "%Y-%m-%d %H:%M:%S"）
    :return: 相差秒数（int，未来为正，过去为负）
    """
    if not time_str:
        return None
    target_time = datetime.strptime(time_str, time_format)
    delta = target_time - datetime.now()
    return int(delta.total_seconds())  # 强制转换为整数


@app.task
def send_worker(task_type, wxsm_msg_type, optional_content, urgency_level, scheduled_time, template_id,
                template_params, push_mode, target_id):
    """
    接收消息，准备写入消息任务message_task表
    :param task_type: 0：微信小程序消息；1：微信服务号消息；2：TTS消息，3：录音消息
    :param wxsm_msg_type: 如果是微信小程序，指定此字段类型。0：会议消息；1：任务消息
    :param optional_content: 消息具体内容。如果 task_type 为 2 或 3，则需要设置此字段。
            TTS 消息填具体的文本，录音消息需要填写录音文件的文件名。
            如果 task_type 为 0 或 1，不需要填此字段，因为消息的具体内容是由模板拼接参数形成的。
    :param urgency_level: 0：即时发送；1：定时发送
    :param scheduled_time: 定时发送时间。如果 urgency_level 为 1，则需要设置此字段；否则设置为 null
    :param template_id: 模板 ID，此次消息推送所使用的模板对应的ID
    :param template_params: 填充模板需要的参数.
    :param push_mode: 0：定点（按工号）推送；1：分组（按标签）推送；2：按电话号码推送
    :param target_id: 发送消息的目标。push_mode 为 0，则 target_id 为医生工号；push_mode 为 1，
        则 target_id 为标签 ID；push_mode 为 2，则 target_id 为电话号码；用 | 分割多个 target_id。
    :return:
    """
    if urgency_level == 1:
        if scheduled_time is None:
            return "定时任务需要设置定时发送时间"

    countdown = None
    if urgency_level == 1:
        # 定时消息，设置 countdown
        countdown = _seconds_diff_from_now(scheduled_time)

    # 如果是微信的服务号、小程序模板消息
    template_type = None
    if task_type in [0, 1]:
        # 模板的类型
        template_type = _get_template_type_from_db(template_id)
        if not template_type:
            return {"status": "error", "message": f"未找到模板类型 {template_id}"}

        original_data = {
            "template_params": template_params
        }

        optional_content = {k: {"value": v} for k, v in original_data["template_params"].items()}

    if wxsm_msg_type is None:
        # 微信小程序模板消息
        wxsm_msg_type = -1


    from celery_task.polling.message_bus import send_message
    res = send_message(task_type=task_type, optional_content=optional_content, urgency_level=urgency_level,
                       template_params=template_params,
                       push_mode=push_mode, target_id=target_id, scheduled_time_str=scheduled_time,
                       template_type=template_type,
                       file_name=optional_content, countdown=countdown, template_id=template_id, wxsm_msg_type=wxsm_msg_type)

    # 写入日志库
    if res["status"] == "success":
        # 发送成功，写入日志到数据库
        if task_type == 0:
            # 查一下 message_template 中的 template_content
            template_content = db('content', "yzzxyy.message_template", {'template_id': template_id}, 'mysql')
            optional_content = _fill_template(template_content=template_content, data=optional_content)
        write_into_message_log(push_mode, template_id, optional_content, target_id, task_type, urgency_level, res)

    return res

def _fill_template(template_content: str, data: dict) -> str:
    """
    将模板字符串中的占位符，如 {name}、{time}，替换为 data 字典中对应的 value 值。
    :param template_content: 字符串模板，如 "您好，{name}，您于{time}有会议。"
    :param data: 形如 {'name': {'value': '陈医生'}} 的嵌套字典
    :return: 替换后的完整字符串
    """
    for key, val in data.items():
        placeholder = f"{{{key}}}"
        template_content = template_content.replace(placeholder, val.get("value", ""))
    return template_content


@app.task
def get_my_messages(unionid: str):
    """
    获取本医生的微信小程序消息
    :return:
    """
    # openid = "o3Ae27VMjCBWFfQpDT4D_xjPtVQs"

    sql = f"""
        SELECT iu.id as doctor_id FROM yzzxyy.info_user iu
            WHERE iu.unionid = '{unionid}'
    """

    df = db(None, None, sql, 'mysql')
    if df.empty:
        return []
    work_id = df.iloc[0]['doctor_id']

    sql = f"""
        SELECT wm.wxsm_msg_id as 消息ID, wm.wxsm_msg_type as 消息类型, wm.content as 消息内容, wm.send_time 消息发送时间, 
        mt.content as 含占位符的模板内容, mt.placeholders as 模板参数, mt.params_explain as 模板参数解释, wm.is_read as 是否已读,
        wm.expire_time as 消息过期时间
        FROM yzzxyy.wxsm_msg wm 
        JOIN yzzxyy.message_template mt ON wm.template_id = mt.template_id
        WHERE wm.target_id = {work_id}
    """

    fr = db(None, None, sql, 'mysql')
    records = fr.to_dict(orient='records')

    # 将模板参数解释字段反序列化为 dict（结构化）
    for r in records:
        try:
            if isinstance(r.get("模板参数解释"), str):
                r["模板参数解释"] = json.loads(r["模板参数解释"])
        except Exception as e:
            r["模板参数解释"] = {"error": "解析失败", "raw": r["模板参数解释"]}

    return records

@app.task
def switch_message_read_status_worker(msg_id_list: str):
    """
    将小程序消息设置为已读
    msg_id_list: "23|43|44"
    :return:
    """
    try:
        # 将 msg_id_list 转为 "23, 43, 44" 的字符串
        in_clause = ', '.join(msg_id_list.split('|'))

        sql = f"""
                UPDATE yzzxyy.wxsm_msg
                SET is_read = 1
                WHERE wxsm_msg_id IN ({in_clause}) AND is_read = 0
            """

        db(None, None, sql, 'mysql')

        return "更新成功"
    except Exception as e:
        return f"更新失败: {str(e)}"


def _get_template_type_from_db(template_id: int):
    """查数据库获取模板类型，用于发送微信小程序、服务号模板消息"""
    template_df = db(['template_type'], 'yzzxyy.message_template', {'template_id': template_id}, 'mysql')
    return template_df.iloc[0]['template_type'] if not template_df.empty else None


def _generate_message_log_sql(push_mode, template_id, optional_content, receiver_id_list, task_type, urgency_level,
                              send_result,
                              is_read=0, expire_time=None):
    """
    :param push_mode: 0：定点推送；1：分组（按标签）推送；2：按电话号码推送
    :param optional_content: 消息具体内容。如果 task_type 为 2 或 3，则需要设置此字段。
            TTS 消息填具体的文本，录音消息需要填写录音文件的文件名。
            如果 task_type 为 0 或 1，不需要填此字段，因为消息的具体内容是由模板拼接参数形成的。
    :param receiver_id_list: 一个 ID 列表
    :param task_type: 0：微信小程序消息；1：微信服务号消息；2：TTS消息，3：录音消息
    :param urgency_level: 0：即时发送；1：定时发送
    :param send_result: 实际发送消息的结果
    :param is_read: 是否已读，默认为0-未读
    :param expire_time: 过期时间，默认为发送时间后7天。
    :return:
    """
    init_sql = f"""
            INSERT INTO yzzxyy.message_log
            (push_mode, template_id, receiver_id, content, result_code, result_msg, task_type, urgency_level, is_read, expire_time)
            VALUES 
    """

    result_code = send_result.get("status", "error")
    result_message = send_result.get("message", "发送错误")

    # 拼接 sql 语句
    now = datetime.now()
    expire_time = now + timedelta(days=7)
    expire_time_str = expire_time.strftime('%Y-%m-%d %H:%M:%S')  # 去掉微秒部分

    target_id_list = [item.strip() for item in receiver_id_list.strip().strip('|').split('|') if item.strip()]
    for target_id in target_id_list:
        sql = f" ({push_mode}, {template_id}, '{target_id}', '{optional_content}', '{result_code}', '{result_message}', {task_type}, {urgency_level}, 0, '{expire_time_str}'),"
        init_sql += sql

    # 去除多余的逗号并返回
    return init_sql[:-1]


def delete_log_records():
    """
    这个方法应由轮询任务触发，遍历数据库，如果发现有记录到达了过期时间，就删除这条记录
    :return:
    """
    current_time = datetime.now()
    sql = f"""
        DELETE FROM yzzxyy.message_log
        WHERE expire_time < '{current_time}'
    """
    db(None, None, sql, 'mysql')

    return "删除log记录成功"


def write_into_message_log(push_mode, template_id, optional_content, receiver_id_list, task_type, urgency_level, send_result,
                           is_read=0,
                           expire_time=None,
                           ):
    """
    这个方法将发送的消息写入 message_log 数据库
    :return:
    """
    sql = _generate_message_log_sql(push_mode, template_id, optional_content, receiver_id_list, task_type, urgency_level,
                                    send_result, is_read, expire_time)
    db(None, None, sql, 'mysql')

    return "success"
