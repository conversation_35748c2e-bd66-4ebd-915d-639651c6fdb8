"""
消息总线API接口 - 改进版
提供简化的消息发送接口，优化了转换逻辑
"""

from typing import Dict, Any, Optional, List

from .config import DEFAULT_PRIORITY, DEFAULT_PLAY_SIZE, ERROR_CODES
from .core import MessagePayload, MessageType, MessagePriority, message_bus
from .tasks import send_voice_tts_async, send_voice_vox_async, send_wechat_template_async


class MessageBusAPI:
    """消息总线API - 改进版"""

    def _get_priority_value(self, priority: str) -> int:
        """将字符串优先级转换为数字优先级"""
        return MessagePriority[priority.upper()].value

    def _format_result(self, result) -> Dict[str, Any]:
        """统一格式化结果"""
        return {
            "success": result.success,
            "message": result.message,
            "data": result.data,
            "error_code": result.error_code,
            "duration": result.duration,
            "message_id": result.message_id
        }

    def _create_payload(self, message_type: MessageType, target_id: str,
                       content: Dict[str, Any], priority: str) -> MessagePayload:
        """创建消息载荷"""
        return MessagePayload(
            message_type=message_type,
            target_id=target_id,
            content=content,
            priority=MessagePriority[priority.upper()]
        )

    def _format_async_result(self, async_result, message_type: str, extra_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """统一格式化异步任务提交结果"""
        data = {
            "task_id": async_result.id,
            "status": "submitted"
        }
        if extra_data:
            data.update(extra_data)

        return {
            "success": True,
            "message": f"{message_type}任务已提交",
            "data": data,
            "error_code": None,
            "duration": 0,  # 提交耗时几乎为0
            "message_id": None  # 异步模式下暂时没有message_id
        }

    def _send_message(self, message_type: MessageType, target_id: str, content: Dict[str, Any],
                     priority: str, async_mode: bool, async_task_func, message_type_name: str,
                     extra_async_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """统一的消息发送逻辑"""
        if async_mode:
            # 异步模式：提交任务后立即返回
            async_result = async_task_func()
            return self._format_async_result(async_result, message_type_name, extra_async_data)
        else:
            # 同步模式：直接调用消息总线
            payload = self._create_payload(message_type, target_id, content, priority)
            result = message_bus.send_message(payload)
            return self._format_result(result)
    
    def send_voice_tts(self, phone: str, text: str, play_size: int = DEFAULT_PLAY_SIZE,
                      priority: str = DEFAULT_PRIORITY, async_mode: bool = True) -> Dict[str, Any]:
        """发送TTS语音通知

        Args:
            phone: 手机号
            text: 语音文本内容
            play_size: 播放次数，默认1次
            priority: 优先级 LOW/NORMAL/HIGH/URGENT
            async_mode: 是否异步发送，默认True

        Returns:
            发送结果字典
            - async_mode=True: 立即返回任务提交状态，不等待执行结果
            - async_mode=False: 等待执行完成，返回实际执行结果
        """
        return self._send_message(
            message_type=MessageType.VOICE_TTS,
            target_id=phone,
            content={"text": text, "play_size": play_size},
            priority=priority,
            async_mode=async_mode,
            async_task_func=lambda: send_voice_tts_async.apply_async(
                args=(phone, text, play_size, priority),
                priority=self._get_priority_value(priority)
            ),
            message_type_name="TTS语音通知",
            extra_async_data={
                "phone": phone,
                "text": text
            }
        )
    
    def send_voice_vox(self, phone: str, file_name: str, play_size: int = DEFAULT_PLAY_SIZE,
                      priority: str = DEFAULT_PRIORITY, async_mode: bool = True) -> Dict[str, Any]:
        """发送VOX录音播放

        Args:
            phone: 手机号
            file_name: 录音文件名
            play_size: 播放次数，默认1次
            priority: 优先级 LOW/NORMAL/HIGH/URGENT
            async_mode: 是否异步发送，默认True

        Returns:
            发送结果字典
            - async_mode=True: 立即返回任务提交状态，不等待执行结果
            - async_mode=False: 等待执行完成，返回实际执行结果
        """
        return self._send_message(
            message_type=MessageType.VOICE_VOX,
            target_id=phone,
            content={"file_name": file_name, "play_size": play_size},
            priority=priority,
            async_mode=async_mode,
            async_task_func=lambda: send_voice_vox_async.apply_async(
                args=(phone, file_name, play_size, priority),
                priority=self._get_priority_value(priority)
            ),
            message_type_name="VOX录音播放",
            extra_async_data={
                "phone": phone,
                "file_name": file_name
            }
        )
    
    def send_wechat_template(self, open_id: str, template_type: str, data: Dict[str, Any],
                           url: Optional[str] = None, miniprogram: Optional[Dict[str, str]] = None,
                           client_msg_id: Optional[str] = None, priority: str = DEFAULT_PRIORITY,
                           async_mode: bool = True) -> Dict[str, Any]:
        """发送微信模板消息

        Args:
            open_id: 微信用户openid
            template_type: 模板类型 generate/timeout/timeout_chief/unbind
            data: 模板数据
            url: 跳转URL（可选）
            miniprogram: 小程序跳转配置（可选）
            client_msg_id: 防重ID（可选）
            priority: 优先级 LOW/NORMAL/HIGH/URGENT
            async_mode: 是否异步发送，默认True

        Returns:
            发送结果字典
            - async_mode=True: 立即返回任务提交状态，不等待执行结果
            - async_mode=False: 等待执行完成，返回实际执行结果
        """
        return self._send_message(
            message_type=MessageType.WECHAT_TEMPLATE,
            target_id=open_id,
            content={
                "template_type": template_type,
                "data": data,
                "url": url,
                "miniprogram": miniprogram,
                "client_msg_id": client_msg_id
            },
            priority=priority,
            async_mode=async_mode,
            async_task_func=lambda: send_wechat_template_async.apply_async(
                args=(open_id, template_type, data, url, miniprogram, client_msg_id, priority),
                priority=self._get_priority_value(priority)
            ),
            message_type_name="微信模板消息",
            extra_async_data={
                "open_id": open_id,
                "template_type": template_type
            }
        )

    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """查询异步任务状态

        Args:
            task_id: 任务ID（从异步调用返回的task_id）

        Returns:
            任务状态字典
        """
        from celery.result import AsyncResult

        try:
            async_result = AsyncResult(task_id)

            if async_result.ready():
                # 任务已完成
                if async_result.successful():
                    result = async_result.result
                    return {
                        "success": True,
                        "message": "任务执行成功",
                        "data": {
                            "task_id": task_id,
                            "status": "completed",
                            "result": result
                        },
                        "error_code": None,
                        "duration": None,
                        "message_id": result.get('message_id') if isinstance(result, dict) else None
                    }
                else:
                    # 任务失败
                    return {
                        "success": False,
                        "message": f"任务执行失败: {str(async_result.info)}",
                        "data": {
                            "task_id": task_id,
                            "status": "failed",
                            "error": str(async_result.info)
                        },
                        "error_code": "TASK_FAILED",
                        "duration": None,
                        "message_id": None
                    }
            else:
                # 任务还在执行中
                return {
                    "success": True,
                    "message": "任务执行中",
                    "data": {
                        "task_id": task_id,
                        "status": "pending",
                        "state": async_result.state
                    },
                    "error_code": None,
                    "duration": None,
                    "message_id": None
                }

        except Exception as e:
            return {
                "success": False,
                "message": f"查询任务状态失败: {str(e)}",
                "data": {
                    "task_id": task_id,
                    "status": "unknown"
                },
                "error_code": "QUERY_FAILED",
                "duration": None,
                "message_id": None
            }

    def send_batch_messages(self, messages: List[Dict[str, Any]],
                          async_mode: bool = True) -> List[Dict[str, Any]]:
        """批量发送消息

        Args:
            messages: 消息列表，每个消息包含type和相应参数
            async_mode: 是否异步发送，默认True

        Returns:
            发送结果列表
        """
        # 使用映射表替代 if-elif 链，更优雅和易扩展
        message_handlers = {
            'voice_tts': lambda msg: self.send_voice_tts(
                phone=msg['phone'],
                text=msg['text'],
                play_size=msg.get('play_size', DEFAULT_PLAY_SIZE),
                priority=msg.get('priority', DEFAULT_PRIORITY),
                async_mode=async_mode
            ),
            'voice_vox': lambda msg: self.send_voice_vox(
                phone=msg['phone'],
                file_name=msg['file_name'],
                play_size=msg.get('play_size', DEFAULT_PLAY_SIZE),
                priority=msg.get('priority', DEFAULT_PRIORITY),
                async_mode=async_mode
            ),
            'wechat_template': lambda msg: self.send_wechat_template(
                open_id=msg['open_id'],
                template_type=msg['template_type'],
                data=msg['data'],
                url=msg.get('url'),
                miniprogram=msg.get('miniprogram'),
                client_msg_id=msg.get('client_msg_id'),
                priority=msg.get('priority', DEFAULT_PRIORITY),
                async_mode=async_mode
            )
        }

        results = []
        for msg in messages:
            msg_type = msg.get('type')
            handler = message_handlers.get(msg_type)

            if handler:
                try:
                    result = handler(msg)
                except Exception as e:
                    result = {
                        "success": False,
                        "message": f"发送消息失败: {str(e)}",
                        "error_code": ERROR_CODES.get('SEND_ERROR', 'UNKNOWN_ERROR')
                    }
            else:
                result = {
                    "success": False,
                    "message": f"不支持的消息类型: {msg_type}",
                    "error_code": ERROR_CODES.get('UNSUPPORTED_TYPE', 'UNSUPPORTED_TYPE')
                }

            results.append(result)

        return results


# 全局API实例
msgbus_api = MessageBusAPI()
