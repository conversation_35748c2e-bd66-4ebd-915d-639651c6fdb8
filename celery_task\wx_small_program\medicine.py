# 药品相关
from celery_task.celery import app
from custom.db.execute import db



def paged_query(sql_base, page, page_size, db_type):
    """分页查询，返回分页数据 + 总数"""
    if page <= 0 or page_size <= 0:
        raise ValueError("page and page_size must be positive integers.")

    # 先查数据，根据
    count_df = db(None, None, sql_base, db_type)

    start_index = (page - 1) * page_size
    end_index = start_index + page_size

    total_count = len(count_df)
    total_pages = (total_count + page_size - 1) // page_size

    page_data = count_df.iloc[start_index:end_index]
    records = page_data.to_dict(orient='records')

    return {
        "page": page,
        "page_size": page_size,
        "total_count": total_count,
        "total_pages": total_pages,
        "records": records,
    }


def generate_where_sql(medicine_name, insurance, stock, pharmacy, district_options):
    """
    生成 where 子句的 sql
    :param medicine_name: 药品名称
    :param insurance: 是否可医保
    :param stock: 是否有库存
    :param pharmacy: 指定药房
    :param district_options: 指定院区
    :return:
    """
    conditions = []

    if medicine_name and medicine_name.strip():
        conditions.append(f"d.TRADE_NAME LIKE '%{medicine_name}%'")

    if insurance and insurance.strip():
        if insurance == 'insured':
            conditions.append("d.INSTRUCTIONS != '医保等级：'")
        elif insurance == 'uninsured':
            conditions.append("d.INSTRUCTIONS = '医保等级：'")

    if stock and stock.strip():
        if stock == 'outOfStock':
            conditions.append("TRUNC(p.STORE_SUM / p.PACK_QTY) < 1")
        elif stock == 'inStock':
            conditions.append("TRUNC(p.STORE_SUM / p.PACK_QTY) >= 1")

    if pharmacy and pharmacy.strip():
        conditions.append(f"HNYZ_ZXYY.FUN_GET_DEPT_NAME(p.drug_dept_code) = '{pharmacy}'")

    if district_options and district_options.strip():
        conditions.append(f"HNYZ_ZXYY.FUN_GET_DEPT_NAME(p.drug_dept_code) LIKE '%{district_options}%'")

    # 拼接 WHERE 子句
    if conditions:
        sql = "WHERE " + " AND ".join(conditions)
    else:
        sql = ""

    return sql


@app.task
def get_medicine_info(medicine_name, insurance, stock, pharmacy, page, page_size, district_options):
    """
    根据药品名，查询药品的信息
    :param medicine_name:
    :return:
    """
    where_sql = generate_where_sql(medicine_name, insurance, stock, pharmacy, district_options)

    sql = f"""
          SELECT d.TRADE_NAME                                  商品名称,
           TRUNC(p.STORE_SUM / p.PACK_QTY)                库存,
           d.SPECS                                       规格,
           ROUND(d.PRICE_REF, 2)                         参考价格,
           HNYZ_ZXYY.FUN_GET_DEPT_NAME(p.drug_dept_code) 药房,
           d.PRODUCER_NAME                               厂商,
           d.INSTRUCTIONS                                医保比率
        FROM HNYZ_ZXYY.PHA_COM_STOCKINFO p
             JOIN HIS_FSHV1.V_ORDT_DRUGTERM d ON d.drugterm_id = p.drug_code
        {where_sql}
        AND p.VALID_STATE = '1'
        AND HNYZ_ZXYY.FUN_GET_DEPT_NAME(p.drug_dept_code) NOT LIKE '%库%'
        ORDER BY d.TRADE_NAME
    """

    result = paged_query(sql, page, page_size, 'oracle')
    return result

@app.task
def get_all_pharmacies_info():
    """
    获取所有药房
    :return:
    """
    sql = f"""
        SELECT DISTINCT HNYZ_ZXYY.FUN_GET_DEPT_NAME(p.drug_dept_code) AS pharmacies
        FROM HNYZ_ZXYY.PHA_COM_STOCKINFO p
        JOIN HIS_FSHV1.V_ORDT_DRUGTERM d ON d.drugterm_id = p.drug_code
        WHERE p.VALID_STATE = '1' AND HNYZ_ZXYY.FUN_GET_DEPT_NAME(p.drug_dept_code) NOT LIKE '%库%'
        AND TRUNC(p.STORE_SUM / p.PACK_QTY) > 0
        ORDER BY pharmacies
    """

    dataframe = db(None, None, sql, 'oracle')
    pharmacies_list = dataframe['PHARMACIES'].tolist()
    return pharmacies_list
