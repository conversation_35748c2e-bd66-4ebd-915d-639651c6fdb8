'''
Author: xun <EMAIL>
Date: 2025-07-29 09:08:42
LastEditors: xun <EMAIL>
LastEditTime: 2025-07-29 17:40:29
FilePath: \remote celery worker\celery_task\polling\follow_generate.py
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
'''
import ast
import pandas
from celery_task.celery import app
from custom.db.execute import db
from datetime import datetime, timedelta
from .method import get_follow_up_plan, insert_follow_up_db


@app.task
def start(test_model=False):
    """
    处理逻辑：
        1、更新最新的查询时间：数据库中查询上次查询时间 + 写入本次查询时间
        2、获取最新查询时间以后的新出院患者信息 + 诊断证明书没有提交的患者的患者信息
        3、把上面患者的诊断证明书更新时间写入数据库中（出院超过15天的取消监测）
    """
    # Step1：基础数据获取
    start_time = db('value', 'yzzxyy.info_db', {'index_information': 'follow_generate_time'}, 'mysql')
    end_time = str(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

    # Step2：获取需要查询的患者信息
    entire = db('VALUE', 'yzzxyy.info_setting', {'NAME': "FOLLOW_ENTIRE"}, 'mysql') == '1'# ==‘1’是将查询结果转化为布尔值
    sql_add = ''
    if not entire:
        dept_code_list = ast.literal_eval(db('VALUE', 'yzzxyy.info_setting',
                            {'NAME': "FOLLOW_DEPT_CODE"}, 'mysql'))
        dept_code_str = "', '".join(dept_code_list)
        sql_add = '' if dept_code_str else f"AND DEPT_CODE IN ('{dept_code_str}')"

    data_information = db(
        ['NAME', 'INPATIENT_NO', 'SEX_CODE', 'IDENNO', 'HOME_TEL', 'LINKMAN_TEL', 'OUT_DATE', 'DEPT_CODE'],
        'HNYZ_ZXYY.FIN_IPR_INMAININFO',
        f'''OUT_DATE BETWEEN TO_DATE('{start_time}', 'YYYY-MM-DD HH24:MI:SS')
                    AND TO_DATE('{end_time}', 'YYYY-MM-DD HH24:MI:SS') {sql_add}''',
        'oracle')

    # 追加查询：【随访方案-诊断证明提交时间】表中UPDATE_TIME是空的数据，这里是为了持续朱总诊断证明没有提交的患者信息
    empty_update = db(['INPATIENT_NO'], 'follow.plan_prove',
                      {'IS_SUBMITTED': '未提交'}, 'mysql')
    if not empty_update.empty:
        inpatient_nos = empty_update['INPATIENT_NO'].tolist()
        sub_lists = [inpatient_nos[i:i + 900] for i in range(0, len(inpatient_nos), 900)]
        data_information_add = db(
            ['NAME', 'INPATIENT_NO', 'SEX_CODE', 'IDENNO', 'HOME_TEL', 'LINKMAN_TEL', 'OUT_DATE', 'DEPT_CODE'],
            'HNYZ_ZXYY.FIN_IPR_INMAININFO',
            " OR ".join([f"""INPATIENT_NO in ('{"', '".join(sub)}')""" for sub in sub_lists]),
            'oracle')
        data_information = pandas.concat([data_information, data_information_add], ignore_index=True)

    # Step3：更新【随访方案-诊断证明提交时间】表，data_information是从上次生成到现在这段时间内新出院的患者加上之前那段时间内没有提交诊断证明的患者
    if data_information.empty:
        return

    inpatient_nos = data_information['INPATIENT_NO'].tolist()
    sub_lists = [inpatient_nos[i:i + 900] for i in range(0, len(inpatient_nos), 900)]
    where_clause = " OR ".join([f"""s.INPATIENT_NO in ('{"', '".join(sub)}')""" for sub in sub_lists])
    sql = f'''SELECT s.INPATIENT_NO,
                   (SELECT MAX(r.UPDATE_TIME)
                    FROM HIT_MDC.MDC_RCD_IN_RECORD r
                    WHERE r.INPATIENT_RECORD_SET_ID = s.ID
                      AND RECORD_CHILD_TYPE = 'Out_Record'
                      AND INPUT_TPL_ID IN ('33554', '34110', '34498')) UPDATE_TIME
                FROM HIT_MDC.MDC_RCD_IN_RECORD_SET s
                WHERE {where_clause}'''
    data_time = db(None, None, sql, 'oracle')
    data_information = data_information.merge(data_time, on='INPATIENT_NO', how='left')

    # 处理出院超过15天但没有提交诊断证明的患者，将诊断证明书的提交时间设为出院时间，来终止对这类患者的持续监测
    mask = (data_information['UPDATE_TIME'].isnull() &
            ((datetime.now() - data_information['OUT_DATE']) > timedelta(days=15)))
    data_information.loc[mask, 'UPDATE_TIME'] = data_information.loc[mask, 'OUT_DATE']

    # 移除默认日期填充，保留类型推断
    with pandas.option_context("future.no_silent_downcasting", True):
        data_information['UPDATE_TIME'] = data_information['UPDATE_TIME'].infer_objects(copy=False)

    if not test_model:
        # 设置提交状态：UPDATE_TIME不为空则为已提交，否则未提交
        data_information['IS_SUBMITTED'] = data_information['UPDATE_TIME'].apply(
            lambda x: '已提交' if pandas.notnull(x) else '未提交')

        sql = f"""INSERT INTO follow.plan_prove (INPATIENT_NO, UPDATE_TIME, OUT_DATE, IS_SUBMITTED) VALUES
                    {data_information.apply(lambda x: 
                        "('{inpatient_no}', {update_time}, '{out_date}', '{is_submitted}')".format(
                            inpatient_no=x['INPATIENT_NO'],
                            update_time=f"'{x['UPDATE_TIME']}'" if pandas.notnull(x['UPDATE_TIME']) else 'NULL',
                            out_date=x['OUT_DATE'],
                            is_submitted=x['IS_SUBMITTED']
                        )
                    , axis=1).str.cat(sep=', ')}
                    ON DUPLICATE KEY UPDATE UPDATE_TIME = VALUES(UPDATE_TIME), OUT_DATE = VALUES(OUT_DATE), IS_SUBMITTED = VALUES(IS_SUBMITTED)
        """
        db(None, None, sql, 'mysql')

    data_df = get_follow_up_plan(
        data_information[data_information['UPDATE_TIME'].notnull()])  # 仅处理已提交证明的患者

    insert_follow_up_db(data_df, test_model=test_model)

    if not test_model:
        sql = f'''UPDATE yzzxyy.info_db SET value = '{end_time}' WHERE index_information = 'follow_generate_time' '''
        db(None, None, sql, 'mysql')


if __name__ == '__main__':
    start()
