/*
 Navicat Premium Dump SQL

 Source Server         : 随访数据库
 Source Server Type    : MySQL
 Source Server Version : 80405 (8.4.5)
 Source Host           : ***************:33060
 Source Schema         : yzzxyy

 Target Server Type    : MySQL
 Target Server Version : 80405 (8.4.5)
 File Encoding         : 65001

 Date: 29/07/2025 08:38:34
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for follow_bind
-- ----------------------------
DROP TABLE IF EXISTS `follow_bind`;
CREATE TABLE `follow_bind`  (
  `USER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分机号UID',
  `BINDING_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分机电话号',
  `EXT_NO` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分机短号',
  `USER_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '别名',
  `USER_TYPE` tinyint NULL DEFAULT NULL COMMENT '用户类型',
  `MOBILE_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主叫号码',
  `CAPTURE` tinyint(1) NULL DEFAULT 0 COMMENT '待捕获次数',
  `BINDING_TIME` datetime NULL DEFAULT '2025-01-01 00:00:00' COMMENT '最后绑定时间',
  PRIMARY KEY (`USER_ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访通话绑定' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_call
-- ----------------------------
DROP TABLE IF EXISTS `follow_call`;
CREATE TABLE `follow_call`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `CALL_1` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主叫号码',
  `CALL_X` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中间号码',
  `CALL_2` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被叫号码',
  `TIME` datetime NULL DEFAULT NULL COMMENT '通话时间',
  `CALL_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通话唯一ID',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `follow_call_CALL_1_CALL_2_index`(`CALL_1` ASC, `CALL_2` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 230 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访通话记录（由客户端发起）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_content
-- ----------------------------
DROP TABLE IF EXISTS `follow_content`;
CREATE TABLE `follow_content`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `INFO` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容显示',
  `COMBO` varchar(400) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组合框字典',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 55 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回访内容' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_dedicated
-- ----------------------------
DROP TABLE IF EXISTS `follow_dedicated`;
CREATE TABLE `follow_dedicated`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `DEPT_CODE` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室代码',
  `DEDICATED_CODE` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专人工号',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `follow_dedicated_pk`(`DEPT_CODE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '回访专人' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_dsms
-- ----------------------------
DROP TABLE IF EXISTS `follow_dsms`;
CREATE TABLE `follow_dsms`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工号',
  `NUM` int NULL DEFAULT NULL COMMENT '数量',
  `WARN_LEVEL` int NULL DEFAULT NULL COMMENT '0 计划任务 1超时 2严重超时 3行政监管',
  `CHIEF` tinyint(1) NULL DEFAULT 0 COMMENT '主任',
  `VOIP` tinyint(1) NULL DEFAULT 0 COMMENT '语音通知',
  `ADD_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 185 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '医生短信' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_dsms_notice
-- ----------------------------
DROP TABLE IF EXISTS `follow_dsms_notice`;
CREATE TABLE `follow_dsms_notice`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `TEL` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '号码',
  `COMPLETE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '通知内容',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 54 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知患者消息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for follow_group_config
-- ----------------------------
DROP TABLE IF EXISTS `follow_group_config`;
CREATE TABLE `follow_group_config`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `USER` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户工号',
  `GROUP_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组名',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `follow_group_config_pk_2`(`USER` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分组配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_group_patient
-- ----------------------------
DROP TABLE IF EXISTS `follow_group_patient`;
CREATE TABLE `follow_group_patient`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `ID_CARD` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `GROUP_ID` int NULL DEFAULT NULL COMMENT '分组号',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `ID_CARD_index`(`ID_CARD` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者分组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_info
-- ----------------------------
DROP TABLE IF EXISTS `follow_info`;
CREATE TABLE `follow_info`  (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
  `ICD_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'ICD 诊断编码',
  `TABLE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库和表名，例如 yzzxyy.follow_plan',
  `TABLE_TYPE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '数据库表名对应的类型，mysql 或 oracle',
  `FOLLOW_PLAN` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '特殊随访计划，以JSON字符串表示',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for follow_plan
-- ----------------------------
DROP TABLE IF EXISTS `follow_plan`;
CREATE TABLE `follow_plan`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `NEXT_TIME` datetime NULL DEFAULT NULL COMMENT '预警时间',
  `OUT_DATE` date NULL DEFAULT NULL COMMENT '出院时间',
  `DEPT_CODE` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出院科室代码',
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住院号/门诊号',
  `NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `APPELLATION` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '称呼',
  `ID_CARD` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `TEL` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者电话列表',
  `INDEX_ID` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计划序号（诊断书中的第几条随访计划，0为普通随访）',
  `CYCLE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否是周期性的',
  `TO_HOUR` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否需精准到小时',
  `HOURS` int NULL DEFAULT NULL COMMENT '小时数',
  `CONTENT` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回访内容',
  `COMPLETE` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完整回访内容',
  `DONE` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '是否已完成全部回访',
  `CYCLE_TIMES` int NULL DEFAULT 0 COMMENT '已完成周期随访次数',
  `STATE` int NULL DEFAULT 0 COMMENT '预警级别(0还未短信1已短信2已生成任务3已超时4已二次超时5严重超时)',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 117945 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访方案' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_plan_prove
-- ----------------------------
DROP TABLE IF EXISTS `follow_plan_prove`;
CREATE TABLE `follow_plan_prove`  (
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '住院号/门诊号',
  `UPDATE_TIME` datetime NULL DEFAULT NULL COMMENT '诊断证明提交时间',
  `OUT_DATE` datetime NULL DEFAULT NULL COMMENT '出院时间',
  PRIMARY KEY (`INPATIENT_NO`) USING BTREE,
  INDEX `follow_plan_prove_UPDATE_TIME_index`(`UPDATE_TIME` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访方案-诊断证明提交时间' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_psms_history
-- ----------------------------
DROP TABLE IF EXISTS `follow_psms_history`;
CREATE TABLE `follow_psms_history`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `PATIENT_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '患者姓名',
  `ID_CARD` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `DEPT_CODE` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室代码',
  `SMS_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '短信时间',
  `TEL` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '短信号码',
  `CONTENT` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提醒内容',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `ID_CARD_index`(`ID_CARD` ASC) USING BTREE,
  INDEX `DEPT_CODE_index`(`DEPT_CODE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 307 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '患者短信历史' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_questionnaire
-- ----------------------------
DROP TABLE IF EXISTS `follow_questionnaire`;
CREATE TABLE `follow_questionnaire`  (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '唯一标识',
  `QUESTIONNAIRE_ID` int NULL DEFAULT NULL COMMENT '问卷ID，一个问卷ID对应多个此表的CONTENT_ID',
  `CONTENT_ID` int NULL DEFAULT NULL COMMENT '关联 follow_content 的 ID 字段',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '存储问卷信息的中间表，与 follow_task_artificial 和 follow_content 有关' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for follow_questionnaire_relation
-- ----------------------------
DROP TABLE IF EXISTS `follow_questionnaire_relation`;
CREATE TABLE `follow_questionnaire_relation`  (
  `ID` int NOT NULL COMMENT '唯一标识',
  `QUESTIONNAIRE_NAME` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '问卷名称',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for follow_record
-- ----------------------------
DROP TABLE IF EXISTS `follow_record`;
CREATE TABLE `follow_record`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `USER_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分机号UID',
  `BILL_PHONE` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分机电话号',
  `EXT_NO` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分机短号',
  `CALL_FLAG` tinyint NULL DEFAULT NULL COMMENT '呼叫类型 1呼出 2 呼入',
  `START_TIME` datetime NULL DEFAULT NULL COMMENT '通话开始时间',
  `END_TIME` datetime NULL DEFAULT NULL COMMENT '通话结束时间',
  `DURATION_TIME` int NULL DEFAULT NULL COMMENT '通话长度(s)',
  `CALL_RESULT` tinyint NULL DEFAULT NULL COMMENT '呼叫结果 1成功 2失败 3漏话',
  `RECORD_FILE` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '录音文件',
  `CALL_ID` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '通话唯一ID 后位1',
  `CALLER` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主叫号码 后位2',
  `CALLED` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被叫号码 后位3',
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `follow_record_CALL_ID_uindex`(`CALL_ID` ASC) USING BTREE,
  INDEX `follow_record_CALLER_CALLED_USER_ID_index`(`CALLER` ASC, `CALLED` ASC, `USER_ID` ASC) USING BTREE COMMENT '主被叫+分机ID索引',
  INDEX `follow_record_CALL_FLAG_index`(`CALL_FLAG` ASC) USING BTREE COMMENT '呼叫类型索引'
) ENGINE = InnoDB AUTO_INCREMENT = 183 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '随访通话记录（由Web端获取）' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_task_artificial
-- ----------------------------
DROP TABLE IF EXISTS `follow_task_artificial`;
CREATE TABLE `follow_task_artificial`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `INPATIENT_NO` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住院号',
  `DEPT_CODE` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '科室代码',
  `HOUSE_DOC_CODE` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主管医生工号',
  `HOUSE_DOC_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主管医生姓名',
  `DEDICATED_CODE` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回访专人工号（对于升级订单，设置为主管医生）',
  `DEDICATED_NAME` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回访专人姓名',
  `PLAN_ID` int NULL DEFAULT NULL COMMENT '随访方案ID（follow_plan表的ID字段）',
  `CONTENT_ID` int NULL DEFAULT NULL COMMENT '随访内容ID',
  `COMBO1` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组合框1中的内容',
  `COMBO2` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组合框2中的内容',
  `ADDS` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '追加文本内容',
  `REMARKS` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注内容',
  `F_TEL` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回访所联系的电话',
  `CALL_ID` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用“|”分割CALL的ID号码',
  `COMPLETE_TIME` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `STATE` int NULL DEFAULT 0 COMMENT '回访状态（0未回访1未回访的升级单2已升级本单作废3已取消4已完成）',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `DEPT_CODE_index`(`DEPT_CODE` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 427 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人工随访任务' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_warning
-- ----------------------------
DROP TABLE IF EXISTS `follow_warning`;
CREATE TABLE `follow_warning`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `USER` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户工号',
  `ID_CARD` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `CONDITIONS` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '条件列表',
  `TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `ID_CARD_index`(`ID_CARD` ASC) USING BTREE,
  INDEX `USER_index`(`USER` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预警信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for follow_warning_history
-- ----------------------------
DROP TABLE IF EXISTS `follow_warning_history`;
CREATE TABLE `follow_warning_history`  (
  `ID` int NOT NULL AUTO_INCREMENT,
  `USER` varchar(6) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户工号',
  `ID_CARD` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '身份证号',
  `CONDITIONS_TIME` datetime NULL DEFAULT NULL COMMENT '条件时间',
  `SMS_TIME` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '短信时间',
  `CONFIRM_TIME` datetime NULL DEFAULT NULL COMMENT '确认时间',
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `USER_index`(`USER` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '预警历史' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
