import ast
import base64
import hashlib
import json

import requests
import time

from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

from celery_task.celery import app
from custom.db.mysql import UsingMysql

with UsingMysql() as um:
    privacy = ast.literal_eval(
        um.fetch_decrypt("select value from info_db where index_information = 'privacy' ",
                         None, 'value'))
platform = "voice"
version = '1.0.0'
function_code = "middleNumberAXB"
base_url = "https://101.37.133.245:11008"
MAX_RETRIES = 3  # 最大重试次数
BACKOFF_FACTOR = 0.5  # 指数退避因子
TIMEOUT = (3.05, 27)  # 连接超时3.05s，读取超时27s
SSL_VERIFY = True  # 生产环境必须验证SSL证书
CA_BUNDLE = True  # CA证书路径 '/path/to/cert.pem'（或设置为True使用系统默认）


def create_retry_session():
    session = requests.Session()
    retry = Retry(total=MAX_RETRIES,
                  backoff_factor=BACKOFF_FACTOR,
                  status_forcelist=[500, 502, 503, 504],
                  allowed_methods=['POST', 'GET'])
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('https://', adapter)
    return session


@app.task
def bind(doctor_phone: str, patient_phone: str, loss=10):
    """ loss失效时间 """
    timestamp = str(int(time.time() * 1000))
    sig_str = privacy['appId'] + privacy['token'] + timestamp  # 验证信息
    sig = hashlib.md5(sig_str.encode()).hexdigest()
    authorization = base64.b64encode(f"{privacy['appId']}:{timestamp}".encode()).decode()
    url = f"{base_url}/{platform}/{version}/{function_code}/{privacy['appId']}/{sig}"

    headers = {"Content-Type": "application/json",
               "Authorization": authorization,
               "Cache-Control": "no-cache"}

    # todo 修改
    payload = {"middleNumber": None,  # 小号号码
               "bindNumberA": doctor_phone,
               "bindNumberB": patient_phone,
               "maxBindingTime": loss * 60,  # 绑定关系有效时长
               "maxCallDuration": None,  # 允许的最大通话时长，单位秒（只支持绑定固话小号时生效）
               "callbackUrl": None,  # 呼叫结果推送地址，http 协议
               "announceCodeToA": None,  # 号码 A 拨打小号时候的放音编号
               "announceCodeToB": None,  # 号码 B 拨打小号时候的放音编号
               "announceCodeToOther": None,  # 其他号码拨打小号时候的放音编号
               "areaCode": None,  # 不指定中间小号的时候会根据该字段分配中间小号
               "customerData": None,  # 用户自定义字段, 呼叫结束后，推送回用户
               }

    session = create_retry_session()
    try:
        response = session.post(url,
                                json=payload,
                                headers=headers,
                                verify=CA_BUNDLE if SSL_VERIFY else False,  # SSL证书验证
                                timeout=TIMEOUT)
        response.raise_for_status()  # 自动触发HTTP错误异常

        try:
            json_response = response.json()
            return json_response  # message，bindId，result，middleNumber
        except json.JSONDecodeError:
            return {'message': '响应包含无效的JSON格式'}

    except requests.exceptions.SSLError as e:
        return {'message': 'SSL证书验证失败'}
    except requests.exceptions.ConnectionError as e:
        return {'message': f"连接失败: {str(e)}"}
    except requests.exceptions.Timeout as e:
        return {'message': f"请求超时: {str(e)}"}
    except requests.exceptions.HTTPError as e:
        return {'message': f"HTTP错误: {str(e)}"}
    except requests.exceptions.RequestException as e:
        return {'message': f"请求异常: {str(e)}"}
    except Exception as e:
        return {'message': f"未知异常: {str(e)}"}
    finally:
        session.close()

    # test = asyncio.run(Privacy.bind_privacy_async(doctor_phone, patient_phone, loss))
    # return {'patient_phone': test.get('extension', ''),  # 患者号码
    #         'privacy_phone': test.get('secret_no', ''),  # 隐私号码
    #         'subs_id': test.get('subs_id', '')  # 绑定ID，用于通话记录查询
    #         }



if __name__ == '__main__':
    # print(query_phone('25078'))
    print(bind('18974680622', '15074620622'))
