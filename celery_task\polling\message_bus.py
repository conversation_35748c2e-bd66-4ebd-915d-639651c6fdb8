from celery import shared_task
from datetime import datetime, timedelta
import pytz
from custom.db.execute import db
from typing import Dict, Any, List, Optional
from celery_task.msgbus import msgbus_api


@shared_task
def dispatch_single_message(task_type: int, content: str, recipient_info: Dict[str, Any], urgency_level: int = 5,
                            template_type: str = None, template_data: Dict[str, Any] = None, template_id: int = None, wxsm_msg_type: int = None):
    """处理单个接收者的消息发送"""
    try:
        # 将 urgency_level 映射到优先级字符串
        priority_map = {1: "URGENT", 3: "HIGH", 5: "NORMAL", 8: "LOW"}
        priority = priority_map.get(urgency_level, "NORMAL")

        if task_type == 0:
            # 微信小程序 - 微信小程序是没有模板消息的，使用自定义消息
            # 插入一条数据到 wxsm_msg 表中
            result = send_wx_small_program_message(template_id, recipient_info, template_data, wxsm_msg_type)
            return {"status": "success", "type": "wechat_miniprogram", "target": recipient_info.get('doctor_id'),
                    "result": result}

        elif task_type == 1:
            # 微信服务号 - 使用微信模板消息
            result = msgbus_api.send_wechat_template(
                open_id=recipient_info.get('openid'),
                template_type=template_type or "generate",
                data=template_data or {"thing33": {"value": content}},
                priority=priority,
                async_mode=False  # 在Celery任务内使用同步模式
            )
            return {"status": "success", "type": "wechat_service", "target": recipient_info.get('openid'),
                    "result": result}

        elif task_type == 2:
            # TTS语音通知
            result = msgbus_api.send_voice_tts(
                phone=recipient_info.get('phone'),
                text=content,
                play_size=2,  # 默认播放2次
                priority=priority,
                async_mode=False  # 在Celery任务内使用同步模式
            )
            return {"status": "success", "type": "voice_tts", "target": recipient_info.get('phone'), "result": result}

        elif task_type == 3:
            # VOX录音播放
            file_name = recipient_info.get('file_name', 'notification.wav')
            result = msgbus_api.send_voice_vox(
                phone=recipient_info.get('phone'),
                file_name=file_name,
                play_size=2,  # 默认播放2次
                priority=priority,
                async_mode=False  # 在Celery任务内使用同步模式
            )
            return {"status": "success", "type": "voice_vox", "target": recipient_info.get('phone'), "result": result}

        else:
            return {"status": "error", "message": f"未知的任务类型: {task_type}"}

    except Exception as e:
        return {"status": "error", "message": f"消息发送失败: {str(e)}", "task_type": task_type,
                "recipient": recipient_info}


def _get_template_from_db(template_id: int) -> Optional[str]:
    """查数据库获取模板内容"""
    template_df = db(['content'], 'yzzxyy.message_template', {'template_id': template_id}, 'mysql')
    return template_df.iloc[0]['content'] if not template_df.empty else None


def _get_template_type_from_db(template_id: int) -> Optional[str]:
    """查数据库获取模板类型，用于发送微信小程序、服务号模板消息"""
    template_df = db(['template_type'], 'yzzxyy.message_template', {'template_id': template_id}, 'mysql')
    return template_df.iloc[0]['template_type'] if not template_df.empty else None


def _resolve_recipients(push_mode: int, target_id: str) -> List[Dict[str, Any]]:
    """解析接收者信息"""
    targets = [t.strip() for t in target_id.split('|') if t.strip()]
    if not targets:
        return []

    # 电话号码推送
    if push_mode == 2:
        return [{'phone': phone} for phone in targets]

    doctor_ids = []
    if push_mode == 0:
        # 医生工号推送
        doctor_ids = targets
    elif push_mode == 1:
        # 标签推送
        ids_str = ",".join([f"'{str(id)}'" for id in doctor_ids])
        sql = f"SELECT DISTINCT doctor_id FROM yzzxyy.doctor_tag_relation WHERE tag_id IN ({ids_str})"
        doctor_df = db(None, None, sql, 'mysql')
        if not doctor_df.empty:
            doctor_ids = doctor_df['doctor_id'].astype(str).tolist()

    # 查询医生联系方式
    if doctor_ids:
        ids_str = ",".join([f"{str(id)}" for id in doctor_ids])
        sql = f"""
            SELECT id as doctor_id, ip.tel as phone, iu.wx_id as openid FROM yzzxyy.info_user iu
            JOIN yzzxyy.info_phone ip ON iu.id = ip.code
            WHERE iu.id in ({ids_str})
        """
        # sql = f"SELECT doctor_id, phone, openid FROM yzzxyy.doctors WHERE doctor_id IN ({placeholders})"
        contact_df = db(None, None, sql, 'mysql')
        return contact_df.to_dict('records') if not contact_df.empty else []

    return []


def send_message(
        task_type: int,
        urgency_level: int,
        push_mode: int,
        target_id: str,
        template_id: Optional[int] = None,
        template_params: Optional[Dict[str, Any]] = None,
        optional_content: Optional[str] = None,
        scheduled_time_str: Optional[str] = None,
        countdown: Optional[int] = None,
        template_type: Optional[str] = None,
        file_name: Optional[str] = None,
        wxsm_msg_type: Optional[int] = None
):
    """发送消息的核心方法"""

    # 解析接收者
    recipients = _resolve_recipients(push_mode, target_id)
    if not recipients:
        return {"status": "error", "message": "未找到有效的接收者"}

    # 处理定时发送
    is_scheduled = countdown is not None or (urgency_level == 1 and scheduled_time_str)
    scheduled_time_dt = None

    if not countdown and is_scheduled:
        try:
            # 1. 将输入的时间字符串解析为 naive datetime
            naive_dt = datetime.fromisoformat(scheduled_time_str)

            # 2. 假定输入为本地时间，为其附加时区信息
            local_tz = pytz.timezone('Asia/Shanghai')
            local_dt = local_tz.localize(naive_dt)

            # 3. 转换为 UTC 时间以传递给 Celery 的 eta 参数
            scheduled_time_dt = local_dt.astimezone(pytz.utc)

            # 4. 使用带时区的当前时间进行比较
            if scheduled_time_dt <= datetime.now(pytz.utc):
                is_scheduled = False

        except (ValueError, TypeError) as e:
            return {"status": "error", "message": f"无效的时间格式: {str(e)}"}

    # 准备模板数据（用于微信消息）
    template_data = None
    if task_type in [0, 1]:  # 微信消息
        template_data = optional_content

    # 分发任务
    for recipient in recipients:
        # 为VOX录音添加文件名信息
        if task_type == 3 and file_name:
            recipient['file_name'] = file_name

        if is_scheduled:
            # 修复：countdown 和 eta 不能同时使用
            if countdown is not None:
                # 使用相对时间
                dispatch_single_message.apply_async(
                    args=[task_type, optional_content, recipient, urgency_level, template_type, template_data,
                          template_id, wxsm_msg_type],
                    countdown=countdown
                )
            elif scheduled_time_dt is not None:
                # 使用绝对时间
                dispatch_single_message.apply_async(
                    args=[task_type, optional_content, recipient, urgency_level, template_type, template_data,
                          template_id, wxsm_msg_type],
                    eta=scheduled_time_dt
                )
        else:
            dispatch_single_message.delay(task_type, optional_content, recipient, urgency_level, template_type,
                                          template_data, template_id, wxsm_msg_type)

    count = len(recipients)
    if countdown:
        return {"status": "success", "message": f"已调度 {count} 条消息于 {countdown} 秒后执行"}
    if is_scheduled:
        return {"status": "success", "message": f"已调度 {count} 条消息于 {scheduled_time_dt}"}
    else:
        return {"status": "success", "message": f"已分发 {count} 条消息"}


# 新增便捷方法
def send_voice_tts_message(phone: str, text: str, priority: str = "NORMAL", play_size: int = 1):
    """发送TTS语音消息的便捷方法"""
    result = msgbus_api.send_voice_tts(
        phone=phone,
        text=text,
        play_size=play_size,
        priority=priority
    )
    return result


def send_voice_vox_message(phone: str, file_name: str, priority: str = "NORMAL", play_size: int = 1):
    """发送VOX录音消息的便捷方法"""
    result = msgbus_api.send_voice_vox(
        phone=phone,
        file_name=file_name,
        play_size=play_size,
        priority=priority
    )
    return result


def send_wechat_template_message(open_id: str, template_type: str, data: Dict[str, Any],
                                 priority: str = "NORMAL", url: str = None):
    """发送微信模板消息的便捷方法"""
    result = msgbus_api.send_wechat_template(
        open_id=open_id,
        template_type=template_type,
        data=data,
        url=url,
        priority=priority
    )
    return result

def send_wx_small_program_message(template_id, recipient_info, data, wxsm_msg_type):
    """
    这个方法，是用来发送微信小程序消息的。
    因为微信小程序不像微信服务号那样，有微信提供的消息功能，所以就自定义了这个方法，“发送”就意味着往wxsm_msg表中插入一条数据。
    data 是这样的一种数据：{ 'name': {'value': '陈医生'}, 'time': {'value': '2025-08-01 08:00:00'}, 'location': {'value': '五教大礼堂'} }
    {name}，您于{time}有一场线下会议，会议地址{location}，请及时参加。
    name,time,location
    :return:
    """
    work_id = recipient_info.get('doctor_id')

    now = datetime.now()

    expire_time = now + timedelta(days=7)
    expire_time_str = expire_time.strftime('%Y-%m-%d %H:%M:%S')  # 去掉微秒部分
    send_time_str = now.strftime('%Y-%m-%d %H:%M:%S')  # 去掉微秒部分

    # 查一下 message_template 中的 template_content
    template_content = db('content', "yzzxyy.message_template", {'template_id': template_id}, 'mysql')

    content = _fill_template(template_content, data)

    sql2 = f"""
        INSERT INTO yzzxyy.wxsm_msg
        (template_id, target_id, content, send_time, expire_time, wxsm_msg_type)
        VALUES ({template_id}, '{work_id}', '{content}','{send_time_str}', '{expire_time_str}', {wxsm_msg_type})
    """

    db(None, None, sql2, 'mysql')

    return "发送微信小程序消息成功"


def _fill_template(template_content: str, data: dict) -> str:
    """
    将模板字符串中的占位符，如 {name}、{time}，替换为 data 字典中对应的 value 值。
    :param template_content: 字符串模板，如 "您好，{name}，您于{time}有会议。"
    :param data: 形如 {'name': {'value': '陈医生'}} 的嵌套字典
    :return: 替换后的完整字符串
    """
    for key, val in data.items():
        placeholder = f"{{{key}}}"
        template_content = template_content.replace(placeholder, val.get("value", ""))
    return template_content
