import ast
import pandas
from celery_task.celery import app
from custom.db.execute import db
from datetime import datetime, timedelta
import custom.settins


def get_follow_up_plan_refactored(data_information: pandas.DataFrame) -> pandas.DataFrame:
    """
    重构后的随访方案获取函数，适配新的数据库结构，结构化医嘱
    """
    inpatient_nos = data_information['INPATIENT_NO'].tolist()
    sub_lists = [inpatient_nos[i:i + 900] for i in range(0, len(inpatient_nos), 900)]
    where_clause = " OR ".join([f"""INPATIENT_NO in ('{"', '".join(sub)}')""" for sub in sub_lists])
    
    # 查询出院诊断书随访信息
    sql = f"""select INPATIENT_NO, VALUE, ELEMENT_NAME
                from HIT_MDC.MDC_RCD_IN_RECORD_ITEM
                where ELEMENT_ID in ('2054157', '2054158', '2054159', '2054160',
                                     '2054161', '2054162', '2054163', '2054164',
                                     '2054172', '2054179', '2054186', '2054193',
                                     '2054173', '2054180', '2054187', '2054194',
                                     '2054174', '2054181', '2054188', '2054195',
                                     '2054175', '2054182', '2054189', '2054196',
                                     '2054176', '2054183', '2054190', '2054197',
                                     '2054177', '2054184', '2054191', '2054198',
                                     '2054178', '2054185', '2054192', '2054199',
                                     '375')
                and INPATIENT_RECORD_NAME = '出院诊断书-随访'
                and ({where_clause}) """
    result = db(None, None, sql, 'oracle')
    
    follow_up = []
    for inpatient_no in set(data_information['INPATIENT_NO']):
        info_df = data_information[data_information['INPATIENT_NO'] == inpatient_no].reset_index(drop=True)
        item_df = result[result['INPATIENT_NO'] == inpatient_no].reset_index(drop=True)
        
        if not item_df.empty:
            # 有个性化方案的患者，首次为自动提醒
            follow_up.append({
                'inpatient_no': inpatient_no,
                'next_action_time': get_next_time(False, info_df.loc[0, 'OUT_DATE'],
                                                custom.settins.start_time_of_manual_follow_up * 24),
                'cycle': False,
                'hours': custom.settins.start_time_of_manual_follow_up * 24,
                'content': '自动回访任务', # 后面如果识别到content为自动回访任务，在消息的时候就自动把内容替换成真正的自动随访要问的内容
                'end_date': None,
                'plan_source': '医嘱生成',
                'diag_match_source_type': None,
                'source_id': None,
                'dept_code': info_df.loc[0, 'DEPT_CODE'],
                'house_doc_code': None,
                'house_doc_name': None
            })
            
            for i in range(9):
                df_cycle = item_df[item_df['ELEMENT_NAME'] == f'随访计划周期{i + 1}']
                plan_cycle = False
                if not df_cycle.empty:
                    str_cycle = df_cycle.iloc[0, 1].strip()
                    plan_cycle = str_cycle == '每'
                
                df_time = item_df[item_df['ELEMENT_NAME'] == f'随访计划时间{i + 1}']
                if df_time.empty:
                    break
                    
                plan_time = df_time.iloc[0, 1]
                plan_unit = item_df[item_df['ELEMENT_NAME'] == f'随访计划时间单位{i + 1}'].iloc[0, 1]
                plan_hours = get_hours(plan_time, plan_unit)
                if plan_hours == 0:
                    break
                
                next_time = get_next_time(plan_unit == '小时', info_df.loc[0, 'OUT_DATE'], int(plan_hours))
                plan_content = remove_first_last_mark(
                    item_df[item_df['ELEMENT_NAME'] == f'随访处理内容{i + 1}'].iloc[0, 1])
                
                follow_up.append({
                    'inpatient_no': inpatient_no,
                    'next_action_time': next_time,
                    'cycle': plan_cycle,
                    'hours': plan_hours,
                    'content': plan_content,
                    'end_date': None,
                    'plan_source': '医嘱生成',
                    'diag_match_source_type': None,
                    'source_id': None,
                    'dept_code': info_df.loc[0, 'DEPT_CODE'],
                    'house_doc_code': None,
                    'house_doc_name': None
                })
        else:
            # 没写随访方案的患者
            follow_up.append({
                'inpatient_no': inpatient_no,
                'next_action_time': get_next_time(False, info_df.loc[0, 'OUT_DATE'],
                                                custom.settins.start_time_of_manual_follow_up * 24),
                'cycle': False,
                'hours': custom.settins.start_time_of_manual_follow_up * 24,
                'content': '常规随访任务', # 后面如果识别到content为常规随访任务，在消息的时候就自动把内容替换成真正的常规随访要问的内容
                'end_date': None,
                'plan_source': '医嘱生成',
                'diag_match_source_type': None,
                'source_id': None,
                'dept_code': info_df.loc[0, 'DEPT_CODE'],
                'house_doc_code': None,
                'house_doc_name': None
            })
    
    return pandas.DataFrame(follow_up)


def insert_follow_up_db_refactored(data: pandas.DataFrame, test_model=False):
    """
    重构后的随访方案插入函数，适配新的数据库结构
    """
    if data.empty:
        return
    
    # 批量插入到新的plan表
    batch_size = 100
    for i in range(0, len(data), batch_size):
        batch = data.iloc[i:i + batch_size]
        values = ", ".join(batch.apply(
            lambda x: f"""('{x['next_action_time']}', '{x['inpatient_no']}', 
                          {1 if x['cycle'] else 0}, {x['hours']}, '{x['content']}', 
                          {f"'{x['end_date']}'" if x['end_date'] else 'NULL'}, 
                          '{x['plan_source']}', '{x['diag_match_source_type']}', 
                          {x['source_id'] if x['source_id'] else 'NULL'}, 
                          '{x['dept_code']}', 
                          {f"'{x['house_doc_code']}'" if x['house_doc_code'] else 'NULL'}, 
                          {f"'{x['house_doc_name']}'" if x['house_doc_name'] else 'NULL'})""",
            axis=1))
        
        if values and not test_model:
            sql = f'''INSERT INTO follow.plan 
                     (NEXT_ACTION_TIME, INPATIENT_NO, CYCLE, HOURS, CONTENT, 
                      END_DATE, PLAN_SOURCE, DIAG_MATCH_SOURCE_TYPE, SOURCE_ID, 
                      DEPT_CODE, HOUSE_DOC_CODE, HOUSE_DOC_NAME)
                     VALUES {values}'''
            db(None, None, sql, 'mysql')


def get_next_time(to_hour: bool, start_date: datetime, add_hours: int):
    """获取下次随访时间"""
    if to_hour:
        next_time = (start_date + timedelta(hours=add_hours)).strftime("%Y-%m-%d %H:%M")
    else:
        next_time = ((start_date + timedelta(hours=add_hours)).strftime("%Y-%m-%d") +
                     ' ' + custom.settins.notification_time)
    return next_time


def get_hours(plan_time, plan_unit):
    """转换时间单位为小时"""
    try:
        time_value = int(plan_time)
        if plan_unit == '小时':
            return time_value
        elif plan_unit == '天':
            return time_value * 24
        elif plan_unit == '周':
            return time_value * 24 * 7
        elif plan_unit == '月':
            return time_value * 24 * 30
        else:
            return 0
    except:
        return 0


def remove_first_last_mark(content: str):
    """移除文本首位的符号"""
    import re
    cleaned_content = re.sub(r'(^[；;。.，,])|([；;。.，,]$)', '', content)
    return cleaned_content


@app.task
def start(test_model=False):
    """
    重构后的主函数，适配新的数据库结构
    """
    # Step1：基础数据获取
    start_time = db('value', 'yzzxyy.info_db', {'index_information': 'follow_generate_time'}, 'mysql')
    end_time = str(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))

    # Step2：获取需要查询的患者信息
    entire = db('VALUE', 'yzzxyy.info_setting', {'NAME': "FOLLOW_ENTIRE"}, 'mysql') == '1'
    sql_add = ''
    if not entire:
        dept_code_list = ast.literal_eval(db('VALUE', 'yzzxyy.info_setting',
                            {'NAME': "FOLLOW_DEPT_CODE"}, 'mysql'))
        dept_code_str = "', '".join(dept_code_list)
        sql_add = '' if dept_code_str else f"AND DEPT_CODE IN ('{dept_code_str}')"

    data_information = db(
        ['NAME', 'INPATIENT_NO', 'SEX_CODE', 'IDENNO', 'HOME_TEL', 'LINKMAN_TEL', 'OUT_DATE', 'DEPT_CODE'],
        'HNYZ_ZXYY.FIN_IPR_INMAININFO',
        f'''OUT_DATE BETWEEN TO_DATE('{start_time}', 'YYYY-MM-DD HH24:MI:SS') 
                    AND TO_DATE('{end_time}', 'YYYY-MM-DD HH24:MI:SS') {sql_add}''',
        'oracle')

    # 追加查询：【随访方案-诊断证明提交时间】表中UPDATE_TIME是空的数据，这里是为了持续朱总诊断证明没有提交的患者信息
    empty_update = db(['INPATIENT_NO'], 'follow.plan_prove',
                      {'IS_SUBMITTED': '未提交'}, 'mysql')
    if not empty_update.empty:
        inpatient_nos = empty_update['INPATIENT_NO'].tolist()
        sub_lists = [inpatient_nos[i:i + 900] for i in range(0, len(inpatient_nos), 900)]
        data_information_add = db(
            ['NAME', 'INPATIENT_NO', 'SEX_CODE', 'IDENNO', 'HOME_TEL', 'LINKMAN_TEL', 'OUT_DATE', 'DEPT_CODE'],
            'HNYZ_ZXYY.FIN_IPR_INMAININFO',
            " OR ".join([f"""INPATIENT_NO in ('{"', '".join(sub)}')""" for sub in sub_lists]),
            'oracle')
        data_information = pandas.concat([data_information, data_information_add], ignore_index=True)

    # Step3：更新【随访方案-诊断证明提交时间】表
    if data_information.empty:
        return
    
    inpatient_nos = data_information['INPATIENT_NO'].tolist()
    sub_lists = [inpatient_nos[i:i + 900] for i in range(0, len(inpatient_nos), 900)]
    where_clause = " OR ".join([f"""s.INPATIENT_NO in ('{"', '".join(sub)}')""" for sub in sub_lists])
    sql = f'''SELECT s.INPATIENT_NO,
                   (SELECT MAX(r.UPDATE_TIME)
                    FROM HIT_MDC.MDC_RCD_IN_RECORD r
                    WHERE r.INPATIENT_RECORD_SET_ID = s.ID
                      AND RECORD_CHILD_TYPE = 'Out_Record'
                      AND INPUT_TPL_ID IN ('33554', '34110', '34498')) UPDATE_TIME
                FROM HIT_MDC.MDC_RCD_IN_RECORD_SET s
                WHERE {where_clause}'''
    data_time = db(None, None, sql, 'oracle')
    data_information = data_information.merge(data_time, on='INPATIENT_NO', how='left')
    
    mask = (data_information['UPDATE_TIME'].isnull() &
            ((datetime.now() - data_information['OUT_DATE']) > timedelta(days=15)))
    data_information.loc[mask, 'UPDATE_TIME'] = data_information.loc[mask, 'OUT_DATE']

    with pandas.option_context("future.no_silent_downcasting", True):
        data_information['UPDATE_TIME'] = data_information['UPDATE_TIME'].infer_objects(copy=False)

    if not test_model:
        # 设置提交状态
        data_information['IS_SUBMITTED'] = data_information['UPDATE_TIME'].apply(
            lambda x: '已提交' if x != datetime.strptime('1970-01-01 00:00:00', '%Y-%m-%d %H:%M:%S') else '未提交')
        
        sql = f"""INSERT INTO follow.plan_prove (INPATIENT_NO, UPDATE_TIME, OUT_DATE, IS_SUBMITTED) VALUES
                    {data_information.apply(lambda x: f"('{x['INPATIENT_NO']}', '{x['UPDATE_TIME']}', '{x['OUT_DATE']}', '{x['IS_SUBMITTED']}')",
                                            axis=1).str.cat(sep=', ')} 
                    ON DUPLICATE KEY UPDATE UPDATE_TIME = VALUES(UPDATE_TIME), OUT_DATE = VALUES(OUT_DATE), IS_SUBMITTED = VALUES(IS_SUBMITTED)
        """
        db(None, None, sql, 'mysql')

    data_df = get_follow_up_plan_refactored(
        data_information[data_information['UPDATE_TIME'] !=
                         datetime.strptime('1970-01-01 00:00:00', '%Y-%m-%d %H:%M:%S')])
    insert_follow_up_db_refactored(data_df, test_model=test_model)
    
    if not test_model:
        sql = f'''UPDATE yzzxyy.info_db SET value = '{end_time}' WHERE index_information = 'follow_generate_time' '''
        db(None, None, sql, 'mysql')


if __name__ == '__main__':
    start()
