import json
import time
import pandas as pd
import requests
import re
import concurrent.futures
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app
from typing import List, Dict, Tuple
from openai import OpenAI
client = OpenAI(
    api_key="sk-grifcwikbizjcfzpvmquaqtwlqsmqiirpbjfwuccpfrulyie",
    base_url="https://api.siliconflow.cn/v1",
)
def check_table():
    """
    主任务函数：提取所有类型癌症的"首次非手术治疗时间"并更新数据库
    使用多批次和多线程处理
    """
    try:
        # 查询需要处理的记录
        sql = """SELECT ID, TEXT \
                 FROM ai.test_no_oper \
                 WHERE CONTENT is null"""
        records_df = db(None, None, sql, 'mysql')
        if records_df.empty:
            print("未找到需要处理的记录")
            return
        print(f"找到{len(records_df)}条需要处理的记录")

        # 设置批次大小和线程数
        batch_size = 5
        max_workers = 5
        total_rows = len(records_df)

        # 创建批次列表
        batches = []
        for start in range(0, total_rows, batch_size):
            end = min(start + batch_size, total_rows)
            batches.append(records_df.iloc[start:end])

        # 使用线程池处理所有批次
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有批次任务
            future_to_batch = {
                executor.submit(process_batch, batch_df): batch_df
                for batch_df in batches
            }

            # 收集结果并处理异常
            for future in concurrent.futures.as_completed(future_to_batch):
                batch_df = future_to_batch[future]
                try:
                    results = future.result()
                    update_content_batch(results)
                    print(f"成功处理批次: {batch_df['ID'].tolist()}")
                except Exception as e:
                    print(f"处理批次 {batch_df['ID'].tolist()} 时出错: {str(e)}")
                    # 失败时使用默认值更新
                    default_results = [{'id': row['ID'], 'result': '1970-01-01 00:00:00'}
                                       for _, row in batch_df.iterrows()]
                    update_content_batch(default_results)

    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def process_batch(batch_df: DataFrame) -> List[Dict]:
    """
    处理一个批次的记录，调用AI提取时间
    """
    results = []
    for _, row in batch_df.iterrows():
        try:
            time_result = extract_content(row)
            results.append({
                'id': row['ID'],
                'result': time_result
            })
        except Exception as e:
            print(f"处理记录ID {row['ID']} 时出错: {str(e)}")
            results.append({
                'id': row['ID'],
                'result': '1970-01-01 00:00:00'
            })
    return results


def extract_content(row: pd.Series) -> str:
    """
    使用AI提取单条记录的首次非手术治疗时间
    """
    prompt = f"""
    你是一名医生，请严格按以下规则获取"医嘱"中的首次特定治疗对应的时间：
    1.特定治疗指代：放疗、化疗、靶向治疗、内分泌治疗、免疫治疗
    2.要求输出特定治疗对应的最早时间
    3.未找到特定治疗时，结果输出为1970-01-01 00:00:00
    4.最终严格按照下面输出要求返回。
    以下是医嘱内容：
     [{row["TEXT"]}]
     以下是输出要求：
     1、输出只允许为一段时间文本，除了时间文本不要有任何文字其他符号如*及说明，切记切记。
     输出举例：2024-01-25 15:06:00
    """

    # 清理提示中的特殊字符
    cleaned_prompt = prompt.translate(str.maketrans('', '', '\t\n\r\x0b\x0c'))

    # 正则表达式验证时间格式
    pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    max_retries = 3
    retry_count = 0

    while retry_count < max_retries:
        try:
            # 调用AI服务
            response = client.chat.completions.create(
                model="deepseek-ai/DeepSeek-R1-0528-Qwen3-8B",
                messages=[{"role": "user", "content": prompt}],
            )

            # 处理AI响应
            ai_output = response.choices[0].message.content.strip()

            # 清理响应内容
            clean_response = re.sub(r'<think>.*?</think>', '', ai_output, flags=re.DOTALL).strip()
            print(clean_response)
            # 验证响应格式
            if re.match(pattern, clean_response):
                return clean_response

            print(f"格式校验失败，重试中... (响应: '{clean_response}')")
            retry_count += 1
            time.sleep(1)  # 避免频繁请求
        except requests.exceptions.RequestException as e:
            print(f"AI服务调用失败: {str(e)}")
            retry_count += 1
            time.sleep(2)  # 网络错误时延长等待

    # 超过最大重试次数，返回默认值
    return '1970-01-01 00:00:00'


def update_content_batch(results: List[Dict]):
    """
    批量更新数据库
    """
    if not results:
        return

    # 构建插入值字符串
    values = []
    for res in results:
        id_val = res['id']
        result_val = res['result']
        # 处理NULL值
        if result_val == 'NULL' or result_val is None:
            values.append(f"('{id_val}', NULL)")
        else:
            values.append(f"('{id_val}', '{result_val}')")

    values_str = ', '.join(values)

    # 构建并执行SQL
    sql = f"""
    INSERT INTO ai.test_no_oper (ID, CONTENT)
    VALUES {values_str}
    ON DUPLICATE KEY UPDATE CONTENT = VALUES(CONTENT)
    """
    db(None, None, sql, 'mysql')


# 定时任务入口
@app.task
def start():
    check_table()


if __name__ == "__main__":
    start()