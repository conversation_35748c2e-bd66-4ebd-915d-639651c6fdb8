import pandas

from celery_task.celery import app
from celery_task.wx_small_program.wxsm_utils import get_dept_name
from custom.db.execute import db


def paged_query(sql_base, page, page_size, db_type):
    """分页查询，返回分页数据 + 总数"""
    if page <= 0 or page_size <= 0:
        raise ValueError("page and page_size must be positive integers.")

    # 先查数据，根据
    count_df = db(None, None, sql_base, db_type)

    # 转换 IN_DATE 为 datetime 并加时区
    if 'IN_DATE' in count_df.columns:
        count_df['IN_DATE'] = pandas.to_datetime(count_df['IN_DATE'], errors='coerce')
        count_df['IN_DATE'] = count_df['IN_DATE'].dt.tz_localize('Asia/Shanghai')  # 设置为北京时间

    start_index = (page - 1) * page_size
    end_index = start_index + page_size

    total_count = len(count_df)
    total_pages = (total_count + page_size - 1) // page_size

    page_data = count_df.iloc[start_index:end_index]
    records = page_data.to_dict(orient='records')

    return {
        "page": page,
        "page_size": page_size,
        "total_count": total_count,
        "total_pages": total_pages,
        "records": records,
    }


def paged_query_with_sql(sql_base: str, count_sql: str, page: int, page_size: int, db_type: str):
    """利用数据库执行分页 SQL，提高性能"""
    if page <= 0 or page_size <= 0:
        raise ValueError("page and page_size must be positive integers.")

    start_row = (page - 1) * page_size + 1
    end_row = page * page_size

    if db_type.lower() == 'oracle':
        sql = f"""
            SELECT * FROM (
                SELECT inner_query.*, ROWNUM AS rn FROM (
                    {sql_base}
                ) inner_query
                WHERE ROWNUM <= {end_row}
            )
            WHERE rn >= {start_row}
        """
    elif db_type.lower() == 'mysql':
        sql = f"""
            {sql_base}
            LIMIT {page_size} OFFSET {(page - 1) * page_size}
        """
    else:
        raise ValueError(f"Unsupported db_type: {db_type}")

    # 分页数据
    data_df = db(None, None, sql, db_type)
    if 'IN_DATE' in data_df.columns:
        data_df['IN_DATE'] = pandas.to_datetime(data_df['IN_DATE'], errors='coerce')
        data_df['IN_DATE'] = data_df['IN_DATE'].dt.tz_localize('Asia/Shanghai')

    # 总数
    count_df = db(None, None, count_sql, db_type)
    total_count = int(count_df.iloc[0][0]) if not count_df.empty else 0
    total_pages = (total_count + page_size - 1) // page_size

    return {
        "page": page,
        "page_size": page_size,
        "total_count": total_count,
        "total_pages": total_pages,
        "records": data_df.to_dict(orient='records')
    }


def get_symbol(row):
    if row['TOTAL_COUNT'] == 1:
        return ''
    elif row['ROW_NUM'] == 1:
        return '⌈ '
    elif row['ROW_NUM'] == row['TOTAL_COUNT']:
        return '⌊ '
    else:
        return '| '


@app.task
def get_check_report_data(inpatient_no: str):
    """ 根据住院号 inpatient_no 获取某个病人的所有检验报告"""
    sql = f"""
          SELECT SAMPLETIME, 
                 HIS_ITEMNAME, 
                 BARCODE
          FROM HNYZ_ZXYY.LIS_TEST_REG
          WHERE INPATIENTNO = '{inpatient_no}'
          AND RESULTDATE IS NOT NULL
    """
    dataform = db(None, None, sql, 'oracle')
    if dataform.empty:
        return []
    # 转换 SAMPLETIME 为 datetime 类型并添加时区
    if 'SAMPLETIME' in dataform.columns:
        dataform['SAMPLETIME'] = pandas.to_datetime(dataform['SAMPLETIME'], errors='coerce')
        dataform['SAMPLETIME'] = dataform['SAMPLETIME'].dt.tz_localize('Asia/Shanghai')  # 设置为北京时间
    return dataform.to_json(orient='records', force_ascii=False)


@app.task
def get_check_report_detail_data(barcode: str):
    """ 根据 barcode 获取某个病人的某项检验报告的详细情况"""
    # 已去掉无用的字段
    sql = f"""
        SELECT ITEMNAME, RESULT, UNIT, FLAG, DOWNVALUE, UPVALUE
        FROM HNYZ_ZXYY.LIS_RESULT
        WHERE BARCODE = '{barcode}'
        AND ITEMNAME IS NOT NULL
        AND RESULT IS NOT NULL
    """

    dataFrame = db(None, None, sql, 'oracle')
    return dataFrame.to_json(orient='records', force_ascii=False)


@app.task
def get_bedding_patients_by_unionid(unionid: str):
    """ 根据 unionid 获取医生的管床患者信息 """
    sql = f"""
            SELECT ID
            FROM yzzxyy.info_user
            WHERE unionid = '{unionid}'
        """

    dataform = db(None, None, sql, 'mysql')
    if dataform.empty:
        return []
    work_id = dataform.iloc[0]['ID']
    """ 根据工号 id 相关科室病人的姓名、住院号、住院日期和编号获取 """
    dept_info = get_dept_name(work_id)
    if dept_info.empty:
        return []

    dept_codes = dept_info['DEPT_ID'].tolist()
    in_clause = ','.join([f"'{code}'" for code in dept_codes])

    sql = f"""   
            SELECT
            NAME,
            INPATIENT_NO,
            IN_DATE,
            LPAD(SUBSTR(a.BED_NO, LENGTH(a.NURSE_CELL_CODE) + 1, 5), 2, '0') AS NO,
            DEPT_NAME,
            CASE
                WHEN SEX_CODE = 'M' THEN '男'
                WHEN SEX_CODE = 'F' THEN '女'
                ELSE '未知'
            END AS SEX,
            TRUNC(MONTHS_BETWEEN(SYSDATE, BIRTHDAY) / 12) AS AGE,
            DIAG_NAME
        FROM HNYZ_ZXYY.FIN_IPR_INMAININFO a
        WHERE IN_STATE = 'I' AND DEPT_CODE IN ({in_clause})
        ORDER BY NO
    """
    dataform = db(None, None, sql, 'oracle')
    if dataform.empty:
        return []
    # 转换 SAMPLETIME 为 datetime 类型并添加时区
    if 'IN_DATE' in dataform.columns:
        dataform['IN_DATE'] = pandas.to_datetime(dataform['IN_DATE'], errors='coerce')
        dataform['IN_DATE'] = dataform['IN_DATE'].dt.tz_localize('Asia/Shanghai')  # 设置为北京时间
    return dataform.to_json(orient='records', force_ascii=False)


@app.task
def get_bedding_patients_by_unionid_paged_worker(unionid: str, query: str = None, dept_name: str = None, page: int = 1,
                                                 page_size: int = 10):
    """根据 unionid 获取医生的管床患者信息，支持模糊搜索和科室过滤"""
    # 1. 获取工号
    sql = f"""
        SELECT ID
        FROM yzzxyy.info_user
        WHERE unionid = '{unionid}'
    """
    dataform = db(None, None, sql, 'mysql')
    if dataform.empty:
        return []

    work_id = dataform.iloc[0]['ID']

    # 2. 获取该工号关联的科室
    dept_info = get_dept_name(work_id)
    if dept_info.empty:
        return []

    dept_codes = dept_info['DEPT_ID'].tolist()
    in_clause = ','.join([f"'{code}'" for code in dept_codes])

    # 3. 拼接动态 SQL，增加 query 和 dept_name 条件
    query_condition = ""
    if query:
        query_safe = query.replace("'", "''")  # 防注入
        query_condition = f"""
            AND (
                NAME LIKE '%{query_safe}%' OR
                INPATIENT_NO LIKE '%{query_safe}%' OR
                LPAD(SUBSTR(a.BED_NO, LENGTH(a.NURSE_CELL_CODE) + 1, 5), 2, '0') LIKE '%{query_safe}%'
            )
        """

    dept_name_condition = ""
    if dept_name:
        dept_name_safe = dept_name.replace("'", "''")  # 防注入
        dept_name_condition = f"AND DEPT_NAME = '{dept_name_safe}'"

    sql = f"""   
        SELECT
            NAME,
            INPATIENT_NO,
            IN_DATE,
            LPAD(SUBSTR(a.BED_NO, LENGTH(a.NURSE_CELL_CODE) + 1, 5), 2, '0') AS NO,
            DEPT_NAME,
            CASE
                WHEN SEX_CODE = 'M' THEN '男'
                WHEN SEX_CODE = 'F' THEN '女'
                ELSE '未知'
            END AS SEX,
            TRUNC(MONTHS_BETWEEN(SYSDATE, BIRTHDAY) / 12) AS AGE,
            DIAG_NAME
        FROM HNYZ_ZXYY.FIN_IPR_INMAININFO a
        WHERE IN_STATE = 'I'
          AND DEPT_CODE IN ({in_clause})
          {dept_name_condition}
          {query_condition}
        ORDER BY NO
    """

    # 构造 count_sql
    count_sql = f"""
        SELECT COUNT(*)
        FROM HNYZ_ZXYY.FIN_IPR_INMAININFO a
        WHERE IN_STATE = 'I'
          AND DEPT_CODE IN ({in_clause})
          {dept_name_condition}
          {query_condition}
    """

    df_paged = paged_query_with_sql(sql, count_sql, page, page_size, 'oracle')
    return df_paged


@app.task
def get_inspection_report(inpatient_no, page, page_size):
    """ 根据住院号 inpatient_no 获取某个病人的所有检查报告"""
    sql = f"""
          SELECT NAME             AS 姓名,
       CHECK_DATE       AS 检查时间,
       CHECK_NAME       AS 检查名称,
       CHECK_TYPE       AS 检查类型,
       CHECK_VIEW       AS 检查描述,
       REGEXP_REPLACE(
               REGEXP_REPLACE(CHECK_RESULT, '^\d+', ''),
               '^、', '1、'
       )                AS 检查结论,
       APPLY_DEPT_NAME  AS 申请科室,
       CONFIRM_DOC_NAME AS 申请人
        FROM HNYZ_ZXYY.TH_CHECK_RECORD
        WHERE TREATMENT_CODE = '{inpatient_no}'
    """
    df = db(None, None, sql, 'oracle')
    if df.empty:
        return {
            "page": page,
            "page_size": page_size,
            "total_count": 0,
            "total_pages": 0,
            "records": []
        }
    result = df.to_dict(orient='records')
    # result = paged_query(sql, page, page_size, 'oracle')
    return {
        "page": page,
        "page_size": page_size,
        "total_count": len(result),
        "total_pages": (len(result) + page_size - 1) // page_size,
        "records": result
    }


@app.task
def get_medical_order_worker(inpatient_no):
    """ 根据住院号 inpatient_no 获取某个病人临时医嘱单"""
    # 先从 info_sql 中取到查询临时医嘱单所需的 sql
    # todo 这里会有bug，需要单独把sql语句写死.
    sql = f"""
          SELECT TO_CHAR(MO_DATE, 'YYYY-MM-DD HH24:MI') AS MO_DATE,
                 TO_CHAR(TO_DATE(EXM_DATE, 'YYYY-MM-DD HH24:MI:SS'),
                         'MM-DD HH24:MI')               AS EXM_DATE,
                 (CASE
                      WHEN total_count = 1 THEN ''
                      WHEN row_num = 1 THEN '1' -- 首次出现
                      WHEN row_num = total_count THEN '3' -- 最后一次出现
                      ELSE '2' -- 中间出现
                     END)                               AS PREFIX,
                 CONTENT
          FROM (SELECT t.MO_DATE,
                       t.ITEM_NAME ||
                       CASE
                           WHEN t.BASE_DOSE != 0 THEN ' ' || CASE
                                                                 WHEN MOD(t.BASE_DOSE, 1) = 0
                                                                     THEN TO_CHAR(t.BASE_DOSE, 'FM9999') -- 整数
                                                                 ELSE TO_CHAR(t.BASE_DOSE, 'FM9990.9999') -- 小数
                               END
                           END ||
                       t.DOSE_UNIT ||
                       CASE WHEN t.FREQUENCY_CODE NOT IN ('遵医嘱', 'ONCE') THEN ' ' || t.FREQUENCY_CODE END ||
                       CASE WHEN t.USE_NAME IS NOT NULL THEN ' ' || t.USE_NAME END AS CONTENT,
                       t.COMB_NO,
                       t.SORT_ID,
                       s.EXM_DATE,
                       ROW_NUMBER()                                                   OVER (PARTITION BY t.COMB_NO ORDER BY t.SORT_ID) AS row_num, COUNT(*) OVER (PARTITION BY t.COMB_NO)                        AS total_count
                FROM HIT_APP.met_ipm_order t
                         JOIN HIT_APP.MET_IPM_ORDERPRINT s ON s.MO_ORDER = t.MO_ORDER
                WHERE t.TYPE_CODE = 'LZ'
                  AND t.SUBTBL_FLAG = '0'
                  AND t.INPATIENT_NO = '{inpatient_no}'
                ORDER BY t.COMB_NO, t.SORT_ID)
          ORDER BY MO_DATE, COMB_NO, SORT_ID 
          """

    df = db(None, None, sql, 'oracle')
    if df.empty:
        return []

    prefix_map = {
        '1': '⌈ ',
        '2': '| ',
        '3': '⌊ '
    }

    # 添加 ITEM_NAME 列，根据 PREFIX 拼接前缀
    df['ITEM_NAME'] = df.apply(lambda row: prefix_map.get(row['PREFIX'], '') + str(row['CONTENT']), axis=1)

    # 删除原来的 PREFIX 和 CONTENT 列
    df.drop(columns=['PREFIX', 'CONTENT'], inplace=True)

    return df.to_dict(orient='records')


def get_long_term_medical_order_worker(inpatient_no):
    """
    获取长期医嘱单
    """

    sql = """

    """
