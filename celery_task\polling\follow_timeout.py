from celery_task.celery import app
from custom.db.execute import db
from .method import get_next_time, get_follow_code
import custom.settins
import json
import re
import pandas as pd
from datetime import datetime


# 回访到期时预警
@app.task
def start(test_model=False):
    # 1. 一次性查询所有到期任务
    sql = f'''SELECT ID, INPATIENT_NO, APPELLATION, ID_CARD, TEL, INDEX_ID, TO_HOUR, CYCLE, STATE, CONTENT, COMPLETE, CYCLE_TIMES, HOURS
                FROM yzzxyy.follow_plan 
                WHERE DONE = 0 AND STATE < 5 AND NEXT_TIME <= NOW()'''
    data_timeout = db(None, None, sql, 'mysql')
    if data_timeout.empty:
        return

    # 2. 批量获取所有需要的外部信息
    all_inpatient_nos = data_timeout['INPATIENT_NO'].unique().tolist()
    all_plan_ids = data_timeout['ID'].unique().tolist()

    # 批量获取主诊疗及首页信息
    main_info = pd.DataFrame()
    if all_inpatient_nos:
        inpatient_nos_str = "', '".join(all_inpatient_nos)
        main_info = db(
            None, None,
            f"""SELECT f.INPATIENT_NO,
                                   f.NAME,
                                   f.OUT_DATE,
                                   f.DEPT_NAME,
                                   f.IDENNO,
                                   f.DEPT_CODE,
                                   f.HOUSE_DOC_CODE,
                                   f.HOUSE_DOC_NAME,
                                   f.CHIEF_DOC_CODE,
                                   f.CHIEF_DOC_NAME,
                                   COALESCE(h.PRESENT_TEL, f.HOME_TEL, f.WORK_TEL)    AS PRESENT_TEL,
                                   COALESCE(h.CONTACT_TEL, f.LINKMAN_TEL, f.WORK_TEL) AS CONTACT_TEL,
                                   COALESCE(h.CERTIFICATE_NO, f.IDENNO)               AS CERTIFICATE_NO
                            FROM HNYZ_ZXYY.FIN_IPR_INMAININFO f
                                     LEFT JOIN NMRWS.NMRWS_MR_HOMEPAGE h ON h.INPATIENT_NO = f.INPATIENT_NO
                            WHERE f.INPATIENT_NO IN ('{inpatient_nos_str}')"""
            , 'oracle')

    # 3. 将外部信息合并到主DataFrame
    data_merged = data_timeout.merge(main_info, on='INPATIENT_NO', how='left', suffixes=('', '_main'))

    # 定义用于存储数据库变更操作的容器
    final_plan_updates = {}  # {5: {'done_sql': 'update yzzxyy.follow_plan set DONE = 1 where ID = 5'}}
    notice_to_insert = []  # [{'ID_CARD': '******************', 'PATIENT_NAME': '汪家林', 'TEL': '["13787621576"]', 'TYPE': 'auto'}]
    tasks_to_insert = []  # ["('ZY010013966692', '28344', '刘霑', '28344', '刘霑', 5, 3, '2108')"]
    dsms_to_upsert = []  # [{'chief': False, 'follow_code': '28344', 'voip': False, 'warn_level': 0}]

    # 4. 非人工STATE分组处理
    # --- 处理 STATE 0 ---
    state0_group = data_merged[data_merged['STATE'] == 0].copy()
    if not state0_group.empty:
        s0_updates, s0_notice = _process_state_0_batch(state0_group)
        final_plan_updates.update(s0_updates)
        notice_to_insert.extend(s0_notice)

    # --- 处理 STATE 1 ---
    state1_group = data_merged[data_merged['STATE'] == 1].copy()
    if not state1_group.empty:
        s1_updates, s1_tasks, s1_dsms = _process_state_1_batch(state1_group)
        final_plan_updates.update(s1_updates)
        tasks_to_insert.extend(s1_tasks)
        dsms_to_upsert.extend(s1_dsms)

    # 5. 人工STATE分组处理
    # 获取人工随访任务中完成状态的任务
    artificial_info = pd.DataFrame()
    if all_plan_ids:
        plan_ids_str = ", ".join(map(str, all_plan_ids))
        artificial_sql = f"""SELECT DISTINCT PLAN_ID ID, DEDICATED_CODE
                             FROM yzzxyy.follow_task_artificial
                             WHERE PLAN_ID IN ({plan_ids_str}) AND STATE NOT IN (2, 3, 4)"""  # 不是234说明还没完成
        artificial_info = db(None, None, artificial_sql, 'mysql')

    # --- 处理 STATE 大于1（已派发人工任务），artificial_info找不到的（已处理） ---
    higher_states_group = pd.DataFrame()
    state_gt1_group = data_merged[data_merged['STATE'] > 1].copy()
    if not artificial_info.empty:  # 没完成的
        existing_plan_ids = artificial_info['ID'].tolist()
        higher_states_group = state_gt1_group[state_gt1_group['ID'].isin(existing_plan_ids)]
        state_gt1_group = state_gt1_group[~state_gt1_group['ID'].isin(existing_plan_ids)]
    for _, row in state_gt1_group.iterrows():
        final_plan_updates[row['ID']] = {'done_sql': complete_stage(row)}
    if not higher_states_group.empty:
        higher_states_group = higher_states_group.merge(artificial_info, on='ID', how='left')
        s_higher_updates, s_higher_dsms = _process_states_higher_batch(higher_states_group)
        final_plan_updates.update(s_higher_updates)
        dsms_to_upsert.extend(s_higher_dsms)

    # 6. 统一批量执行数据库写入
    # 批量更新 a.yzzxyy.follow_plan
    if final_plan_updates:
        # 分离需要特殊处理的 'done_sql'
        done_sqls = [v['done_sql'] for v in final_plan_updates.values() if 'done_sql' in v]
        for sql_to_run in done_sqls:
            if not test_model:
                db(None, None, sql_to_run, 'mysql')  # 单独执行

        # 聚合普通更新
        update_groups = {}
        for plan_id, updates in final_plan_updates.items():
            if 'done_sql' in updates: continue
            set_clause = ", ".join([f"{k} = {v}" for k, v in updates.items()])
            if set_clause not in update_groups:
                update_groups[set_clause] = []
            update_groups[set_clause].append(str(plan_id))

        for set_clause, ids in update_groups.items():
            ids_str = ", ".join(ids)
            if not test_model:
                db(None, None,
                   f"UPDATE yzzxyy.follow_plan SET {set_clause} WHERE ID IN ({ids_str})", 'mysql')

    # 批量写入 yzzxyy.follow_psms_history
    if notice_to_insert:
        values_history = []
        values_notice = []
        for sms in notice_to_insert:
            # 防止SQL注入，对字符串值进行基本转义
            patient_name = str(sms['PATIENT_NAME']).replace("'", "")
            content = str(sms['CONTENT']).replace("'", "")
            complete = str(sms['COMPLETE']).replace("'", "")
            tel = str(sms['TEL']).replace("'", "")
            values_history.append(f"('{patient_name}', '{sms['ID_CARD']}', '{content}', '{sms['DEPT_CODE']}', '{tel}')")
            values_notice.append(f"('{tel}', '{complete}'")
        if values_history and not test_model:
            db(None, None,
               f"""INSERT INTO yzzxyy.follow_psms_history (PATIENT_NAME, ID_CARD, CONTENT, DEPT_CODE, TEL) 
                              VALUES {', '.join(values_history)}""", 'mysql')  # 写入历史
            db(None, None,
               f"""INSERT INTO yzzxyy.follow_dsms_notice (TEL, COMPLETE) 
                              VALUES {', '.join(values_notice)}""", 'mysql')  # 写入通知队列

    # 批量写入 yzzxyy.follow_task_artificial （新增人工随访）
    if tasks_to_insert and not test_model:
        db(None, None,
           f"""INSERT INTO yzzxyy.follow_task_artificial 
                           (INPATIENT_NO, HOUSE_DOC_CODE, HOUSE_DOC_NAME, DEDICATED_CODE, DEDICATED_NAME, PLAN_ID, CONTENT_ID, DEPT_CODE)
                           VALUES {', '.join(tasks_to_insert)}"""
           , 'mysql')

    # 批量写入/更新 yzzxyy.follow_dsms
    if dsms_to_upsert:
        chief_inserts = []
        non_chief_updates = {}  # {code: {warn_level: [], voip: [], num: 0}}
        for notice in dsms_to_upsert:
            if notice['chief']:
                chief_inserts.append(
                    f"('{notice['inpatient_no']}', '{notice['code']}', 1, {notice['warn_level']})")
            else:
                code = notice['code']
                if code not in non_chief_updates:
                    non_chief_updates[code] = {'warn_levels': [], 'voips': [], 'num': 0}
                non_chief_updates[code]['warn_levels'].append(notice['warn_level'])
                non_chief_updates[code]['voips'].append(notice['voip'])
                non_chief_updates[code]['num'] += 1

        # 处理通知主任
        if chief_inserts and not test_model:
            db(None, None,
               f"""INSERT INTO yzzxyy.follow_dsms (INPATIENT_NO, CODE, CHIEF, WARN_LEVEL)
                                VALUES {', '.join(chief_inserts)}""", 'mysql')

        # 处理通知医生/专员 (UPSERT)
        if non_chief_updates:
            values_to_upsert = []
            for code, data in non_chief_updates.items():
                max_warn_level = max(data['warn_levels'])
                is_voip = int(any(data['voips']))
                num = data['num']
                values_to_upsert.append(f"('{code}', {num}, {max_warn_level}, {is_voip})")

            if not test_model:
                db(None, None,
                   f"""INSERT INTO yzzxyy.follow_dsms (CODE, NUM, WARN_LEVEL, VOIP)
                                     VALUES {', '.join(values_to_upsert)}
                                     ON DUPLICATE KEY UPDATE 
                                        NUM = NUM + VALUES(NUM),
                                        WARN_LEVEL = GREATEST(WARN_LEVEL, VALUES(WARN_LEVEL)),
                                        VOIP = GREATEST(VOIP, VALUES(VOIP))""", 'mysql')

    if not test_model:
        end_time = str(datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        sql = f'''UPDATE yzzxyy.info_db SET value = '{end_time}' WHERE index_information = 'follow_timeout_time' '''
        db(None, None, sql, 'mysql')


def _hospitalization_actions(group: pd.DataFrame):
    """ 检测是否有住院行为 """
    readmitted_patients = pd.DataFrame()
    if not group.empty:
        conditions = []
        for _, row in group.dropna(subset=['IDENNO', 'DEPT_CODE']).iterrows():
            out_date_str = row['OUT_DATE'].strftime('%Y-%m-%d %H:%M:%S')
            conditions.append(
                f"""(IDENNO = '{row['IDENNO']}' AND DEPT_CODE = '{row['DEPT_CODE']}' AND IN_DATE > TO_DATE('{out_date_str}', 'YYYY-MM-DD HH24:MI:SS'))""")
        if conditions:
            readmitted_patients = db(None, None,
                                     f"""SELECT DISTINCT IDENNO, DEPT_CODE FROM HNYZ_ZXYY.FIN_IPR_INMAININFO
                                                    WHERE {' OR '.join(list(set(conditions)))}"""
                                     , 'oracle')

    # 标记已完成和需继续处理的任务
    if not readmitted_patients.empty:
        group['is_readmitted'] = group.apply(
            lambda row: ((row['IDENNO'], row['DEPT_CODE']) in zip(readmitted_patients['IDENNO'],
                                                                  readmitted_patients['DEPT_CODE'])),
            axis=1)
    else:
        group['is_readmitted'] = False

    plans_to_complete = group[group['is_readmitted']].drop(columns=['is_readmitted'])
    plans_to_process = group[~group['is_readmitted']].drop(columns=['is_readmitted'])
    return plans_to_complete, plans_to_process


def _registration_actions(group: pd.DataFrame):
    """ 检测是否有挂号行为 """
    readmitted_patients = pd.DataFrame()
    if not group.empty:
        conditions = []
        for _, row in group.iterrows():
            id_card = row['CERTIFICATE_NO'] if pd.notna(row['CERTIFICATE_NO']) else row['ID_CARD']
            interval_hours = (custom.settins.key_patients_waiting_time_for_execution +
                              custom.settins.effective_registration_time_before_reminding_key_patients) * 24
            conditions.append(
                f"""(IDENNO = '{id_card}' AND REG_DATE >= CURRENT_TIMESTAMP - INTERVAL '{interval_hours}' HOUR(4))""")
        if conditions:
            readmission_sql = f"""SELECT DISTINCT IDENNO, DEPT_CODE FROM HNYZ_ZXYY.FIN_OPR_REGISTER WHERE TRANS_TYPE = 1 AND ({' OR '.join(conditions)})"""
            readmitted_patients = db(None, None, readmission_sql, 'oracle')

    # 标记已完成和需继续处理的任务
    if not readmitted_patients.empty:
        group['is_readmitted'] = group.apply(
            lambda row: ((row['IDENNO']) in zip(readmitted_patients['IDENNO'],
                                                readmitted_patients['DEPT_CODE'])),
            axis=1)
    else:
        group['is_readmitted'] = False

    plans_to_complete = group[group['is_readmitted']].drop(columns=['is_readmitted'])
    plans_to_process = group[~group['is_readmitted']].drop(columns=['is_readmitted'])
    return plans_to_complete, plans_to_process


def _process_state_0_batch(group: pd.DataFrame):
    """批量处理STATE=0的任务，state为0且index为0的为自动任务，否则就是重点病人的首次通知任务 """
    # 批量检测是否有住院行为
    plans_to_complete, plans_to_process = _hospitalization_actions(group)

    # 对于未重复入院的，处理电话更新和短信通知
    notice_to_insert = []  # 方案提醒通知
    plans_to_update = {}
    # 对于已入院的，准备完成任务
    for _, row in plans_to_complete.iterrows():
        plans_to_update[row['ID']] = {'done_sql': complete_stage(row)}

    # 对于未入院的，继续处理
    for _, row in plans_to_process.iterrows():
        # 更新电话号码
        tel_set = set(json.loads(row.get('TEL', '[]')))
        pattern = r'^1\d{10}$'
        if row.get('PRESENT_TEL') and re.match(pattern, str(row['PRESENT_TEL'])):
            tel_set.add(str(row['PRESENT_TEL']))
        if row.get('CONTACT_TEL') and re.match(pattern, str(row['CONTACT_TEL'])):
            tel_set.add(str(row['CONTACT_TEL']))

        tel = row['TEL']
        update_data = {}
        if tel_set:
            new_tel_json = json.dumps(list(tel_set))
            if new_tel_json != row['TEL']:
                update_data['TEL'] = f"'{new_tel_json}'"
                tel = new_tel_json

        if row['INDEX_ID'] != '0' and tel_set:  # 重点病人的方案通知
            # PATIENT_NAME, ID_CARD, CONTENT, DEPT_CODE, TEL
            try:
                time_str = f"按{row['OUT_DATE'].strftime('%Y年%m月%d日')}"
            except Exception:
                time_str = '按'

            content = (f'尊敬的{row["NAME"]}{row["APPELLATION"]}您好，这里是永州市中心医院智能随访提醒电话。'
                       f"{time_str}{row['DEPT_NAME']}出院随访计划，"
                       f"您的主管医生{row['HOUSE_DOC_NAME']}提醒您。{row['COMPLETE']}。目前已到计划时间，请及时完成随访计划。祝您身体健康")
            notice_to_insert.append({
                'PATIENT_NAME': row['NAME'],
                'ID_CARD': row['IDENNO'],
                'CONTENT': row['COMPLETE'],
                'COMPLETE': content,
                'DEPT_CODE': row['DEPT_CODE'],
                'TEL': tel})
            # 计算下一阶段时间并准备更新
            next_time = get_next_time(row['TO_HOUR'] == '1', datetime.now(),
                                      custom.settins.key_patients_waiting_time_for_execution * 24)
            update_data.update(
                {'STATE': 1,
                 'NEXT_TIME': f"'{next_time}'"}
            )
            plans_to_update[row['ID']] = update_data
        elif row['INDEX_ID'] == '0' and tel_set:  # 重点病人的自动回访通知
            try:
                time_str = f"按{row['OUT_DATE'].strftime('%Y年%m月%d日')}"
            except Exception:
                time_str = '按'
            content = (f'尊敬的{row["NAME"]}{row["APPELLATION"]}您好，这里是永州市中心医院智能随访提醒电话。'
                       f"{time_str}{row['DEPT_NAME']}出院随访计划，"
                       f"智能随访系统将在计划设定时间给予您提醒。如有任何疑问可拨打8899120咨询。祝您身体健康")
            notice_to_insert.append({
                'PATIENT_NAME': row['NAME'],
                'ID_CARD': row['IDENNO'],
                'CONTENT': '自动随访提醒',
                'COMPLETE': content,
                'DEPT_CODE': row['DEPT_CODE'],
                'TEL': tel})
            plans_to_update[row['ID']] = {'done_sql': complete_stage(row)}  # 自动完结任务

    return plans_to_update, notice_to_insert


def _process_state_1_batch(group: pd.DataFrame):
    """批量处理STATE=1的任务"""
    dsms_to_upsert = []
    plans_to_update = {}
    tasks_to_insert = []

    # 批量检测是否有住院行为
    plans_to_complete, plans_to_process = _hospitalization_actions(group)
    for _, row in plans_to_complete.iterrows():
        plans_to_update[row['ID']] = {'done_sql': complete_stage(row)}

    # 批量检测是否有有效挂号行为
    plans_to_complete, plans_to_process = _registration_actions(plans_to_process)
    for _, row in plans_to_complete.iterrows():
        plans_to_update[row['ID']] = {'done_sql': complete_stage(row)}

    # 准备派发人工任务
    '''
    如果不存在时间范围内有效挂号，则派发回访任务
    拆分出follow_task_artificial的目的是因为follow_plan是周期性的大的回访方案
    具体单次人工回访细节，记录在拆分出的follow_task_artificial中。
    '''
    for _, row in plans_to_process.iterrows():
        follow_code, follow_name = get_follow_code(row['DEPT_CODE'],
                                                   row['HOUSE_DOC_CODE'],
                                                   row['HOUSE_DOC_NAME'])  # 回访人工号
        # 准备人工任务表插入
        tasks_to_insert.append(f"('{row['INPATIENT_NO']}', '{row['HOUSE_DOC_CODE']}', '{row['HOUSE_DOC_NAME']}', "
                               f"'{follow_code}', '{follow_name}', {row['ID']}, 3, '{row['DEPT_CODE']}')")

        # 准备医生通知 (notice)
        dsms_to_upsert.append({'warn_level': 0, 'code': follow_code, 'voip': False, 'chief': False})

        # 准备更新计划
        next_time = get_next_time(False, datetime.now(), int(custom.settins.timeout_reminder * 24))
        update_data = {'STATE': 2, 'NEXT_TIME': f"'{next_time}'"}
        if pd.notna(row['CERTIFICATE_NO']) and row['IDENNO'] != row['CERTIFICATE_NO']:
            update_data['ID_CARD'] = f"'{row['CERTIFICATE_NO']}'"
        plans_to_update[row['ID']] = update_data

    return plans_to_update, tasks_to_insert, dsms_to_upsert


def _process_states_higher_batch(group: pd.DataFrame):
    """批量处理STATE >= 2 的任务"""
    plans_to_update = {}
    dsms_to_upsert = []
    # 对未完成的，进行升级处理
    for _, row in group.iterrows():
        state = row['STATE']
        follow_code = row['DEDICATED_CODE']
        if state == 2:
            dsms_to_upsert.append(
                {'warn_level': 1, 'code': follow_code, 'voip': False, 'chief': False})
            next_time = get_next_time(False, datetime.now(), custom.settins.serious_timeout_reminder * 24)
            plans_to_update[row['ID']] = {'STATE': 3, 'NEXT_TIME': f"'{next_time}'"}
        elif state == 3:
            dsms_to_upsert.append(
                {'warn_level': 2, 'code': follow_code, 'voip': True, 'chief': False})
            next_time = get_next_time(False, datetime.now(), int(custom.settins.director_reminder * 24))
            plans_to_update[row['ID']] = {'STATE': 4, 'NEXT_TIME': f"'{next_time}'"}
        elif state == 4:
            # 语音通知医生
            dsms_to_upsert.append(
                {'warn_level': 3, 'code': follow_code, 'voip': True, 'chief': False})
            # 消息通知主任
            dsms_to_upsert.append({
                'inpatient_no': row['INPATIENT_NO'],
                'code': row['CHIEF_DOC_CODE'],
                'warn_level': 3,
                'chief': True
            })
            plans_to_update[row['ID']] = {'STATE': 5}

    return plans_to_update, dsms_to_upsert


# 完成本阶段随访任务
def complete_stage(row, artificials=False):
    """artificials 询问是否已经进入人工随访计划，以便更新follow_task_artificial"""
    if row['CYCLE'] == '0' or row['CYCLE_TIMES'] >= custom.settins.number_of_cycles:  # 非周期或达到最大周期数，算作完成随访。
        return f'''update yzzxyy.follow_plan set DONE = 1 where ID = {row['ID']}'''
    else:  # 周期内，更新下次随访时间
        if artificials:
            sqls = f'''update yzzxyy.follow_task_artificial set STATE = 0 where ID = {row['ID']} '''
            db(None, None, sqls, 'mysql')
        cycle_time = row['CYCLE_TIMES'] + 1
        next_times = get_next_time(row['TO_HOUR'] == '1', row['OUT_DATE'],
                                   int((cycle_time + 1) * row['HOURS']))
        return f'''update yzzxyy.follow_plan set CYCLE_TIMES = {cycle_time}, NEXT_TIME = '{next_times}' 
                            where ID = {row['ID']}'''
