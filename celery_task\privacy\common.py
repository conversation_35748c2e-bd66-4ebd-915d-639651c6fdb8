import ast
import base64
import json
import urllib.parse
import re
import requests

from Crypto.Cipher import DES3
from Crypto.Util.Padding import pad, unpad
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

from custom.db.mysql import UsingMysql

with UsingMysql() as um:
    privacy = ast.literal_eval(
        um.fetch_decrypt("select value from info_db where index_information = 'privacy' ",
                         None, 'value'))

IV = bytes([0x12, 0x34, 0x56, 0x78, 0x90, 0xAB, 0xCD, 0xEF])
privacy = {'appkey': 'c25161fd3930442b853905b780552618',
           'appsecret': '700e35b0e04746b0b536a97cf6cc32bb',
           'entId': '1748749255876017401',
           'secret_key': '183011650110499994876335',
           'base_url': 'http://*************:9082'}

MAX_RETRIES = 3  # 最大重试次数
BACKOFF_FACTOR = 0.5  # 指数退避因子

TIMEOUT = (3.05, 27)  # 连接超时3.05s，读取超时27s
SSL_VERIFY = True  # 生产环境必须验证SSL证书
CA_BUNDLE = True  # CA证书路径 '/path/to/cert.pem'（或设置为True使用系统默认）


def encrypt_3des(plain_text: str) -> str:
    """
    3DES加密（带URL编码处理）
    :param plain_text: 待加密文本
    :return: URL编码后的Base64字符串
    """
    if len(privacy['secret_key']) != 24:
        raise ValueError("密钥长度必须为24字节")
    # 创建加密器（secret_key需转为bytes类型，CBC模式，iv为固定初始化向量）
    cipher = DES3.new(privacy['secret_key'].encode(), DES3.MODE_CBC, iv=IV)
    # 数据填充处理（明文转为UTF-8编码的bytes，采用PKCS7填充标准）
    padded_data = pad(plain_text.encode('utf-8'), 8, style='pkcs7')
    # 填充后的数据进行CBC模式加密
    encrypted_data = cipher.encrypt(padded_data)
    # 数据Base64编码并将结果转为字符串
    return base64.b64encode(encrypted_data).decode('utf-8')


def decrypt_3des(encrypted_str: str) -> str:
    """
    3DES解密（带URL解码处理）
    :param encrypted_str: URL编码后的加密字符串
    :return: 解密后的明文
    """
    # URL解码并Base64解码
    decoded_str = urllib.parse.unquote(encrypted_str)
    encrypted_data = base64.b64decode(decoded_str)
    # 创建解密器
    cipher = DES3.new(privacy['secret_key'].encode(), DES3.MODE_CBC, iv=IV)
    # 解密并去除填充
    decrypted_data = cipher.decrypt(encrypted_data)
    return unpad(decrypted_data, 8, style='pkcs7').decode('utf-8')


def create_retry_session():
    session = requests.Session()
    retry = Retry(total=MAX_RETRIES,
                  backoff_factor=BACKOFF_FACTOR,
                  status_forcelist=[500, 502, 503, 504],
                  allowed_methods=['POST', 'GET'])
    adapter = HTTPAdapter(pool_connections=50, pool_maxsize=50, max_retries=retry)
    session.mount('https://', adapter)
    return session


def recognize_text(base64_image: str):
    """ 验证码识别
    用法：recognize_text(cv.imread(r'5.jpg')) """
    session = create_retry_session()
    try:
        payload = {"image": base64_image,
                   "token": "hRqFGnKzIgclaSH0xZTZV6ki7RHtkW7HJhwx2Xl8nqA",  # 替换你的验证token
                   "type": "10110"}

        response = session.post('http://api.jfbym.com/api/YmServer/customApi',
                                json=payload,
                                headers={"Content-Type": "application/json"},
                                verify=CA_BUNDLE if SSL_VERIFY else False,  # SSL证书验证
                                timeout=TIMEOUT)
        response.raise_for_status()  # 自动触发HTTP错误异常
        try:
            json_response = response.json()
            msg = json_response.get('msg', '')
            if json_response.get('code', '') == 10000:
                return {'success': True, 'data': response.json().get('data', {}).get('data', ''), 'msg': msg}
            else:
                return {'success': False, 'msg': msg}
        except json.JSONDecodeError:
            return {'success': False, 'msg': '响应包含无效的JSON格式'}

    except requests.exceptions.SSLError as e:
        return {'success': False, 'msg': 'SSL证书验证失败'}
    except requests.exceptions.ConnectionError as e:
        return {'success': False, 'msg': f"连接失败: {str(e)}"}
    except requests.exceptions.Timeout as e:
        return {'success': False, 'msg': f"请求超时: {str(e)}"}
    except requests.exceptions.HTTPError as e:
        return {'success': False, 'msg': f"HTTP错误: {str(e)}"}
    except requests.exceptions.RequestException as e:
        return {'success': False, 'msg': f"请求异常: {str(e)}"}
    except Exception as e:
        return {'success': False, 'msg': f"未知异常: {str(e)}"}
    finally:
        session.close()


def generate_batch_insert(data, table_name, unique=None, batch_size=50):
    """生成分批插入SQL语句（支持ON DUPLICATE KEY UPDATE）
    :param data: 原始数据列表
    :param table_name: 表名
    :param unique: 唯一索引列名（支持字符串或列表）
    :param batch_size: 每批插入数量 默认50
    :return: 包含多个INSERT语句的列表
    """
    if not data:
        return []

    # 处理字段名
    columns = [f'`{k}`' for k in data[0].keys()]

    # 构建UPDATE子句
    update_clause = ""
    if unique:
        update_fields = [f"`{col}` = VALUES(`{col}`)" for col in data[0].keys() if col != unique]
        update_clause = f"\nON DUPLICATE KEY UPDATE {', '.join(update_fields)}"

    sql_batches = []
    values_batch = []
    for index, item in enumerate(data, 1):
        # 值处理
        values = []
        for key, value in item.items():
            if isinstance(value, str):
                # 处理单引号和特殊字符
                sanitized = value.replace("'", "''")
                values.append(f"'{sanitized}'")
            elif value is None:
                values.append('NULL')
            elif isinstance(value, (int, float)):
                values.append(str(value))
            else:
                values.append(f"'{str(value)}'")

        values_batch.append(f"({', '.join(values)})")

        # 达到批次数量或最后一条
        if index % batch_size == 0 or index == len(data):
            sql = (
                f"INSERT INTO {table_name} ({', '.join(columns)})\n"
                f"VALUES {', '.join(values_batch)}"
                f"{update_clause};\n"  # 添加更新子句
            )
            sql_batches.append(sql)
            values_batch = []

    return sql_batches
