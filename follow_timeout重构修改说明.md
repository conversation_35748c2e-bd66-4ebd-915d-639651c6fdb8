# follow_timeout.py 重构修改说明

## 概述
本文档详细说明了在数据库重构后，对 `celery_task/polling/follow_timeout.py` 文件进行的重构，以适配新的数据库结构。由于新数据库结构的变化较大，创建了全新的重构版本 `follow_timeout_refactored.py`。

## 主要变化

### 1. 数据库表结构适配

#### 原始查询逻辑
```sql
-- 老版本查询
SELECT ID, INPATIENT_NO, APPELLATION, ID_CARD, TEL, INDEX_ID, TO_HOUR, CYCLE, STATE, CONTENT, COMPLETE, CYCLE_TIMES, HOURS
FROM yzzxyy.follow_plan 
WHERE DONE = 0 AND STATE < 5 AND NEXT_TIME <= NOW()
```

#### 重构后查询逻辑
```sql
-- 新版本查询
SELECT p.ID, p.INPATIENT_NO, p.NEXT_ACTION_TIME, p.<PERSON>Y<PERSON><PERSON>, p.HOURS, p.CONTENT,
       p.DEPT_CODE, p.HOUSE_DOC_CODE, p.HOUSE_DOC_NAME,
       s.STATE, s.DOWN, s.NEXT_TIME
FROM follow.plan p
LEFT JOIN follow.state s ON s.FOLLOW_PLAN_ID = p.ID
WHERE (s.DOWN = 0 OR s.DOWN IS NULL) 
  AND (s.NEXT_TIME <= NOW() OR p.NEXT_ACTION_TIME <= NOW())
```

### 2. 状态管理重构

#### 老版本状态管理
- 状态直接存储在 `follow_plan` 表的 `STATE` 字段中
- 使用数字表示状态：0, 1, 2, 3, 4, 5
- 完成状态通过 `DONE` 字段标识

#### 新版本状态管理
- 状态存储在独立的 `follow.state` 表中
- 使用枚举值：'待处理', '通知', '任务生成', '超时', '二次超时', '严重超时'
- 完成状态通过 `DOWN` 字段标识

### 3. 字段映射变化

| 功能 | 老字段 | 新字段 | 说明 |
|------|--------|--------|------|
| 下次随访时间 | `NEXT_TIME` | `NEXT_ACTION_TIME` (plan表) + `NEXT_TIME` (state表) | 分离存储 |
| 患者姓名 | `NAME` | ❌ 移除 | 需从HIS系统查询 |
| 患者称呼 | `APPELLATION` | ❌ 移除 | 需从HIS系统查询 |
| 身份证号 | `ID_CARD` | ❌ 移除 | 需从HIS系统查询 |
| 电话号码 | `TEL` | ❌ 移除 | 需从HIS系统查询 |
| 计划序号 | `INDEX_ID` | ❌ 移除 | 不再使用 |
| 精确到小时 | `TO_HOUR` | ❌ 移除 | 通过业务逻辑判断 |
| 完整内容 | `COMPLETE` | ❌ 移除 | 使用CONTENT字段 |
| 周期次数 | `CYCLE_TIMES` | ❌ 移除 | 需要新的逻辑处理 |
| 完成状态 | `DONE` | `DOWN` (state表) | 字段名变更 |
| 状态 | `STATE` | `STATE` (state表) | 移至独立表 |

### 4. 核心函数重构

#### `get_follow_code` 函数适配
```python
# 老版本
def get_follow_code(dept_code, house_doc_code, house_doc_name):
    dedicated = db('DEDICATED_CODE', 'yzzxyy.follow_dedicated', {'DEPT_CODE': dept_code}, 'mysql')
    # ...

# 新版本
def get_follow_code_refactored(dept_code, house_doc_code, house_doc_name):
    dedicated = db('DEDICATED_CODE', 'follow.dedicated', {'DEPT_CODE': dept_code}, 'mysql')
    # ...
```

#### 状态处理函数重构
- `_process_pending_state()` - 处理待处理状态
- `_process_notification_state()` - 处理通知状态  
- `_process_task_generated_state()` - 处理任务生成状态
- `_process_timeout_state()` - 处理超时状态
- `_process_second_timeout_state()` - 处理二次超时状态

### 5. 通知历史记录适配

#### 老版本
```python
# 写入 yzzxyy.follow_psms_history
INSERT INTO yzzxyy.follow_psms_history (PATIENT_NAME, ID_CARD, CONTENT, DEPT_CODE, TEL) 
VALUES ...
```

#### 新版本
```python
# 写入 follow.notice_history
INSERT INTO follow.notice_history (INPATIENT_NO, DEDICATED_CODE, NOTICE_TYPE, CONTENT, UPDATE_TIME) 
VALUES ...
```

## 无法完全用新数据库复现的功能

### 1. 人工任务管理
**问题**: 新数据库中没有对应 `follow_task_artificial` 的表结构。

**当前解决方案**: 继续使用老的 `yzzxyy.follow_task_artificial` 表。

**代码示例**:
```python
# 仍需使用老表
sql = f"""INSERT INTO yzzxyy.follow_task_artificial 
          (INPATIENT_NO, HOUSE_DOC_CODE, HOUSE_DOC_NAME, DEDICATED_CODE, DEDICATED_NAME, PLAN_ID, CONTENT_ID, DEPT_CODE)
          VALUES ('{row['INPATIENT_NO']}', '{row['HOUSE_DOC_CODE']}', '{row['HOUSE_DOC_NAME']}', 
                  '{follow_code}', '{follow_name}', {row['ID']}, 3, '{row['DEPT_CODE']}')"""
```

### 2. 周期性随访管理
**问题**: 新数据库移除了 `CYCLE_TIMES` 字段，无法跟踪周期随访的完成次数。

**影响**: 
- 无法判断周期随访是否达到最大次数
- 无法计算下次周期随访时间

**建议**: 在 `follow.state` 表中增加周期次数字段，或创建专门的周期跟踪表。

### 3. 患者基本信息缓存
**问题**: 新表不存储患者姓名、电话等基本信息，每次都需要从HIS系统查询。

**影响**: 
- 增加了数据库查询负担
- 可能影响性能

**当前解决方案**: 每次从 `HNYZ_ZXYY.FIN_IPR_INMAININFO` 和 `NMRWS.NMRWS_MR_HOMEPAGE` 查询。

### 4. 医生短信通知
**问题**: 新数据库中没有对应 `follow_dsms` 的表结构。

**当前解决方案**: 继续使用老的 `yzzxyy.follow_dsms` 表进行医生通知。

## 重构优势

### 1. 数据结构更清晰
- 随访计划和状态分离，职责更明确
- 枚举状态比数字状态更易理解

### 2. 扩展性更好
- 状态表可以独立扩展
- 通知历史记录更规范

### 3. 数据一致性
- 减少了数据冗余
- 提高了数据的规范性

## 实施建议

### 1. 分阶段实施
1. **第一阶段**: 使用重构版本处理新的随访计划
2. **第二阶段**: 逐步迁移现有数据
3. **第三阶段**: 完全切换到新版本

### 2. 数据迁移脚本
需要编写数据迁移脚本，将老表数据转换到新表结构：
```sql
-- 示例迁移脚本
INSERT INTO follow.plan (NEXT_ACTION_TIME, INPATIENT_NO, CYCLE, HOURS, CONTENT, ...)
SELECT NEXT_TIME, INPATIENT_NO, CYCLE, HOURS, CONTENT, ...
FROM yzzxyy.follow_plan
WHERE ...;

INSERT INTO follow.state (FOLLOW_PLAN_ID, STATE, DOWN, NEXT_TIME)
SELECT ID, 
       CASE STATE 
         WHEN 0 THEN '待处理'
         WHEN 1 THEN '通知'
         WHEN 2 THEN '任务生成'
         WHEN 3 THEN '超时'
         WHEN 4 THEN '二次超时'
         WHEN 5 THEN '严重超时'
       END,
       DONE,
       NEXT_TIME
FROM yzzxyy.follow_plan;
```

### 3. 补充表设计
建议在新数据库中补充以下表：
1. **任务管理表** - 替代 `follow_task_artificial`
2. **医生通知表** - 替代 `follow_dsms`
3. **周期跟踪表** - 管理周期随访次数

## 总结

重构后的版本更好地适配了新的数据库结构，提高了代码的可维护性和扩展性。但由于新数据库设计还不完善，部分功能仍需依赖老表。建议采用渐进式迁移策略，确保系统稳定运行的同时逐步完成重构。
