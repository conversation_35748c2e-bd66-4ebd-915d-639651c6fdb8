import base64
import hashlib
import json
import os
import struct
import time
from typing import Tuple, Union, Dict

import requests
from Crypto.Cipher import AES
from lxml import etree

from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

from celery_task.WeChat.settings import EncodingAESKey, APP_ID, CUSTOMER_TOKEN
from custom.db.redis import redis_client

MAX_RETRIES = 3  # 最大重试次数
BACKOFF_FACTOR = 0.5  # 指数退避因子

TIMEOUT = (3.05, 27)  # 连接超时3.05s，读取超时27s
SSL_VERIFY = True  # 生产环境必须验证SSL证书

# 扩展字段配置
FIELD_CONFIG = {
    'Text': {
        'order': ['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Content'],  # 顺序
        'cdata': {'ToUserName', 'FromUserName', 'MsgType', 'Content'}  # 带CDATA的
    },
    'Image': {
        'order': ['ToUserName', 'FromUserName', 'CreateTime', 'MsgType', 'Image'],
        'cdata': {'ToUserName', 'FromUserName', 'MsgType'}
    },
    'Out': {
        'order': ['Encrypt', 'MsgSignature', 'TimeStamp', 'Nonce'],
        'cdata': {'Encrypt', 'MsgSignature', 'Nonce'}
    }
}


def wx_session(api_url, payload=None, headers=None, method: str = 'get', download_file=None) -> Tuple[
    bool, Union[str, bytes]]:
    session = requests.Session()
    retry = Retry(total=MAX_RETRIES,
                  backoff_factor=BACKOFF_FACTOR,
                  status_forcelist=[500, 502, 503, 504],
                  allowed_methods=['POST', 'GET'])
    adapter = HTTPAdapter(pool_connections=50, pool_maxsize=50, max_retries=retry)
    session.mount('https://', adapter)
    try:
        kwargs = {'url': api_url,
                  'verify': SSL_VERIFY,
                  'timeout': TIMEOUT,
                  'headers': headers}
        if download_file:
            kwargs['stream'] = True
        if payload and method == 'get':
            kwargs['params'] = payload
        if payload and method == 'post_json':
            kwargs['data'] = json.dumps(payload, ensure_ascii=False).encode('utf-8')

        response = session.get(**kwargs) if method == 'get' else session.post(**kwargs)
        response.raise_for_status()  # 自动触发HTTP错误异常

        content_type = response.headers.get('Content-Type', '')
        if 'application/json' in content_type:
            try:
                json_response = response.json()
                return True, json_response
            except json.JSONDecodeError:
                return False, '响应包含无效的JSON格式'
        elif 'image/jpg' in content_type:
            return True, response.content
        # elif 'text/html' in content_type:
        #     return {'success': True,
        #             'msg': response.text,
        #             'cookies': {cookie.name: cookie.value for cookie in common_session.cookies}}
        # elif 'application/octet-stream' in content_type and download_file:
        #     try:
        #         file_path = os.path.join(download_file['path'], download_file['name'])
        #         with open(file_path, 'wb') as f:
        #             for chunk in response.iter_content(chunk_size=8192):
        #                 if chunk:
        #                     f.write(chunk)
        #         return {'success': True,
        #                 'msg': f"文件已保存到：{file_path}",
        #                 'cookies': {cookie.name: cookie.value for cookie in common_session.cookies}}
        #     except Exception as e:
        #         return {'success': False, 'msg': f'录音文件下载失败: {str(e)}'}
        else:
            return False, f'未知响应类型: {content_type}'

    except requests.exceptions.SSLError as e:
        return False, 'SSL证书验证失败'
    except requests.exceptions.ConnectionError as e:
        return False, f"连接失败: {str(e)}"
    except requests.exceptions.Timeout as e:
        return False, f"请求超时: {str(e)}"
    except requests.exceptions.HTTPError as e:
        return False, f"HTTP错误: {str(e)}"
    except requests.exceptions.RequestException as e:
        return False, f"请求异常: {str(e)}"
    except Exception as e:
        return False, f"未知异常: {str(e)}"
    finally:
        session.close()


def verify_signature(params: list, targ: str) -> bool:
    """ 验证签名 """
    sorted_params = sorted(params)
    sha1 = hashlib.sha1(''.join(sorted_params).encode('utf-8')).hexdigest()
    return sha1 == targ


def _get_cipher():
    encoding_aes_key = EncodingAESKey
    if not encoding_aes_key:
        raise ValueError("未配置EncodingAESKey")
    aes_key = base64.b64decode(EncodingAESKey + "=")
    if len(aes_key) != 32:
        raise ValueError(f"无效的AESKey长度：{len(aes_key)}字节")
    return AES.new(aes_key, AES.MODE_CBC, iv=aes_key[:16])


def _generate_signature(token: str, timestamp: str, nonce: str, encrypt: str) -> str:
    """生成MsgSignature签名"""
    params = sorted([token, timestamp, nonce, encrypt])
    combined = ''.join(params)
    return hashlib.sha1(combined.encode()).hexdigest()


def decrypt_msg(encrypt_msg: str) -> dict:
    """
    微信消息解密方法
    :param encrypt_msg: 加密消息字符串
    :return: 解析后的消息字典
    """
    try:
        # ================== 步骤2：解密消息体 ==================
        # Base64解码加密消息
        try:
            encrypted_data = base64.b64decode(encrypt_msg)
        except Exception as e:
            raise ValueError(f"Base64解码失败: {str(e)}")

        # 执行解密并去除PKCS#7填充
        decrypted = _get_cipher().decrypt(encrypted_data)

        # PKCS#7去填充
        pad_len = decrypted[-1]
        if pad_len < 1 or pad_len > 32:  # AES-256的块大小是32字节
            raise ValueError("无效的PKCS#7填充长度")
        unpadded = decrypted[:-pad_len]

        # ================== 步骤3：解析消息结构 ==================
        # 分割消息结构 random(16B) + msg_len(4B) + msg + appid
        if len(unpadded) < 16 + 4 + 1 + 5:  # 最小长度校验
            raise ValueError("解密后数据长度异常")

        msg_len = struct.unpack(">I", unpadded[16:20])[0]  # 网络字节序转int
        msg_start = 20
        msg_end = msg_start + msg_len
        msg_content = unpadded[msg_start:msg_end].decode('utf-8')
        appid = unpadded[msg_end:].decode('utf-8')

        # ================== 步骤4：验证APPID ==================
        if appid != APP_ID:
            raise ValueError(f"APPID不匹配: 期望{APP_ID} 实际{appid}")

        # ================== 步骤5：解析消息内容 ==================
        try:
            # 根据实际消息类型选择解析方式（示例为JSON）
            return json.loads(msg_content)
        except json.JSONDecodeError as e:
            return {'raw_content': msg_content}

    except Exception as e:
        raise ValueError(f"错误: {str(e)}")


def encrypt_msg(msg_content: str, nonce: str, timestamp: int = None) -> dict:
    """
    微信消息加密方法
    :param msg_content: 明文消息内容（需符合接口格式要求）
    :param nonce: URL中的nonce
    :param timestamp: 时间戳（默认当前时间）
    :return: 加密后的消息字典
    """
    try:
        # ================== 配置校验 ==================
        if not all([CUSTOMER_TOKEN, EncodingAESKey, APP_ID]):
            raise ValueError("微信配置缺失，请检查WECHAT_TOKEN/WECHAT_ENCODING_AES_KEY/WECHAT_APPID")

        # ================== 构造FullStr ==================
        # 生成16字节随机数
        random_16b = os.urandom(16)

        # 构造消息体
        msg = msg_content.encode('utf-8')
        msg_len = struct.pack('>I', len(msg))  # 网络字节序

        appid_bytes = APP_ID.encode('utf-8')
        full_str = random_16b + msg_len + msg + appid_bytes

        # ================== AES加密 ==================
        # PKCS#7填充
        block_size = 32  # AES-256的块大小
        pad_len = block_size - (len(full_str) % block_size)
        padded = full_str + bytes([pad_len] * pad_len)

        # 初始化加密器（IV取AESKey前16字节）
        encrypted = _get_cipher().encrypt(padded)

        # Base64编码
        encrypt_str = base64.b64encode(encrypted).decode('utf-8')

        # ================== 生成签名 ==================
        timestamp = timestamp or int(time.time())
        sign_str = _generate_signature(CUSTOMER_TOKEN, str(timestamp), nonce, encrypt_str)

        # ================== 返回加密结构 ==================
        return {
            "Encrypt": encrypt_str,
            "MsgSignature": sign_str,
            "TimeStamp": timestamp,
            "Nonce": nonce
        }

    except Exception as e:
        raise ValueError(f"加密错误: {str(e)}")


def xml_to_json(xml_str: str) -> Dict[str, Union[str, int, float]]:
    """
    使用 lxml 加速的 XML 解析器 (支持 100MB+ 大文件)
    """
    # 使用 fromstring 直接解析字符串
    root = etree.fromstring(xml_str)
    result = {}

    for elem in root.iter():
        # 处理 CDATA
        if elem.text and isinstance(elem.text, etree.CDATA):
            value = elem.text
        else:
            value = elem.text.strip() if elem.text else ''

        # 类型转换
        if elem.tag == 'CreateTime':
            result[elem.tag] = int(value)
        else:
            result[elem.tag] = value

    return result


def json_to_xml(json_data: dict, parsing=True) -> str:
    """增强版转换器，支持更多微信消息类型"""
    # 根据消息类型选择配置
    msg_type = json_data.get('MsgType', 'text') if parsing else 'Out'
    config = FIELD_CONFIG.get(msg_type, FIELD_CONFIG['Text'])

    # 创建XML树
    root = etree.Element('xml')
    for field in config['order']:
        if field in json_data:
            elem = etree.SubElement(root, field)
            if field in config['cdata']:
                elem.text = etree.CDATA(str(json_data[field]))
            else:
                elem.text = str(json_data[field])

    # 生成带缩进的XML字符串
    xml_str = etree.tostring(
        root,
        encoding='unicode',
        pretty_print=True,  # 启用格式化
        xml_declaration=False,
        with_tail=False
    )

    # 微信规范要求：每个标签独占一行，无多余空行
    return '\n'.join(line.strip() for line in xml_str.split('\n') if line.strip())

def send_text(msg, open_id):
    """ 给用户发送文本消息 """
    wx_token = redis_client.get('wx_access_token')
    api_url = f"https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token={wx_token}"
    payload = {"touser": open_id,
               "msgtype": "text",
               "text": {"content": msg}}
    suc, result = wx_session(api_url, payload=payload, method='post_json')
    if not suc:
        raise ValueError(result)
