#!/bin/bash
# vim:sw=4:ts=4:et

set -e
USERNAME=${USERNAME:-celery}
USER_ID=${USER_ID:-1000}
GROUP_ID=${GROUP_ID:-1000}

# 容器初始化日志
log() {
    gosu ${USER_ID}:${GROUP_ID} echo "[Entrypoint] $(date '+%Y-%m-%d %T') - $@"
}

# 预处理操作
prepare() {
    # 创建用户
    log "创建用户 ${USERNAME}"
    adduser --disabled-password --gecos '' --uid ${USER_ID} ${USERNAME} || true
    # 修复挂载点权限
    log "检查挂载点权限"
    for target_dir in /app/celery /app/record; do
        find "$target_dir" -type d -exec chmod 775 {} +
        find "$target_dir" -type f -exec chmod 664 {} +
        chown -R ${USER_ID}:${GROUP_ID} "$target_dir" 2>/dev/null || true
    done
}

# 主流程
case "$1" in
    celery)
        prepare
        log "启动 Supervisord"
        exec supervisord -n -c /etc/supervisor/supervisord.conf
        ;;
    *)
        exec gosu ${USER_ID}:${GROUP_ID} "$@"
        ;;
esac
