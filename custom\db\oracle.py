# 连接Oracle数据库

import ast
from timeit import default_timer

import oracledb
from dbutils.pooled_db import PooledDB
from .mysql import UsingMysql


class DOracleConfig:
    def __init__(self, dsn, user, password):
        self.dsn = dsn  # 数据库ip地址
        self.user = user  # 用户名
        self.password = password  # 密码

        self.encoding = 'utf-8'  # 不能是 utf-8  # 字符编码
        self.minCached = 1  # 连接池中空闲连接的初始数量
        self.maxCached = 20  # 连接池中空闲连接的最大数量
        self.maxShared = 10  # 共享连接的最大数量
        self.maxConnection = 50  # 创建连接池的最大数量

        self.blocking = True  # 超过最大连接数量时候的表现，为True等待连接数量下降，为false直接报错处理
        self.maxUsage = 100  # 单个连接的最大重复使用次数
        self.setSession = None  # 可用于准备的SQL命令的可选列表会话，例如[“将日期样式设置为…”，“设置时区…”]
        self.reset = True  # 重置：当连接返回到池中时，应如何重置连接（False或None用于回滚以begin（）开始的事务，确实，为了安全起见，总是会发出回滚）


# ---- 用连接池来返回数据库连接
class DOraclePoolConn:
    __pool = None

    def __init__(self, config):
        if not self.__pool:
            try:
                self.__class__.__pool = PooledDB(
                    creator=oracledb,
                    maxconnections=config.maxConnection,
                    mincached=config.minCached,
                    maxcached=config.maxCached,
                    maxshared=config.maxShared,
                    blocking=config.blocking,
                    maxusage=config.maxUsage,
                    setsession=config.setSession,
                    # encoding=config.encoding,
                    dsn=config.dsn,
                    user=config.user,
                    password=config.password,
                )
            except oracledb.Error as e:
                print(f"连接池初始化失败: {str(e)}")
                raise

    def get_conn(self):
        return self.__pool.connection()


# ========== 在程序的开始初始化一个连接池（在程序运行周期内不管调用几次只会运行一次）
with UsingMysql(log_time=True) as um:
    his_str = um.fetch_decrypt("select value from info_db where index_information = 'his' ", None, 'value')
his_params = ast.literal_eval(his_str)
db_config = DOracleConfig(**his_params)
g_pool_connection = DOraclePoolConn(db_config)


class UsingOracle(object):  # 使用 with 的方式来优化代码

    def __init__(self, commit=True, log_time=False, log_label='oracle总用时'):
        """
        :param commit: 是否在最后提交事务(设置为False的时候方便单元测试)
        :param log_time:  是否打印程序运行总时间
        :param log_label:  自定义log的文字
        """
        self._log_time = log_time
        self._commit = commit
        self._log_label = log_label

    def __enter__(self):
        # 如果需要记录时间
        if self._log_time is True:
            self._start = default_timer()

        # 从连接池获取数据库连接
        conn = g_pool_connection.get_conn()
        cursor = conn.cursor()
        conn.autocommit = False

        self._conn = conn
        self._cursor = cursor
        return self

    def __exit__(self, *exc_info):
        # 提交事务
        if self._commit:
            self._conn.commit()
        # 在退出的时候自动关闭连接和cursor
        self._cursor.close()
        self._conn.close()

        if self._log_time is True:
            diff = default_timer() - self._start
            print('-- %s: %.6f 秒' % (self._log_label, diff))

    @property
    def cursor(self):
        return self._cursor

    @staticmethod
    def convert_to_str(value):
        if isinstance(value, oracledb.LOB):
            # 读取LOB对象内容并转换为字符串
            lob_data = value.read()
            return lob_data  # 根据实际情况选择合适的解码方式
        else:
            return value  # 如果不是LOB对象，直接返回原值

    # ========= 一系列封装的业务方法
    def fetch_all(self, sql):
        self.cursor.execute(sql)
        header = [desc[0] for desc in self.cursor.description]  # 获取列名
        content = self.cursor.fetchall()  # 获取数据
        content_str = [[self.convert_to_str(col) for col in row] for row in content]  # 将LOB对象转换为字符串
        return header, content_str  # 返回列名和数据

    def execute(self, sql):
        self.cursor.execute(sql)
        return self.cursor.rowcount
