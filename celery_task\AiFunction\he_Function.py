import json
import time

import pandas as pd
import requests
import re
from pandas import DataFrame
from custom.db.execute import db
from celery_task.celery import app
from typing import List, Dict, Tuple


def check_table():
    """
    主任务函数：提取所有类型癌症的”首次非手术治疗时间”并更新数据库
    """
    try:
        sql = f"""SELECT ID,TEXT FROM ai.test_no_oper"""
        records_df = db(None, None, sql, 'mysql')
        print(f"找到{len(records_df)}条需要处理的记录")
        for _, row in records_df.iterrows():
            time_results = extract_content(row)
            # update_CONTENT(time_results)
        batch_size = 5

        # for start in range(0, len(records_df), batch_size):
        #     batch_df = records_df.iloc[start:start + batch_size]
        #     time_results = extract_CONTENT(batch_df)
        #     update_CONTENT(batch_df, time_results)
    except Exception as e:
        print(f"处理过程中发生错误: {str(e)}")
        raise


def extract_content(records_df: pd.Series) -> str:
    """
    使用AI提取TEXT列中的首次非手术治疗时间
    返回: 时间列表，格式为 ["2023-12-30", "NULL", ...]
    """
    # 修正：使用正确的列名TEXT
    # prompt = f"""
    # 1. 文本中的格式为“时间：治疗”，判断TEXT列中每个“时间：治疗”是否包含“放疗、化疗、靶向治疗、内分泌治疗、免疫治疗”
    # 2. 若包含，返回最早的时间；若不包含，返回NULL
    # 3. 每行返回一个结果，用;分隔
    # 输出举例：2023-12-30 09:15:00;2023-12-30 09:15:00;NULL;2023-12-30 09:15:00;NULL
    #
    # {records_df[["TEXT"]].to_json(force_ascii=False)}
    # """
    prompt = f"""
    你是一名医生，请严格按以下规则获取"医嘱"中的首次特定治疗对应的时间：
    1.特定治疗指代：放疗、化疗、靶向治疗、内分泌治疗、免疫治疗
    2.要求输出特定治疗对应的最早时间
        3 未找到特定治疗时，结果输出为1970-01-01 00:00:00
        5. 最终严格按照下面输出要求返回。
    以下是医嘱内容：
     [{records_df["TEXT"]}]
     以下是输出要求：
     1、输出只允许为一段时间文本，除了时间文本不要有任何文字其他符号如*及说明，切记切记。
     输出举例：2024-01-25 15:06:00
        """
    # 最大重试次数
    MAX_RETRIES = 3

    retry_count = 0
    clean_response = ""

    pattern = r'^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$'
    while retry_count < MAX_RETRIES:
        try:
            response = requests.post(
                "http://***************:12434/api/generate",
                json={
                    "model": 'qwen3:8b-q4_K_M',
                    "prompt": prompt.translate(str.maketrans('', '', '\t\n\r\x0b\x0c')),
                    "stream": False,
                },
                timeout=120  # 添加超时防止卡死
            )
            response.raise_for_status()

            # 处理AI响应
            ai_output = response.json().get("response", "").strip()
            clean_response = re.sub(r'<think>.*?</think>', '', ai_output, flags=re.DOTALL).strip()

            if re.match(pattern, clean_response):
                break  # 格式正确则退出循环

            print(f"格式校验失败，重试中... (响应: '{clean_response}')")
            retry_count += 1
            time.sleep(1)  # 避免频繁请求
        except requests.exceptions.RequestException as e:
            print(f"AI服务调用失败: {str(e)}")
            retry_count += 1
            time.sleep(2)  # 网络错误时延长等待

    # 最终校验
    if not re.match(pattern, clean_response):
        print(f"超过最大重试次数，返回默认值. 最终响应: '{clean_response}'")
        clean_response = 'NULL'
    # 标准化结果
    return clean_response


def update_CONTENT(records_df: DataFrame, results: List[str]):
    """
    使用参数化查询更新数据库
    """
    if not records_df.empty and results:
        # 构建参数列表
        params = []
        for i, row in records_df.iterrows():
            time_value = results[i]
            # 处理NULL值（数据库NULL而非字符串'NULL'）
            params.append((row['ID'], None if time_value == 'NULL' else time_value))

        sql = """
              INSERT INTO ai.test_no_oper (ID, CONTENT)
              VALUES (%s, %s)
              ON DUPLICATE KEY \
                  UPDATE CONTENT = \
                             VALUES(CONTENT) \
              """

        try:
            db(None, None, sql, 'mysql', params=params)
            print(f"成功更新{len(params)}条记录")
        except Exception as e:
            print(f"更新数据库失败: {str(e)}")
            raise


# 定时任务入口
@app.task
def start():
    check_table()


if __name__ == "__main__":
    start()
